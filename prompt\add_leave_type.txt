I want to enhance the attendance/absence grid system by integrating employee leave data and improving the display logic. Here are the specific requirements:

**1. Leave Data Integration:**
- Add leave information to the attendance grid using data from the HR_H_Leave table
- Use this SQL query as the data source:
```sql
SELECT TOP (1000)
    leave_table.[Periode],
    leave_table.[EmployeeID],
    employee_table.[EmployeeName],
    leave_table.[LeaveTypeCode],
    leave_table.[LeaveYear],
    leave_table.[RefNumber],
    leave_table.[RefDate],
    leave_table.[BusCode],
    leave_table.[Incoming],
    leave_table.[Outgoing]
FROM [VenusHR14].[dbo].[HR_H_Leave] AS leave_table
INNER JOIN [VenusHR14].[dbo].[HR_M_EmployeePI] AS employee_table
ON leave_table.EmployeeID = employee_table.EmployeeID
WHERE YEAR(leave_table.[RefDate]) = 2025 AND MONTH(leave_table.[RefDate]) = 6
```

**2. Leave Type Codes and Descriptions:**
Map these leave type codes to their descriptions:
- CT = CUTI (Leave)
- H2 = HAMIL/MELAHIRKAN (Maternity Leave)
- P1 = KELUARGA MENINGGAL (Family Bereavement)
- P2 = IZIN MENIKAHKAN ATAU KHITANAN (Wedding/Circumcision Permission)
- P3 = CUTI MENIKAH (Marriage Leave)

**3. Grid Display Logic:**
- For each employee (EmployeeID), check the RefDate and display the corresponding LeaveTypeCode
- Add leave information to both the main attendance grid and staging data
- Include leave data when copying attendance records to staging data
- Ensure leave information appears in the staging data API responses

**4. Absence Marking (ALFA):**
- Mark cells RED with text "ALFA" when:
  - Employee has no attendance record from HR_T_TAMachine_Summary (missing Check In OR Check Out)
  - AND employee has no LeaveTypeCode for that date
- This applies to regular working days only

**5. Weekend and Holiday Logic:**
- For Sundays and national holidays:
  - Display "OFF" if employee has no overtime hours
  - If employee has overtime hours, show only overtime (no regular hours)
  - Remove all (-) symbols except for Sundays and national holidays

**6. Implementation Requirements:**
- Update the attendance grid display logic
- Modify the staging data transfer process to include leave information
- Update the staging data API endpoints to return leave data
- Ensure proper color coding (red for ALFA status)
- Integrate with the existing national holidays system

Please implement these changes to both the frontend grid display and backend API endpoints, ensuring that leave data is properly integrated throughout the attendance tracking system.