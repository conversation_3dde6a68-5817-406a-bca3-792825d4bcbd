<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Error - Attendance Report System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .fallback-container {
            max-width: 600px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fallback-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .fallback-body {
            padding: 2rem;
        }
        .connection-status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 5px;
        }
        .status-connected {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .status-disconnected {
            background-color: #f8d7da;
            color: #842029;
        }
        .status-testing {
            background-color: #fff3cd;
            color: #664d03;
        }
        .btn-retry {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-retry:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .btn-config {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-config:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body>
    <div class="fallback-container">
        <div class="fallback-header">
            <h1><i class="fas fa-exclamation-triangle me-3"></i>Database Connection Error</h1>
            <p class="mb-0">Unable to connect to attendance database</p>
        </div>
        
        <div class="fallback-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-database me-2"></i>Connection Status</h5>
                <p class="mb-0">Both local and remote database connections are currently unavailable. Please check your database configuration or contact your system administrator.</p>
            </div>

            <div class="connection-status">
                <h6><i class="fas fa-network-wired me-2"></i>Database Servers</h6>
                
                <div class="status-item status-disconnected" id="localStatus">
                    <div>
                        <strong>Local Database</strong>
                        <br><small class="text-muted">localhost:1433</small>
                    </div>
                    <div>
                        <i class="fas fa-times-circle"></i>
                        <span class="ms-2">Disconnected</span>
                    </div>
                </div>
                
                <div class="status-item status-disconnected" id="remoteStatus">
                    <div>
                        <strong>Remote Database</strong>
                        <br><small class="text-muted">********:1888</small>
                    </div>
                    <div>
                        <i class="fas fa-times-circle"></i>
                        <span class="ms-2">Disconnected</span>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 mt-4">
                <button type="button" class="btn btn-retry" id="retryConnectionBtn">
                    <i class="fas fa-sync me-2"></i>
                    Retry Connection
                </button>
                
                <button type="button" class="btn btn-config" id="openConfigBtn">
                    <i class="fas fa-cog me-2"></i>
                    Configure Database Settings
                </button>
                
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-2"></i>
                    Return to Main Page
                </a>
            </div>

            <div class="loading-spinner text-center mt-3" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Testing connections...</span>
                </div>
                <p class="mt-2 text-muted">Testing database connections...</p>
            </div>
        </div>
    </div>

    <!-- Database Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-database me-2"></i>
                        Database Configuration
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-home me-2"></i>Local Database</h6>
                            <form id="localConfigForm">
                                <div class="mb-3">
                                    <label class="form-label">Server</label>
                                    <input type="text" class="form-control" id="localServer" value="localhost">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Port</label>
                                    <input type="number" class="form-control" id="localPort" value="1433">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" id="localUsername" value="sa">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="localPassword">
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testLocalBtn">
                                    <i class="fas fa-flask me-1"></i>Test Connection
                                </button>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <h6><i class="fas fa-cloud me-2"></i>Remote Database</h6>
                            <form id="remoteConfigForm">
                                <div class="mb-3">
                                    <label class="form-label">Server IP</label>
                                    <input type="text" class="form-control" id="remoteServer" value="********">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Port</label>
                                    <input type="number" class="form-control" id="remotePort" value="1888">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" id="remoteUsername" value="sa">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="remotePassword" value="supp0rt@">
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testRemoteBtn">
                                    <i class="fas fa-flask me-1"></i>Test Connection
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveConfigBtn">
                        <i class="fas fa-save me-1"></i>Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Database connection management
        let configModal;
        
        $(document).ready(function() {
            configModal = new bootstrap.Modal(document.getElementById('configModal'));
            loadDatabaseStatus();
            
            $('#retryConnectionBtn').click(retryConnections);
            $('#openConfigBtn').click(() => configModal.show());
            $('#testLocalBtn').click(() => testConnection('local'));
            $('#testRemoteBtn').click(() => testConnection('remote'));
            $('#saveConfigBtn').click(saveConfiguration);
        });
        
        function loadDatabaseStatus() {
            $.get('/api/database/status')
                .done(function(response) {
                    if (response.success) {
                        updateConnectionStatus(response.status);
                    }
                })
                .fail(function() {
                    console.error('Failed to load database status');
                });
        }
        
        function updateConnectionStatus(status) {
            // Update local status
            const localElement = $('#localStatus');
            if (status.local_status.connected) {
                localElement.removeClass('status-disconnected').addClass('status-connected');
                localElement.find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
                localElement.find('span').text('Connected');
            } else {
                localElement.removeClass('status-connected').addClass('status-disconnected');
                localElement.find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
                localElement.find('span').text('Disconnected');
            }
            
            // Update remote status
            const remoteElement = $('#remoteStatus');
            if (status.remote_status.connected) {
                remoteElement.removeClass('status-disconnected').addClass('status-connected');
                remoteElement.find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
                remoteElement.find('span').text('Connected');
            } else {
                remoteElement.removeClass('status-connected').addClass('status-disconnected');
                remoteElement.find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
                remoteElement.find('span').text('Disconnected');
            }
            
            // If any connection is successful, redirect to main page
            if (status.local_status.connected || status.remote_status.connected) {
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            }
        }
        
        function retryConnections() {
            $('#loadingSpinner').show();
            $('#retryConnectionBtn').prop('disabled', true);
            
            // Test both connections
            Promise.all([
                testConnection('local', false),
                testConnection('remote', false)
            ]).finally(() => {
                $('#loadingSpinner').hide();
                $('#retryConnectionBtn').prop('disabled', false);
                loadDatabaseStatus();
            });
        }
        
        function testConnection(mode, showAlert = true) {
            return $.post('/api/database/test-connection', {
                mode: mode
            }).done(function(response) {
                if (showAlert) {
                    if (response.success) {
                        alert(`${mode.charAt(0).toUpperCase() + mode.slice(1)} database connection successful!`);
                    } else {
                        alert(`${mode.charAt(0).toUpperCase() + mode.slice(1)} database connection failed: ${response.message}`);
                    }
                }
                updateConnectionStatus(response.status);
            }).fail(function() {
                if (showAlert) {
                    alert(`Failed to test ${mode} database connection`);
                }
            });
        }
        
        function saveConfiguration() {
            const config = {
                mode: 'remote', // Default to remote for configuration
                database_config: {
                    local_database: {
                        server: $('#localServer').val(),
                        port: parseInt($('#localPort').val()),
                        username: $('#localUsername').val(),
                        password: $('#localPassword').val()
                    },
                    remote_database: {
                        server: $('#remoteServer').val(),
                        port: parseInt($('#remotePort').val()),
                        username: $('#remoteUsername').val(),
                        password: $('#remotePassword').val()
                    }
                }
            };
            
            $.post('/api/database/update-config', config)
                .done(function(response) {
                    if (response.success) {
                        alert('Configuration saved successfully!');
                        configModal.hide();
                        loadDatabaseStatus();
                    } else {
                        alert('Failed to save configuration: ' + response.error);
                    }
                })
                .fail(function() {
                    alert('Failed to save configuration');
                });
        }
    </script>
</body>
</html> 