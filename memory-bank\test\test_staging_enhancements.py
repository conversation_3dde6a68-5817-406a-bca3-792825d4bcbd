#!/usr/bin/env python3
"""
Test script for enhanced staging functionality:
- Grid layout standardization
- Data filtering for successfully transferred records
- Log history implementation
- Read/write operation logging
"""

import requests
import json
import sys
from datetime import datetime, timedelta

# Test configuration
FLASK_APP_URL = "http://localhost:5173"

def test_enhanced_staging_grid():
    """Test the enhanced staging grid with transfer status indicators."""
    print("=" * 60)
    print("TESTING ENHANCED STAGING GRID")
    print("=" * 60)
    
    try:
        # Test staging data retrieval with filtering
        response = requests.get(f"{FLASK_APP_URL}/api/staging/data", params={
            'status': 'staged',  # Only successfully transferred records
            'limit': 50
        })
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Enhanced staging grid data loaded successfully")
            print(f"   Total records: {data.get('total_records', 0)}")
            print(f"   Successfully transferred: {data.get('successfully_transferred', 0)}")
            print(f"   Returned records: {data.get('returned_records', 0)}")
            
            # Check for transfer status indicators
            if data.get('data'):
                sample_record = data['data'][0]
                if 'transfer_status' in sample_record:
                    print(f"   ✅ Transfer status indicator present: {sample_record['transfer_status']}")
                else:
                    print(f"   ❌ Transfer status indicator missing")
            
            return True
        else:
            print(f"❌ Failed to load staging data: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing enhanced staging grid: {e}")
        return False

def test_staging_operation_logging():
    """Test staging operations logging functionality."""
    print("\n" + "=" * 60)
    print("TESTING STAGING OPERATION LOGGING")
    print("=" * 60)
    
    try:
        # Test log retrieval
        response = requests.get(f"{FLASK_APP_URL}/api/staging/logs", params={
            'limit': 20,
            'result_status': 'success'
        })
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Staging operation logs loaded successfully")
            print(f"   Total logs: {data.get('total_logs', 0)}")
            print(f"   Returned logs: {data.get('returned_logs', 0)}")
            
            # Check log structure
            if data.get('logs'):
                sample_log = data['logs'][0]
                required_fields = ['timestamp', 'operation_type', 'table_name', 'result_status']
                missing_fields = [field for field in required_fields if field not in sample_log]
                
                if not missing_fields:
                    print(f"   ✅ Log structure complete")
                    print(f"   Sample operation: {sample_log.get('operation_type')} on {sample_log.get('table_name')}")
                else:
                    print(f"   ❌ Missing log fields: {missing_fields}")
            
            # Check operation statistics
            if data.get('operation_statistics'):
                stats = data['operation_statistics']
                print(f"   ✅ Operation statistics available: {len(stats)} operation types")
                for op_type, op_stats in stats.items():
                    success_rate = (op_stats.get('success', 0) / op_stats.get('total', 1)) * 100
                    print(f"     {op_type}: {op_stats.get('total', 0)} total, {success_rate:.1f}% success")
            
            return True
        else:
            print(f"❌ Failed to load staging logs: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing staging operation logging: {e}")
        return False

def test_read_operation_logging():
    """Test read operation logging by making multiple data requests."""
    print("\n" + "=" * 60)
    print("TESTING READ OPERATION LOGGING")
    print("=" * 60)
    
    try:
        # Perform multiple read operations to generate logs
        operations = [
            {'params': {'status': 'staged', 'limit': 10}, 'desc': 'staged records'},
            {'params': {'status': 'ready_for_upload', 'limit': 5}, 'desc': 'ready records'},
            {'params': {'employee_id': 'PTRJ.241000001', 'limit': 20}, 'desc': 'specific employee'}
        ]
        
        for operation in operations:
            response = requests.get(f"{FLASK_APP_URL}/api/staging/data", params=operation['params'])
            print(f"   Read operation: {operation['desc']} - Status: {response.status_code}")
        
        # Wait a moment for logging to complete
        import time
        time.sleep(1)
        
        # Check if read operations were logged
        log_response = requests.get(f"{FLASK_APP_URL}/api/staging/logs", params={
            'operation_type': 'READ',
            'limit': 10
        })
        
        if log_response.status_code == 200:
            log_data = log_response.json()
            read_logs = [log for log in log_data.get('logs', []) if log.get('operation_type') == 'READ']
            
            if len(read_logs) >= len(operations):
                print(f"   ✅ Read operations logged successfully: {len(read_logs)} entries found")
                
                # Check log details
                sample_log = read_logs[0]
                if sample_log.get('query_parameters'):
                    print(f"   ✅ Query parameters logged")
                if sample_log.get('data_volume', 0) >= 0:
                    print(f"   ✅ Data volume tracked: {sample_log.get('data_volume')} records")
                
                return True
            else:
                print(f"   ❌ Insufficient read logs found: {len(read_logs)} < {len(operations)}")
                return False
        else:
            print(f"   ❌ Failed to retrieve read operation logs")
            return False
            
    except Exception as e:
        print(f"❌ Error testing read operation logging: {e}")
        return False

def test_write_operation_logging():
    """Test write operation logging by adding test records."""
    print("\n" + "=" * 60)
    print("TESTING WRITE OPERATION LOGGING")
    print("=" * 60)
    
    try:
        # Create test records for staging
        test_records = [
            {
                'employee_id': 'TEST001',
                'employee_name': 'Test Employee 1',
                'date': '2025-01-15',
                'day_of_week': 'Wednesday',
                'shift': 'Regular',
                'check_in': '08:00',
                'check_out': '17:00',
                'regular_hours': 8.0,
                'overtime_hours': 1.0,
                'task_code': 'TEST_TASK',
                'station_code': 'TEST_STATION',
                'machine_code': 'TEST_MACHINE',
                'expense_code': 'TEST_EXPENSE',
                'notes': 'Test record for logging verification'
            },
            {
                'employee_id': 'TEST002',
                'employee_name': 'Test Employee 2',
                'date': '2025-01-16',
                'day_of_week': 'Thursday',
                'shift': 'Regular',
                'check_in': '08:00',
                'check_out': '16:00',
                'regular_hours': 7.0,
                'overtime_hours': 0.0,
                'task_code': 'TEST_TASK2',
                'station_code': 'TEST_STATION2',
                'machine_code': 'TEST_MACHINE2',
                'expense_code': 'TEST_EXPENSE2',
                'notes': 'Second test record for logging verification'
            }
        ]
        
        # Add test records
        response = requests.post(f"{FLASK_APP_URL}/api/staging/data", 
                               json={'records': test_records},
                               headers={'Content-Type': 'application/json'})
        
        print(f"Write operation status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Test records added: {result.get('added_records', 0)}")
            
            # Wait for logging to complete
            import time
            time.sleep(1)
            
            # Check if write operation was logged
            log_response = requests.get(f"{FLASK_APP_URL}/api/staging/logs", params={
                'operation_type': 'BULK_INSERT',
                'limit': 5
            })
            
            if log_response.status_code == 200:
                log_data = log_response.json()
                write_logs = [log for log in log_data.get('logs', []) if log.get('operation_type') == 'BULK_INSERT']
                
                if write_logs:
                    latest_log = write_logs[0]
                    print(f"   ✅ Write operation logged successfully")
                    print(f"   Operation details: {latest_log.get('operation_details', 'N/A')}")
                    print(f"   Data volume: {latest_log.get('data_volume', 0)} records")
                    print(f"   Result status: {latest_log.get('result_status', 'N/A')}")
                    
                    return True
                else:
                    print(f"   ❌ Write operation not found in logs")
                    return False
            else:
                print(f"   ❌ Failed to retrieve write operation logs")
                return False
        else:
            print(f"   ❌ Failed to add test records: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing write operation logging: {e}")
        return False

def test_log_filtering_and_statistics():
    """Test log filtering and statistics functionality."""
    print("\n" + "=" * 60)
    print("TESTING LOG FILTERING AND STATISTICS")
    print("=" * 60)
    
    try:
        # Test filtering by operation type
        operations_to_test = ['READ', 'BULK_INSERT']
        
        for op_type in operations_to_test:
            response = requests.get(f"{FLASK_APP_URL}/api/staging/logs", params={
                'operation_type': op_type,
                'limit': 10
            })
            
            if response.status_code == 200:
                data = response.json()
                filtered_logs = data.get('logs', [])
                print(f"   ✅ {op_type} filter working: {len(filtered_logs)} logs found")
                
                # Verify all logs match the filter
                mismatched = [log for log in filtered_logs if log.get('operation_type') != op_type]
                if not mismatched:
                    print(f"   ✅ All {op_type} logs match filter criteria")
                else:
                    print(f"   ❌ {len(mismatched)} logs don't match {op_type} filter")
            else:
                print(f"   ❌ Failed to filter {op_type} logs")
        
        # Test filtering by result status
        response = requests.get(f"{FLASK_APP_URL}/api/staging/logs", params={
            'result_status': 'success',
            'limit': 20
        })
        
        if response.status_code == 200:
            data = response.json()
            success_logs = data.get('logs', [])
            print(f"   ✅ Success status filter working: {len(success_logs)} logs found")
            
            # Test statistics
            stats = data.get('operation_statistics', {})
            if stats:
                print(f"   ✅ Operation statistics available: {len(stats)} operation types")
                
                total_operations = sum(op_stats.get('total', 0) for op_stats in stats.values())
                total_success = sum(op_stats.get('success', 0) for op_stats in stats.values())
                
                if total_operations > 0:
                    overall_success_rate = (total_success / total_operations) * 100
                    print(f"   Overall success rate: {overall_success_rate:.1f}%")
                
                return True
            else:
                print(f"   ❌ No operation statistics available")
                return False
        else:
            print(f"   ❌ Failed to filter success logs")
            return False
            
    except Exception as e:
        print(f"❌ Error testing log filtering and statistics: {e}")
        return False

def run_enhanced_staging_tests():
    """Run all enhanced staging functionality tests."""
    print("ENHANCED STAGING FUNCTIONALITY TEST SUITE")
    print("=" * 60)
    print(f"Testing Flask app at: {FLASK_APP_URL}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("Enhanced Staging Grid", test_enhanced_staging_grid),
        ("Staging Operation Logging", test_staging_operation_logging),
        ("Read Operation Logging", test_read_operation_logging),
        ("Write Operation Logging", test_write_operation_logging),
        ("Log Filtering and Statistics", test_log_filtering_and_statistics)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("ENHANCED STAGING TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\nOverall Test Success Rate: {passed}/{len(results)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 All enhanced staging functionality tests passed!")
    elif success_rate >= 80:
        print("⚠️  Most enhanced staging functionality tests passed")
    else:
        print("❌ Multiple enhanced staging functionality tests failed")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = run_enhanced_staging_tests()
    sys.exit(0 if success else 1) 