"""
Database Manager for Attendance Report System.
Handles database connections, connection management, and health monitoring.
Implements Dependency Inversion and Single Responsibility Principles.
"""

import os
import time
import sqlite3
import pyodbc
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime

from interfaces.database_interfaces import IConnectionManager, IDatabaseConnection, IStagingRepository


class DatabaseConnectionManager(IDatabaseConnection):
    """
    Manages database connections with toggle functionality and fallback systems.
    Supports both local and remote SQL Server connections with automatic failover.
    """
    
    def __init__(self, config_manager, logging_manager):
        """
        Initialize database connection manager.
        
        Args:
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)
        
        # Get database configuration
        db_config = config_manager.get_database_config()
        self.connection_mode = db_config.get("connection_mode", "local")
        self.fallback_enabled = db_config.get("fallback_enabled", True)
        self.connection_timeout = db_config.get("connection_timeout", 30)
        
        # Connection status tracking
        self.connection_status = {
            "local": {"connected": False, "last_attempt": None, "error": None},
            "remote": {"connected": False, "last_attempt": None, "error": None}
        }
        
        self.current_connection = None
    
    def _build_connection_string(self, mode: str = "local", config: Optional[Dict] = None) -> Optional[str]:
        """Build connection string for specified mode with optional custom config."""
        try:
            db_config = self.config_manager.get_database_config()
            
            if mode == "local":
                local_config = db_config.get("local_database", {})
                server = local_config.get("server", "localhost")
                port = local_config.get("port", 1433)
                database = local_config.get("database", "VenusHR14")
                username = local_config.get("username", "sa")
                password = local_config.get("password", "windows0819")
                driver = local_config.get("driver", "ODBC Driver 17 for SQL Server")
                
            elif mode == "remote":
                if config:
                    remote_config = config
                else:
                    remote_config = db_config.get("remote_database", {})
                
                if not remote_config or not remote_config.get("server"):
                    self.logger.warning("Remote database configuration not available")
                    return None
                
                server = remote_config.get("server")
                port = remote_config.get("port", 1888)
                database = remote_config.get("database", "VenusHR14")
                username = remote_config.get("username", "sa")
                password = remote_config.get("password")
                driver = remote_config.get("driver", "ODBC Driver 17 for SQL Server")
                
                if not password:
                    self.logger.warning("Remote database password not configured")
                    return None
            else:
                self.logger.error(f"Unknown database mode: {mode}")
                return None
            
            connection_string = (
                f"DRIVER={{{driver}}};"
                f"SERVER={server},{port};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"TrustServerCertificate=yes;"
                f"Encrypt=yes;"
            )
            
            self.logger.debug(f"Built connection string for {mode} mode")
            return connection_string
            
        except Exception as e:
            self.logger.error(f"Error building connection string for {mode}: {e}")
            return None
    
    def test_connection(self, mode: str = "local", skip_during_init: bool = False, 
                       config: Optional[Dict] = None) -> Tuple[bool, str]:
        """Test database connection for specified mode."""
        try:
            # Skip testing during initialization for faster startup
            if skip_during_init:
                self.logger.info(f"⏩ Skipping {mode} database connection test during initialization")
                self.connection_status[mode]["connected"] = None  # Unknown status
                self.connection_status[mode]["error"] = "Not tested during startup (fast boot mode)"
                self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
                return True, "Connection test skipped for fast startup"
            
            # Use appropriate timeout for connections
            connection_timeout = 10 if mode == "remote" else 5
            
            connection_string = self._build_connection_string(mode, config)
            if not connection_string:
                error_msg = f"Failed to build connection string for {mode} mode"
                self.connection_status[mode]["connected"] = False
                self.connection_status[mode]["error"] = error_msg
                self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
                return False, error_msg
            
            self.logger.info(f"🧪 Testing {mode} database connection (timeout: {connection_timeout}s)...")
            
            # Test connection with timeout
            start_time = time.time()
            conn = pyodbc.connect(connection_string, timeout=connection_timeout)
            conn.close()
            test_duration = time.time() - start_time
            
            # Update status on success
            self.connection_status[mode]["connected"] = True
            self.connection_status[mode]["error"] = None
            self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
            
            self.logger.info(f"✅ {mode.title()} database connection successful ({test_duration:.2f}s)")
            return True, f"Connection successful in {test_duration:.2f} seconds"
            
        except Exception as e:
            error_msg = f"Database connection failed: {str(e)}"
            self.connection_status[mode]["connected"] = False
            self.connection_status[mode]["error"] = error_msg
            self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
            
            self.logger.warning(f"❌ {mode.title()} database connection failed: {error_msg}")
            return False, error_msg
    
    def get_connection(self, force_mode: Optional[str] = None):
        """
        Get database connection with automatic fallback logic.
        
        Args:
            force_mode: Force specific connection mode ("local" or "remote")
            
        Returns:
            pyodbc.Connection or None
        """
        mode_to_try = force_mode or self.connection_mode
        
        try:
            # Primary connection attempt
            success, error = self.test_connection(mode_to_try)
            if success:
                connection_string = self._build_connection_string(mode_to_try)
                conn = pyodbc.connect(connection_string)
                self.logger.info(f"✅ Connected to {mode_to_try} database")
                return conn
            
            # Fallback logic if primary connection fails
            if self.fallback_enabled and not force_mode:
                fallback_mode = "remote" if mode_to_try == "local" else "local"
                self.logger.warning(f"Primary connection ({mode_to_try}) failed, trying fallback ({fallback_mode})")
                
                success, error = self.test_connection(fallback_mode)
                if success:
                    connection_string = self._build_connection_string(fallback_mode)
                    conn = pyodbc.connect(connection_string)
                    self.logger.info(f"✅ Connected to {fallback_mode} database (fallback)")
                    # Update current mode to successful fallback
                    self.connection_mode = fallback_mode
                    return conn
                else:
                    self.logger.error(f"❌ Fallback connection ({fallback_mode}) also failed: {error}")
            
            # Both connections failed or fallback disabled
            self.logger.error(f"❌ Database connection failed: {error}")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Unexpected error in get_connection: {str(e)}")
            return None
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a query and return results as list of dictionaries."""
        conn = None
        try:
            conn = self.get_connection()
            if not conn:
                raise Exception("No database connection available")
            
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            # Get column names
            columns = [column[0] for column in cursor.description]
            
            # Fetch all rows and convert to dictionaries
            rows = cursor.fetchall()
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Query execution failed: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_non_query(self, query: str, params: tuple = ()) -> int:
        """Execute a non-query (INSERT, UPDATE, DELETE) and return affected rows."""
        conn = None
        try:
            conn = self.get_connection()
            if not conn:
                raise Exception("No database connection available")
            
            cursor = conn.cursor()
            cursor.execute(query, params)
            affected_rows = cursor.rowcount
            conn.commit()
            
            return affected_rows
            
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Non-query execution failed: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status for both modes."""
        return {
            "current_mode": self.connection_mode,
            "fallback_enabled": self.fallback_enabled,
            "local_status": self.connection_status["local"],
            "remote_status": self.connection_status["remote"],
            "last_updated": datetime.now().isoformat()
        }
    
    def switch_connection_mode(self, new_mode: str) -> Tuple[bool, str]:
        """Switch connection mode and test new connection."""
        if new_mode not in ["local", "remote"]:
            return False, "Invalid connection mode"

        success, error = self.test_connection(new_mode)
        if success:
            self.connection_mode = new_mode
            self.logger.info(f"✅ Switched to {new_mode} database mode")
            return True, f"Successfully switched to {new_mode} database"
        else:
            return False, f"Failed to switch to {new_mode} database: {error}"


class StagingDatabaseManager(IStagingRepository):
    """
    Manages staging database operations with SQLite.
    Implements Single Responsibility Principle for staging operations.
    """

    def __init__(self, config_manager, logging_manager):
        """
        Initialize staging database manager.

        Args:
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)
        self.staging_db_path = os.path.join('data', 'staging_attendance.db')

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

    def get_connection(self) -> sqlite3.Connection:
        """Get a connection to the staging database."""
        return sqlite3.connect(self.staging_db_path)

    def initialize_database(self) -> bool:
        """Initialize the staging database table if it doesn't exist."""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Create staging table with comprehensive structure
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS staging_attendance (
                    id TEXT PRIMARY KEY,
                    employee_id TEXT NOT NULL,
                    employee_name TEXT NOT NULL,
                    date TEXT NOT NULL,
                    day_of_week TEXT,
                    shift TEXT,
                    check_in TEXT,
                    check_out TEXT,
                    regular_hours REAL DEFAULT 0,
                    overtime_hours REAL DEFAULT 0,
                    total_hours REAL DEFAULT 0,
                    task_code TEXT,
                    station_code TEXT,
                    machine_code TEXT,
                    expense_code TEXT,
                    raw_charge_job TEXT,
                    status TEXT DEFAULT 'staged',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    source_record_id TEXT,
                    notes TEXT,
                    leave_type_code TEXT,
                    leave_type_description TEXT,
                    leave_ref_number TEXT,
                    is_alfa BOOLEAN DEFAULT 0,
                    is_on_leave BOOLEAN DEFAULT 0,
                    ptrj_employee_id TEXT DEFAULT "N/A"
                )
            ''')

            # Create staging operations log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS staging_operations_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    operation_type TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    operation_details TEXT,
                    affected_record_ids TEXT,
                    data_volume INTEGER DEFAULT 0,
                    user_identifier TEXT DEFAULT 'system',
                    result_status TEXT NOT NULL,
                    error_details TEXT,
                    query_parameters TEXT,
                    ip_address TEXT,
                    user_agent TEXT
                )
            ''')

            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_employee_id ON staging_attendance(employee_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_date ON staging_attendance(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_status ON staging_attendance(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_created_at ON staging_attendance(created_at)')

            # Create unique constraint to prevent duplicate records
            try:
                cursor.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_staging_unique_employee_date ON staging_attendance(employee_id, date)')
                self.logger.info("Created unique constraint on employee_id + date to prevent duplicates")
            except Exception as e:
                self.logger.warning(f"Could not create unique constraint (may already exist): {e}")

            # Create indexes for log table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_timestamp ON staging_operations_log(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_operation_type ON staging_operations_log(operation_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_result_status ON staging_operations_log(result_status)')

            conn.commit()
            conn.close()

            self.logger.info("Staging database initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize staging database: {e}")
            return False

    def get_staging_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get staging data with filters."""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Build query with filters
            query = '''
                SELECT id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                       check_in, check_out, regular_hours, overtime_hours, total_hours,
                       task_code, station_code, machine_code, expense_code, raw_charge_job,
                       leave_type_code, leave_type_description, leave_ref_number,
                       is_alfa, is_on_leave, status,
                       created_at, updated_at, source_record_id, notes
                FROM staging_attendance
                WHERE 1=1
            '''
            params = []

            # Apply filters
            if filters.get('start_date'):
                query += ' AND date >= ?'
                params.append(filters['start_date'])
            if filters.get('end_date'):
                query += ' AND date <= ?'
                params.append(filters['end_date'])
            if filters.get('employee_id'):
                query += ' AND employee_id = ?'
                params.append(filters['employee_id'])
            if filters.get('status'):
                query += ' AND status = ?'
                params.append(filters['status'])

            # Add ordering and limits
            query += ' ORDER BY date ASC, employee_name ASC, created_at DESC'

            if filters.get('limit'):
                query += ' LIMIT ?'
                params.append(filters['limit'])
                if filters.get('offset'):
                    query += ' OFFSET ?'
                    params.append(filters['offset'])

            cursor.execute(query, params)
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()

            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))

            conn.close()
            return result

        except Exception as e:
            self.logger.error(f"Failed to get staging data: {str(e)}")
            return []

    def add_staging_record(self, record: Dict[str, Any]) -> str:
        """Add a record to staging."""
        try:
            import uuid

            conn = self.get_connection()
            cursor = conn.cursor()

            # Generate unique ID if not provided
            record_id = record.get('id', str(uuid.uuid4()))

            # Insert record
            cursor.execute('''
                INSERT OR REPLACE INTO staging_attendance (
                    id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                    check_in, check_out, regular_hours, overtime_hours, total_hours,
                    task_code, station_code, machine_code, expense_code, raw_charge_job,
                    leave_type_code, leave_type_description, leave_ref_number,
                    is_alfa, is_on_leave, status, source_record_id, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record_id,
                record.get('employee_id'),
                record.get('employee_name'),
                record.get('ptrj_employee_id', 'N/A'),
                record.get('date'),
                record.get('day_of_week'),
                record.get('shift'),
                record.get('check_in'),
                record.get('check_out'),
                record.get('regular_hours', 0),
                record.get('overtime_hours', 0),
                record.get('total_hours', 0),
                record.get('task_code'),
                record.get('station_code'),
                record.get('machine_code'),
                record.get('expense_code'),
                record.get('raw_charge_job'),
                record.get('leave_type_code'),
                record.get('leave_type_description'),
                record.get('leave_ref_number'),
                record.get('is_alfa', 0),
                record.get('is_on_leave', 0),
                record.get('status', 'staged'),
                record.get('source_record_id'),
                record.get('notes')
            ))

            conn.commit()
            conn.close()

            return record_id

        except Exception as e:
            self.logger.error(f"Failed to add staging record: {str(e)}")
            raise

    def update_staging_record(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a staging record."""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Build update query dynamically based on provided data
            update_fields = []
            params = []

            for field, value in data.items():
                if field != 'id':  # Don't update ID
                    update_fields.append(f"{field} = ?")
                    params.append(value)

            if not update_fields:
                return False

            # Add updated_at timestamp
            update_fields.append("updated_at = CURRENT_TIMESTAMP")

            query = f"UPDATE staging_attendance SET {', '.join(update_fields)} WHERE id = ?"
            params.append(record_id)

            cursor.execute(query, params)
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"Failed to update staging record: {str(e)}")
            return False

    def delete_staging_record(self, record_id: str) -> bool:
        """Delete a staging record."""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('DELETE FROM staging_attendance WHERE id = ?', (record_id,))
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"Failed to delete staging record: {str(e)}")
            return False

    def get_staging_statistics(self) -> Dict[str, Any]:
        """Get staging database statistics."""
        try:
            if not os.path.exists(self.staging_db_path):
                return {
                    'total_attendance_records': 0,
                    'total_log_entries': 0,
                    'unique_employees': 0,
                    'date_range': {'earliest': None, 'latest': None},
                    'recent_operations_24h': 0,
                    'database_size_mb': 0,
                    'error': 'Database file not found'
                }

            conn = self.get_connection()
            cursor = conn.cursor()

            # Get record counts
            cursor.execute("SELECT COUNT(*) FROM staging_attendance")
            attendance_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM staging_operations_log")
            log_count = cursor.fetchone()[0]

            # Get date range of attendance data
            cursor.execute("SELECT MIN(date), MAX(date) FROM staging_attendance WHERE date IS NOT NULL")
            date_range = cursor.fetchone()

            # Get unique employees count
            cursor.execute("SELECT COUNT(DISTINCT employee_id) FROM staging_attendance")
            unique_employees = cursor.fetchone()[0]

            # Get recent operations
            cursor.execute("SELECT COUNT(*) FROM staging_operations_log WHERE timestamp >= datetime('now', '-24 hours')")
            recent_operations = cursor.fetchone()[0]

            conn.close()

            return {
                'total_attendance_records': attendance_count,
                'total_log_entries': log_count,
                'unique_employees': unique_employees,
                'date_range': {
                    'earliest': date_range[0] if date_range[0] else None,
                    'latest': date_range[1] if date_range[1] else None
                },
                'recent_operations_24h': recent_operations,
                'database_size_mb': round(os.path.getsize(self.staging_db_path) / (1024 * 1024), 2)
            }

        except Exception as e:
            self.logger.error(f"Failed to get staging statistics: {str(e)}")
            return {
                'total_attendance_records': 0,
                'total_log_entries': 0,
                'unique_employees': 0,
                'date_range': {'earliest': None, 'latest': None},
                'recent_operations_24h': 0,
                'database_size_mb': 0,
                'error': str(e)
            }

    def cleanup_duplicates(self) -> Dict[str, Any]:
        """Clean up duplicate records in staging database, keeping the most recent ones."""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Find duplicate records (same employee_id + date combination)
            cursor.execute('''
                SELECT employee_id, date, COUNT(*) as count,
                       GROUP_CONCAT(id) as all_ids,
                       GROUP_CONCAT(created_at) as all_created_at
                FROM staging_attendance
                GROUP BY employee_id, date
                HAVING COUNT(*) > 1
                ORDER BY employee_id, date
            ''')

            duplicates = cursor.fetchall()

            if not duplicates:
                self.logger.info("No duplicate records found in staging database")
                return {'duplicates_found': 0, 'records_removed': 0}

            self.logger.info(f"Found {len(duplicates)} sets of duplicate records")

            total_removed = 0

            for dup in duplicates:
                employee_id, date, count, all_ids, all_created_at = dup
                ids_list = all_ids.split(',')
                created_at_list = all_created_at.split(',')

                # Create list of (id, created_at) pairs and sort by created_at (most recent first)
                id_time_pairs = list(zip(ids_list, created_at_list))
                id_time_pairs.sort(key=lambda x: x[1], reverse=True)

                # Keep the most recent record, remove the rest
                keep_id = id_time_pairs[0][0]
                remove_ids = [pair[0] for pair in id_time_pairs[1:]]

                self.logger.info(f"For {employee_id} on {date}: keeping {keep_id}, removing {len(remove_ids)} duplicates")

                # Remove duplicate records
                for remove_id in remove_ids:
                    cursor.execute('DELETE FROM staging_attendance WHERE id = ?', (remove_id,))
                    total_removed += 1

            conn.commit()
            conn.close()

            self.logger.info(f"Cleanup completed: removed {total_removed} duplicate records from {len(duplicates)} duplicate sets")

            return {
                'duplicates_found': len(duplicates),
                'records_removed': total_removed,
                'cleanup_successful': True
            }

        except Exception as e:
            self.logger.error(f"Error during duplicate cleanup: {e}")
            return {
                'duplicates_found': 0,
                'records_removed': 0,
                'cleanup_successful': False,
                'error': str(e)
            }

    def check_health(self) -> bool:
        """Check the health of the staging database."""
        try:
            if not os.path.exists(self.staging_db_path):
                return False

            conn = self.get_connection()
            cursor = conn.cursor()

            # Test basic connectivity and table existence
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='staging_attendance'")
            attendance_table = cursor.fetchone()

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='staging_operations_log'")
            log_table = cursor.fetchone()

            # Test a simple query
            cursor.execute("SELECT COUNT(*) FROM staging_attendance")
            cursor.fetchone()

            conn.close()

            return attendance_table is not None and log_table is not None

        except Exception as e:
            self.logger.error(f"Staging database health check failed: {str(e)}")
            return False


class DatabaseManager(IConnectionManager):
    """
    Main database manager that coordinates all database operations.
    Implements Dependency Inversion Principle and acts as a facade.
    """

    def __init__(self, config_manager, logging_manager):
        """
        Initialize database manager with all sub-managers.

        Args:
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)

        # Initialize sub-managers
        self.connection_manager = DatabaseConnectionManager(config_manager, logging_manager)
        self.staging_manager = StagingDatabaseManager(config_manager, logging_manager)

        # Performance tracking
        self._connection_pool = {}
        self._last_health_check = None

    def initialize_connections(self) -> bool:
        """Initialize database connections based on configuration."""
        try:
            self.logger.info("=" * 60)
            self.logger.info("INITIALIZING DATABASE CONNECTIONS")
            self.logger.info("=" * 60)

            # Check for database mode from environment variable (set by main app)
            db_mode = os.environ.get('DB_MODE', None)

            if db_mode == 'local':
                self.logger.info("🎯 Command-line argument detected: --local")
                self.logger.info("⚡ Fast boot mode: Using local database directly (skipping scan)")
                success, message = self.connection_manager.switch_connection_mode('local')
                if success:
                    self.logger.info(f"✅ Successfully switched to local mode: {message}")
                    scan_success = True
                else:
                    self.logger.warning(f"⚠️ Failed to switch to local mode: {message}")
                    self.logger.info("🔄 Falling back to standard initialization with scan...")
                    scan_success = self._perform_initial_scan()

            elif db_mode == 'remote':
                self.logger.info("🎯 Command-line argument detected: --remote")
                self.logger.info("⚡ Fast boot mode: Using remote database directly (skipping scan)")
                success, message = self.connection_manager.switch_connection_mode('remote')
                if success:
                    self.logger.info(f"✅ Successfully switched to remote mode: {message}")
                    scan_success = True
                else:
                    self.logger.warning(f"⚠️ Failed to switch to remote mode: {message}")
                    self.logger.info("🔄 Falling back to standard initialization with scan...")
                    scan_success = self._perform_initial_scan()
            else:
                self.logger.info("🔍 No specific database mode specified, performing initial scan...")
                scan_success = self._perform_initial_scan()

            self.logger.info("✅ Database Connection Manager initialized")
            self.logger.info("=" * 60)

            return scan_success

        except Exception as e:
            self.logger.error(f"❌ Database initialization failed: {str(e)}")
            return False

    def _perform_initial_scan(self) -> bool:
        """Perform initial database connection scan with quick timeouts."""
        try:
            current_mode = self.connection_manager.connection_mode

            local_success = False
            remote_success = False

            if current_mode == "remote":
                # Test remote first
                self.logger.info("🧪 Testing remote database connection (timeout: 10s)...")
                remote_success, remote_message = self.connection_manager.test_connection('remote')
                self.logger.info(f"  📍 Remote database: {'✅ Connected' if remote_success else '❌ Failed'}")

                # Test local as fallback if remote fails
                if not remote_success:
                    self.logger.info("🧪 Testing local database connection (timeout: 5s)...")
                    local_success, local_message = self.connection_manager.test_connection('local')
                    self.logger.info(f"  📍 Local database: {'✅ Connected' if local_success else '❌ Failed'}")
            else:
                # Test local first
                self.logger.info("🧪 Testing local database connection (timeout: 5s)...")
                local_success, local_message = self.connection_manager.test_connection('local')
                self.logger.info(f"  📍 Local database: {'✅ Connected' if local_success else '❌ Failed'}")

                # Test remote as fallback
                self.logger.info("🧪 Testing remote database connection (timeout: 10s)...")
                remote_success, remote_message = self.connection_manager.test_connection('remote')
                self.logger.info(f"  📍 Remote database: {'✅ Connected' if remote_success else '❌ Failed'}")

            # Auto-switch to working database if current mode is not working
            current_working = (current_mode == "local" and local_success) or (current_mode == "remote" and remote_success)

            if not current_working:
                if current_mode == "local" and not local_success and remote_success:
                    self.logger.info("🔄 Auto-switching to remote mode (local unavailable)")
                    self.connection_manager.connection_mode = "remote"
                elif current_mode == "remote" and not remote_success and local_success:
                    self.logger.info("🔄 Auto-switching to local mode (remote unavailable)")
                    self.connection_manager.connection_mode = "local"

            if local_success or remote_success:
                self.logger.info(f"✅ Initial scan complete: {('Local' if local_success else '')} {('Remote' if remote_success else '')} available")
                return True
            else:
                self.logger.warning("⚠️ No database connections available at startup")
                return False

        except Exception as e:
            self.logger.error(f"❌ Initial scan failed: {str(e)}")
            return False

    def switch_connection_mode(self, new_mode: str) -> Tuple[bool, str]:
        """Switch database connection mode."""
        return self.connection_manager.switch_connection_mode(new_mode)

    def perform_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check on all database components."""
        try:
            self._last_health_check = datetime.now()

            # Check main database connections
            connection_status = self.connection_manager.get_connection_status()

            # Check staging database
            staging_health = self.staging_manager.check_health()
            staging_stats = self.staging_manager.get_staging_statistics()

            # Overall health assessment
            main_db_healthy = (connection_status['local_status']['connected'] or
                             connection_status['remote_status']['connected'])

            overall_health = main_db_healthy and staging_health

            return {
                'overall_health': overall_health,
                'main_database': {
                    'status': connection_status,
                    'healthy': main_db_healthy
                },
                'staging_database': {
                    'healthy': staging_health,
                    'statistics': staging_stats
                },
                'last_check': self._last_health_check.isoformat(),
                'components_status': {
                    'connection_manager': True,
                    'staging_manager': True
                }
            }

        except Exception as e:
            self.logger.error(f"Health check failed: {str(e)}")
            return {
                'overall_health': False,
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }

    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        return self.connection_manager.get_connection_status()

    def update_database_config(self, new_config: Dict[str, Any]) -> Tuple[bool, str]:
        """Update database configuration."""
        try:
            success = self.config_manager.update_database_config(new_config)
            if success:
                # Reinitialize connection manager with new config
                self.connection_manager = DatabaseConnectionManager(self.config_manager,
                                                                   self.logger.logging_manager)
                return True, "Database configuration updated successfully"
            else:
                return False, "Failed to save configuration"

        except Exception as e:
            self.logger.error(f"Failed to update database configuration: {str(e)}")
            return False, f"Configuration update failed: {str(e)}"

    # Delegate methods to appropriate managers
    def get_connection(self, force_mode: Optional[str] = None):
        """Get database connection."""
        return self.connection_manager.get_connection(force_mode)

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        return self.connection_manager.execute_query(query, params)

    def execute_non_query(self, query: str, params: tuple = ()) -> int:
        """Execute a non-query and return affected rows."""
        return self.connection_manager.execute_non_query(query, params)

    # Staging database methods
    def get_staging_connection(self) -> sqlite3.Connection:
        """Get staging database connection."""
        return self.staging_manager.get_connection()

    def initialize_staging_database(self) -> bool:
        """Initialize staging database."""
        return self.staging_manager.initialize_database()

    def get_staging_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get staging data with filters."""
        return self.staging_manager.get_staging_data(filters)

    def add_staging_record(self, record: Dict[str, Any]) -> str:
        """Add staging record."""
        return self.staging_manager.add_staging_record(record)

    def update_staging_record(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update staging record."""
        return self.staging_manager.update_staging_record(record_id, data)

    def delete_staging_record(self, record_id: str) -> bool:
        """Delete staging record."""
        return self.staging_manager.delete_staging_record(record_id)

    def get_staging_statistics(self) -> Dict[str, Any]:
        """Get staging statistics."""
        return self.staging_manager.get_staging_statistics()

    def cleanup_staging_duplicates(self) -> Dict[str, Any]:
        """Clean up staging duplicates."""
        return self.staging_manager.cleanup_duplicates()

    def check_staging_health(self) -> bool:
        """Check staging database health."""
        return self.staging_manager.check_health()
