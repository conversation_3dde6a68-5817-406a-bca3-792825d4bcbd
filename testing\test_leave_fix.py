#!/usr/bin/env python3
"""
Test the leave data fix to ensure leave type codes are properly displayed
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from modules.attendance_reporter import AttendanceReporter
from modules.db_connection import DatabaseConnection
import j<PERSON>

def test_leave_display_fix():
    """Test that leave type codes are properly displayed in grid"""
    
    print("🔧 Testing Leave Type Code Display Fix")
    print("=" * 60)
    
    try:
        # Initialize database connection
        db_conn = DatabaseConnection()
        if not db_conn.connect():
            print("❌ Failed to connect to database")
            return False
        
        print("✅ Database connection established")
        
        # Initialize attendance reporter
        reporter = AttendanceReporter()
        print("✅ AttendanceReporter initialized")
        
        # Test monthly grid for June 2025 (known to have leave data)
        print("\n📅 Testing monthly grid for June 2025...")
        grid_data = reporter.get_monthly_attendance_grid(2025, 6, "PTRJ")
        
        print(f"   Grid generated for {grid_data.get('total_employees', 0)} employees")
        print(f"   Days in month: {grid_data.get('days_in_month', 0)}")
        
        # Look for employees with leave data
        leave_found = 0
        employees_with_leave = []
        
        for employee in grid_data.get('grid_data', []):
            employee_name = employee.get('EmployeeName', 'Unknown')
            employee_id = employee.get('EmployeeID', 'Unknown')
            days = employee.get('days', {})
            
            for day in range(1, 31):
                day_data = days.get(str(day))
                if day_data:
                    status = day_data.get('status')
                    leave_data = day_data.get('leave_data')
                    is_on_leave = day_data.get('is_on_leave')
                    
                    if status == 'on_leave' or is_on_leave or leave_data:
                        leave_found += 1
                        employees_with_leave.append({
                            'employee': employee_name,
                            'employee_id': employee_id,
                            'day': day,
                            'status': status,
                            'leave_data': leave_data,
                            'is_on_leave': is_on_leave
                        })
        
        print(f"\n🏷️  Leave Records Found: {leave_found}")
        
        if employees_with_leave:
            print("\n✅ SUCCESS: Leave data found in grid!")
            print("📋 Employees with Leave:")
            for record in employees_with_leave[:10]:  # Show first 10
                print(f"  👤 {record['employee']} ({record['employee_id']})")
                print(f"     📅 Day {record['day']}: status='{record['status']}'")
                if record['leave_data']:
                    leave_code = record['leave_data'].get('leave_type_code', 'Unknown')
                    leave_desc = record['leave_data'].get('leave_type_description', 'Unknown')
                    print(f"     🏷️  Leave: {leave_code} ({leave_desc})")
                print(f"     ✅ Is on leave: {record['is_on_leave']}")
                print()
                
            # Test specific known employee
            hanip_found = False
            for record in employees_with_leave:
                if 'Hanip' in record['employee']:
                    hanip_found = True
                    print(f"🎯 SPECIFIC TEST: Found Hanip with leave data!")
                    print(f"   Status: {record['status']}")
                    print(f"   Leave data: {record['leave_data']}")
                    break
            
            if not hanip_found:
                print("⚠️  WARNING: Hanip not found in leave records")
                
        else:
            print("❌ PROBLEM: No leave data found in grid!")
            
            # Debug: Check a sample employee's day structure
            if grid_data.get('grid_data'):
                sample_employee = grid_data['grid_data'][0]
                sample_day = sample_employee.get('days', {}).get('1')
                print(f"\n🔍 Sample day data structure for {sample_employee.get('EmployeeName')}:")
                if sample_day:
                    for key, value in sample_day.items():
                        print(f"     {key}: {value}")
        
        print(f"\n📊 Summary:")
        print(f"   Total employees: {grid_data.get('total_employees', 0)}")
        print(f"   Leave records found: {leave_found}")
        print(f"   Display format: {grid_data.get('display_format', 'unknown')}")
        
        return leave_found > 0
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_conn' in locals():
            db_conn.disconnect()

if __name__ == "__main__":
    success = test_leave_display_fix()
    if success:
        print("\n🎉 LEAVE DISPLAY FIX TEST PASSED!")
        print("✅ Leave type codes should now display in the grid")
    else:
        print("\n❌ LEAVE DISPLAY FIX TEST FAILED!")
        print("❌ Leave type codes are still not displaying properly")
    
    sys.exit(0 if success else 1)
