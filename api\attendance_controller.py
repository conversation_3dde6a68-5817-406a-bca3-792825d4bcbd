"""
Attendance Controller for Attendance Report System.
Handles all attendance-related API endpoints.
Implements Interface Segregation Principle.
"""

from flask import Blueprint, request, send_file
from typing import Dict, Any, List

from api.api_utils import (
    create_success_response, create_error_response, create_paginated_response,
    validate_required_params, log_api_request, parse_date_range,
    parse_pagination_params, validate_filters, handle_database_error,
    safe_int_conversion
)


class AttendanceController:
    """
    Controller for attendance-related API endpoints.
    Implements Interface Segregation - only handles attendance operations.
    """
    
    def __init__(self, attendance_service, logging_manager):
        """
        Initialize attendance controller.
        
        Args:
            attendance_service: Attendance service instance
            logging_manager: Logging manager instance
        """
        self.attendance_service = attendance_service
        self.logger = logging_manager.get_logger(__name__)
        
        # Create Flask blueprint
        self.blueprint = Blueprint('attendance', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """Register all attendance-related routes."""
        
        @self.blueprint.route('/attendance-data', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['start_date', 'end_date'])
        def get_attendance_data():
            """Get attendance data for date range with optional filters."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                # Parse optional filters
                bus_code = request.args.get('bus_code')
                employee_id = request.args.get('employee_id')
                shift = request.args.get('shift')
                
                # Get attendance data
                data = self.attendance_service.get_attendance_data(
                    start_date, end_date, bus_code, employee_id, shift
                )
                
                return create_success_response(
                    data=data,
                    message=f"Retrieved {len(data)} attendance records",
                    date_range={'start': start_date, 'end': end_date},
                    filters={'bus_code': bus_code, 'employee_id': employee_id, 'shift': shift}
                )
                
            except Exception as e:
                self.logger.error(f"Error getting attendance data: {str(e)}")
                return handle_database_error(e, "getting attendance data")
        
        @self.blueprint.route('/monthly-grid', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['year', 'month'])
        def get_monthly_attendance_grid():
            """Get monthly attendance in grid format."""
            try:
                # Parse parameters
                year = safe_int_conversion(request.args.get('year'), 2025)
                month = safe_int_conversion(request.args.get('month'), 1)
                bus_code = request.args.get('bus_code')
                
                # Validate month
                if month < 1 or month > 12:
                    return create_error_response("Month must be between 1 and 12", 400)
                
                # Get monthly grid data
                grid_data = self.attendance_service.get_monthly_attendance_grid(year, month, bus_code)
                
                return create_success_response(
                    data=grid_data,
                    message=f"Retrieved monthly attendance grid for {year}-{month:02d}"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting monthly attendance grid: {str(e)}")
                return handle_database_error(e, "getting monthly attendance grid")
        
        @self.blueprint.route('/summary', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['start_date', 'end_date'])
        def get_attendance_summary():
            """Get attendance summary statistics."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                bus_code = request.args.get('bus_code')
                
                # Get attendance summary
                summary = self.attendance_service.get_attendance_summary(start_date, end_date, bus_code)
                
                return create_success_response(
                    data=summary,
                    message="Attendance summary retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting attendance summary: {str(e)}")
                return handle_database_error(e, "getting attendance summary")
        
        @self.blueprint.route('/export', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['start_date', 'end_date', 'format'])
        def export_attendance_report():
            """Export attendance report in specified format."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                format_type = request.args.get('format', 'excel').lower()
                bus_code = request.args.get('bus_code')
                
                # Validate format
                if format_type not in ['excel', 'csv', 'json']:
                    return create_error_response("Format must be 'excel', 'csv', or 'json'", 400)
                
                # Export attendance report
                export_result = self.attendance_service.export_attendance_report(
                    start_date, end_date, format_type, bus_code
                )
                
                if format_type == 'excel':
                    return send_file(export_result, as_attachment=True)
                else:
                    return create_success_response(
                        data=export_result,
                        message=f"Attendance report exported in {format_type} format"
                    )
                
            except Exception as e:
                self.logger.error(f"Error exporting attendance report: {str(e)}")
                return handle_database_error(e, "exporting attendance report")
        
        @self.blueprint.route('/available-months', methods=['GET'])
        @self.blueprint.route('/months', methods=['GET'])  # Alias for frontend compatibility
        @log_api_request(self.logger)
        def get_available_months():
            """Get list of available months with attendance data."""
            try:
                bus_code = request.args.get('bus_code')
                
                # Get available months
                months = self.attendance_service.get_available_months(bus_code)
                
                return create_success_response(
                    data=months,
                    message=f"Retrieved {len(months)} available months",
                    bus_code=bus_code
                )
                
            except Exception as e:
                self.logger.error(f"Error getting available months: {str(e)}")
                return handle_database_error(e, "getting available months")
        
        @self.blueprint.route('/attendance-data-enhanced', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['start_date', 'end_date'])
        def get_enhanced_attendance_data():
            """Get attendance data enhanced with charge job information."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                bus_code = request.args.get('bus_code')
                
                # Get attendance data
                attendance_data = self.attendance_service.get_attendance_data(
                    start_date, end_date, bus_code
                )
                
                # TODO: Enhance with charge job data
                # This would be implemented when employee service is integrated
                
                return create_success_response(
                    data=attendance_data,
                    message=f"Retrieved {len(attendance_data)} enhanced attendance records",
                    enhancement_status="charge_job_enhancement_pending"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting enhanced attendance data: {str(e)}")
                return handle_database_error(e, "getting enhanced attendance data")
        
        @self.blueprint.route('/attendance-data-grid', methods=['GET'])
        @log_api_request(self.logger)
        @validate_required_params(['start_date', 'end_date'])
        def get_attendance_data_for_grid():
            """Get attendance data optimized for grid display."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                bus_code = request.args.get('bus_code')
                
                # Get attendance data
                attendance_data = self.attendance_service.get_attendance_data(
                    start_date, end_date, bus_code
                )
                
                # Optimize for grid display
                grid_optimized_data = self._optimize_for_grid_display(attendance_data)
                
                return create_success_response(
                    data=grid_optimized_data,
                    message=f"Retrieved attendance data optimized for grid display",
                    optimization="grid_display_format"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting grid-optimized attendance data: {str(e)}")
                return handle_database_error(e, "getting grid-optimized attendance data")
    
    def _optimize_for_grid_display(self, attendance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Optimize attendance data for grid display.
        
        Args:
            attendance_data: List of attendance records
            
        Returns:
            Optimized data structure for grid display
        """
        try:
            # Group by employee for efficient grid rendering
            employees = {}
            
            for record in attendance_data:
                emp_id = record.get('EmployeeID', '')
                emp_name = record.get('EmployeeName', '')
                employee_key = f"{emp_id}_{emp_name}"
                
                if employee_key not in employees:
                    employees[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'attendance_records': []
                    }
                
                employees[employee_key]['attendance_records'].append({
                    'date': record.get('TADate'),
                    'shift': record.get('Shift'),
                    'check_in': record.get('CheckIn'),
                    'check_out': record.get('CheckOut'),
                    'total_hours': record.get('TotalHours'),
                    'status': 'present' if record.get('CheckIn') else 'absent'
                })
            
            return {
                'employees': list(employees.values()),
                'total_employees': len(employees),
                'total_records': len(attendance_data)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to optimize data for grid display: {str(e)}")
            return {
                'employees': [],
                'total_employees': 0,
                'total_records': 0,
                'error': str(e)
            }
