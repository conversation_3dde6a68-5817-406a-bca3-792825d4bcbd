# Project Overview: VenusHR14 Attendance Report System

## Project Overview
This is an enhanced attendance reporting system that interfaces with the VenusHR14 database to provide comprehensive employee attendance analytics, overtime tracking, and data export capabilities. The system serves as a modern web-based interface for HR departments to efficiently manage and analyze workforce attendance data.

## Core Requirements

### Primary Goals
1. **Database Integration**: Read attendance data from VenusHR14 SQL Server database (HR_T_TAMachine_Summary, HR_M_EmployeePI, HR_T_Overtime tables)
2. **Working Hours Calculation**: Display attendance in format "(regular_hours) | (overtime_hours)" with business rule enforcement
3. **Business Rule Enforcement**: 
   - Weekdays: Max 7 regular hours
   - Saturdays: Max 5 regular hours  
   - Sundays: Show overtime only if exists, otherwise "OFF"
4. **Monthly Grid Display**: Calendar-style grid showing daily attendance with color coding
5. **Export Functionality**: Excel and JSON export capabilities
6. **Google Sheets Integration**: Sync functionality for data collaboration

### Business Rules
- **Business Code**: Hardcoded to 'PTRJ' throughout system
- **Working Hours Format**: "(regular) | (overtime)" or "(regular) | (-)" if no OT
- **Color Coding**: Green for meeting thresholds, red for below, based on regular hours only
- **Sunday Logic**: Check overtime first, display "(0) | (OT)" or "OFF"
- **Database Access**: READ-ONLY operations only

### Key Features
1. **Monthly Reports**: Auto-loading available months, click-to-view grid format
2. **Custom Date Range**: Flexible filtering with employee/shift selection
3. **Station Grouping**: Grouped display by employee stations
4. **Sticky Headers**: Fixed date headers during vertical scroll
5. **Indonesian Localization**: Day abbreviations (Min, Sen, Sel, etc.)
6. **Export Options**: Excel with formatting preservation, JSON with metadata
7. **Sync Capabilities**: Google Apps Script integration for sheet updates

## Success Criteria
- Accurate overtime integration from HR_T_Overtime table
- Proper business rule application for working hours
- Intuitive monthly grid interface with color coding
- Reliable export functionality preserving formatting
- Seamless Google Sheets sync capability
- READ-ONLY database operations ensuring data integrity

## Constraints
- Must maintain compatibility with existing VenusHR14 database schema
- No modifications to source database tables
- Performance optimization for large datasets
- Cross-browser compatibility for web interface
- Secure handling of database credentials 