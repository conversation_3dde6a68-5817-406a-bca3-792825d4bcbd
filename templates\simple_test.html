<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Attendance Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid py-4">
        <h1>Simple Test - Monthly Data</h1>
        
        <div class="row mb-4">
            <div class="col-12">
                <button class="btn btn-primary" id="testBtn">Test Load Months</button>
                <button class="btn btn-secondary" id="testApiBtn">Test API Direct</button>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div id="results"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div id="monthsContainer" class="row"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Simple test page loaded');
            
            $('#testBtn').click(function() {
                console.log('Testing load months...');
                loadMonths();
            });
            
            $('#testApiBtn').click(function() {
                console.log('Testing API direct...');
                window.open('/api/months', '_blank');
            });
        });
        
        function loadMonths() {
            $('#results').html('<div class="alert alert-info">Loading...</div>');
            
            $.ajax({
                url: '/api/months',
                method: 'GET',
                success: function(response) {
                    console.log('Success:', response);
                    
                    let html = '<div class="alert alert-success">Success! Found ' + response.data.length + ' months</div>';
                    html += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                    
                    $('#results').html(html);
                    
                    // Display month cards
                    displayMonths(response.data);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', {xhr, status, error});
                    
                    let html = '<div class="alert alert-danger">Error: ' + error + '</div>';
                    html += '<pre>' + xhr.responseText + '</pre>';
                    
                    $('#results').html(html);
                }
            });
        }
        
        function displayMonths(months) {
            const container = $('#monthsContainer');
            container.empty();
            
            if (!months || months.length === 0) {
                container.html('<div class="col-12"><div class="alert alert-warning">No months found</div></div>');
                return;
            }
            
            months.forEach(function(month) {
                const card = `
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">${month.display_name || month.month_name + ' ' + month.year}</h5>
                                <p class="card-text">
                                    Records: ${month.record_count}<br>
                                    Employees: ${month.employee_count}<br>
                                    Period: ${month.first_date} to ${month.last_date}
                                </p>
                                <button class="btn btn-primary btn-sm" onclick="loadMonthData(${month.year}, ${month.month})">
                                    View Details
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.append(card);
            });
        }
        
        function loadMonthData(year, month) {
            console.log('Loading data for:', year, month);
            
            $.ajax({
                url: '/api/monthly-report',
                method: 'GET',
                data: { year: year, month: month },
                success: function(response) {
                    console.log('Monthly data:', response);
                    alert('Monthly data loaded! Check console for details. Records: ' + response.total_records);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading monthly data:', error);
                    alert('Error loading monthly data: ' + error);
                }
            });
        }
    </script>
</body>
</html>
