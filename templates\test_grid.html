<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Grid - Attendance Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .attendance-grid {
            font-size: 0.85rem;
        }

        .attendance-grid th,
        .attendance-grid td {
            padding: 0.3rem !important;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid th:nth-child(2),
        .attendance-grid th:nth-child(3),
        .attendance-grid td:nth-child(1),
        .attendance-grid td:nth-child(2),
        .attendance-grid td:nth-child(3) {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 10;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid td:nth-child(1) {
            left: 0;
            min-width: 50px;
        }

        .attendance-grid th:nth-child(2),
        .attendance-grid td:nth-child(2) {
            left: 50px;
            min-width: 120px;
        }

        .attendance-grid th:nth-child(3),
        .attendance-grid td:nth-child(3) {
            left: 170px;
            min-width: 200px;
            text-align: left !important;
        }

        .table-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
            font-weight: bold;
        }

        .table-danger {
            background-color: #f8d7da !important;
            color: #842029;
            font-weight: bold;
        }

        .table-warning {
            background-color: #fff3cd !important;
            color: #664d03;
            font-weight: bold;
        }

        .table-secondary {
            background-color: #e2e3e5 !important;
            color: #41464b;
        }

        .table-info {
            background-color: #d1ecf1 !important;
            color: #055160;
        }

        .table-container {
            overflow-x: auto;
            max-height: 600px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1>Test Grid - Monthly Attendance</h1>

        <div class="row mb-4">
            <div class="col-12">
                <button class="btn btn-primary" onclick="testGrid(2025, 3)">Test March 2025 Grid (Real Data)</button>
                <button class="btn btn-secondary" onclick="testGrid(2025, 2)">Test February 2025 Grid</button>
                <button class="btn btn-success" onclick="testSampleGrid()">Test Sample Grid (Working Hours)</button>
                <button class="btn btn-info" onclick="testApiDirect()">Test API Direct</button>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div id="results"></div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="table-container">
                    <table class="table table-striped attendance-grid" id="gridTable">
                        <thead class="table-dark">
                            <!-- Headers will be populated by JavaScript -->
                        </thead>
                        <tbody id="gridTableBody">
                            <tr>
                                <td colspan="10" class="text-center text-muted">
                                    Click a test button above to load grid data
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function testGrid(year, month) {
            console.log('Testing grid for:', year, month);

            $('#results').html('<div class="alert alert-info">Loading grid data...</div>');

            $.ajax({
                url: '/api/monthly-grid',
                method: 'GET',
                data: {
                    year: year,
                    month: month,
                    bus_code: 'PTRJ'
                },
                success: function(response) {
                    console.log('Grid response:', response);

                    if (response.success) {
                        displayGrid(response.data);
                        $('#results').html('<div class="alert alert-success">Grid loaded successfully!</div>');
                    } else {
                        $('#results').html('<div class="alert alert-danger">Error: ' + response.error + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', {xhr, status, error});
                    $('#results').html('<div class="alert alert-danger">AJAX Error: ' + error + '</div>');
                }
            });
        }

        function displayGrid(gridData) {
            console.log('Displaying grid:', gridData);

            // Create header
            let headerHtml = `
                <tr>
                    <th>No</th>
                    <th>Employee ID</th>
                    <th>Employee Name</th>
            `;

            for (let day = 1; day <= gridData.days_in_month; day++) {
                headerHtml += `<th>${day}</th>`;
            }
            headerHtml += '</tr>';

            // Create body
            let bodyHtml = '';
            gridData.grid_data.forEach(function(employee) {
                let rowHtml = `
                    <tr>
                        <td>${employee.No}</td>
                        <td>${employee.EmployeeID}</td>
                        <td>${employee.EmployeeName}</td>
                `;

                for (let day = 1; day <= gridData.days_in_month; day++) {
                    const status = employee.days[day.toString()] || '-';
                    const cellClass = getStatusClass(status);
                    rowHtml += `<td class="${cellClass}">${status}</td>`;
                }

                rowHtml += '</tr>';
                bodyHtml += rowHtml;
            });

            $('#gridTable thead').html(headerHtml);
            $('#gridTableBody').html(bodyHtml);
        }

        function getStatusClass(status) {
            switch (status) {
                case '1': return 'table-success';
                case '-': return 'table-danger';
                case 'H': return 'table-warning';
                case 'S': return 'table-secondary';
                case 'M': return 'table-info';
                case 'C': return 'table-primary';
                default: return '';
            }
        }

        function testSampleGrid() {
            console.log('Testing sample grid');

            $('#results').html('<div class="alert alert-info">Loading sample grid data...</div>');

            $.ajax({
                url: '/api/test-grid-with-sample',
                method: 'GET',
                success: function(response) {
                    console.log('Sample grid response:', response);

                    if (response.success) {
                        displayGrid(response);
                        $('#results').html('<div class="alert alert-success">Sample grid loaded successfully!</div>');
                    } else {
                        $('#results').html('<div class="alert alert-danger">Error: ' + response.error + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', {xhr, status, error});
                    $('#results').html('<div class="alert alert-danger">AJAX Error: ' + error + '</div>');
                }
            });
        }

        function testApiDirect() {
            window.open('/api/monthly-grid?year=2025&month=3&bus_code=PTRJ', '_blank');
        }
    </script>
</body>
</html>
