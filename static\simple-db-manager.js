/**
 * Simplified Database Connection Manager
 * Handles connection status, testing, and configuration without redirect issues
 */

class SimpleDatabaseManager {
    constructor() {
        this.connectionTestTimeout = 10000; // 10 seconds
        this.statusRefreshInterval = 30000; // 30 seconds
        this.statusTimer = null;
        this.currentMode = 'local';
        this.connectionHistory = [];
        this.isInitialized = false;
        
        // Local storage keys
        this.storageKeys = {
            connectionStatus: 'db_connection_status',
            connectionHistory: 'db_connection_history',
            lastScan: 'db_last_scan'
        };
    }

    async init() {
        console.log('🚀 Initializing Simple Database Manager');
        
        try {
            // Load cached status first
            this.loadCachedStatus();
            
            // Bind events
            this.bindEvents();
            
            // Perform initial scan
            await this.performInitialScan();
            
            // Start status monitoring
            this.startStatusMonitoring();
            
            this.isInitialized = true;
            console.log('✅ Simple Database Manager initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Simple Database Manager:', error);
            this.showUserFeedback('error', 'Failed to initialize database connection manager');
        }
    }

    bindEvents() {
        // Scan Connections button (renamed from Test All)
        $('#scanConnectionsBtn').off('click').on('click', () => {
            this.performConnectionScan();
        });
        
        // Database mode toggle - two-way toggle
        $('#databaseModeToggle').off('change').on('change', (e) => {
            const isRemote = $(e.target).prop('checked');
            this.handleModeToggle(isRemote);
        });
        
        // Modal events - placeholder for future implementation
        // this.bindModalEvents();
    }

    async performInitialScan() {
        console.log('🔍 Starting initial database connection scan...');
        
        try {
            this.showStatusLoading(true);
            this.showUserFeedback('info', 'Memindai koneksi database...');
            
            const response = await this.makeRequest('/api/database/initial-scan', {
                method: 'POST',
                timeout: 15000 // 15 seconds max for initial scan
            });
            
            if (response.success) {
                console.log('✅ Initial scan completed');
                this.updateConnectionStatus(response.status);
                this.saveStatusToCache(response.status);
                
                // Show scan results in Indonesian
                const { local_available, remote_available } = response.summary;
                if (local_available && remote_available) {
                    this.showUserFeedback('success', 'Database lokal dan remote tersedia');
                } else if (local_available) {
                    this.showUserFeedback('info', 'Database lokal tersedia (remote tidak tersedia)');
                } else if (remote_available) {
                    this.showUserFeedback('warning', 'Hanya database remote yang tersedia');
                } else {
                    this.showUserFeedback('warning', 'Tidak ada koneksi database tersedia - diperlukan pengujian manual');
                }
            } else {
                throw new Error(response.error || 'Initial scan failed');
            }
            
        } catch (error) {
            console.error('❌ Initial scan failed:', error);
            this.showUserFeedback('error', 'Pemindaian database awal gagal - menggunakan status yang tersimpan');
            
            // Try to load basic status without scan
            await this.loadCurrentStatus();
        } finally {
            this.showStatusLoading(false);
        }
    }

    async loadCurrentStatus() {
        try {
            console.log('📥 Loading current database status');
            
            const response = await this.makeRequest('/api/database/status', {
                method: 'GET',
                timeout: this.connectionTestTimeout
            });
            
            if (response.success) {
                this.updateConnectionStatus(response.status);
                this.currentMode = response.status.current_mode;
                this.saveStatusToCache(response.status);
                console.log('✅ Database status loaded successfully');
            } else {
                throw new Error(response.error || 'Failed to load database status');
            }
            
        } catch (error) {
            console.error('❌ Error loading database status:', error);
            this.showUserFeedback('error', `Database status error: ${error.message}`);
            // No redirect - just show the error
        }
    }

    async testCurrentConnection() {
        console.log(`🧪 Testing ${this.currentMode} database connection`);
        
        try {
            this.showConnectionTestLoading(true);
            
            const response = await this.makeRequest('/api/database/test-connection', {
                method: 'POST',
                body: JSON.stringify({ mode: this.currentMode }),
                headers: { 'Content-Type': 'application/json' },
                timeout: this.connectionTestTimeout
            });
            
            if (response.success && response.results[this.currentMode]) {
                const result = response.results[this.currentMode];
                
                if (result.success) {
                    this.showUserFeedback('success', `${this.currentMode} database connection successful!`);
                } else {
                    this.showUserFeedback('error', `${this.currentMode} database connection failed: ${result.message}`);
                }
                
                this.addToConnectionHistory(this.currentMode, result.success, result.message);
                this.updateConnectionStatus(response.status);
            } else {
                throw new Error(response.error || 'Test failed');
            }
            
        } catch (error) {
            console.error('❌ Connection test failed:', error);
            this.showUserFeedback('error', `Failed to test ${this.currentMode} database connection`);
        } finally {
            this.showConnectionTestLoading(false);
        }
    }

    async testAllConnections() {
        console.log('🧪 Testing all database connections');
        
        try {
            this.showConnectionTestLoading(true);
            this.showUserFeedback('info', 'Testing all database connections...');
            
            const response = await this.makeRequest('/api/database/test-connection', {
                method: 'POST',
                body: JSON.stringify({ mode: 'all' }),
                headers: { 'Content-Type': 'application/json' },
                timeout: this.connectionTestTimeout * 2 // Double timeout for testing both
            });
            
            if (response.success) {
                console.log('✅ All connections tested');
                
                // Update results for each connection
                if (response.results.local) {
                    this.addToConnectionHistory('local', response.results.local.success, response.results.local.message);
                }
                
                if (response.results.remote) {
                    this.addToConnectionHistory('remote', response.results.remote.success, response.results.remote.message);
                }
                
                this.updateConnectionStatus(response.status);
                this.saveStatusToCache(response.status);
                
                // Show summary
                const localOk = response.results.local?.success || false;
                const remoteOk = response.results.remote?.success || false;
                
                if (localOk && remoteOk) {
                    this.showUserFeedback('success', 'Both local and remote database connections successful!');
                } else if (localOk) {
                    this.showUserFeedback('warning', 'Local database OK, remote database failed');
                } else if (remoteOk) {
                    this.showUserFeedback('warning', 'Remote database OK, local database failed');
                } else {
                    this.showUserFeedback('error', 'Both database connections failed');
                }
                
                // Show suggestion if available
                if (response.suggestion) {
                    this.showUserFeedback('info', response.suggestion, true);
                }
                
            } else {
                throw new Error(response.error || 'Failed to test connections');
            }
            
        } catch (error) {
            console.error('❌ Test all connections failed:', error);
            this.showUserFeedback('error', 'Failed to test database connections');
        } finally {
            this.showConnectionTestLoading(false);
        }
    }

    async refreshConnectionStatus() {
        console.log('🔄 Refreshing connection status');
        
        try {
            this.showStatusLoading(true);
            
            // Test all connections to get fresh results
            await this.testAllConnections();
            
            this.showUserFeedback('success', 'Connection status refreshed');
            
        } catch (error) {
            console.error('❌ Error refreshing status:', error);
            this.showUserFeedback('error', 'Failed to refresh connection status');
        } finally {
            this.showStatusLoading(false);
        }
    }

    async handleModeToggle(isRemoteMode) {
        const newMode = isRemoteMode ? 'remote' : 'local';
        
        if (newMode !== this.currentMode) {
            await this.switchDatabaseMode(newMode);
        }
    }

    async switchDatabaseMode(newMode) {
        console.log(`🔄 Switching to ${newMode} database mode`);
        
        try {
            this.showModeChangeLoading(true);
            
            const response = await this.makeRequest('/api/database/switch-mode', {
                method: 'POST',
                body: JSON.stringify({ mode: newMode }),
                headers: { 'Content-Type': 'application/json' },
                timeout: this.connectionTestTimeout
            });
            
            if (response.success) {
                this.currentMode = newMode;
                this.updateConnectionStatus(response.status);
                this.updateModeToggle(newMode);
                this.saveStatusToCache(response.status);
                
                this.showUserFeedback('success', `Successfully switched to ${newMode} database`);
            } else {
                // Revert toggle
                this.updateModeToggle(this.currentMode);
                this.showUserFeedback('error', `Failed to switch to ${newMode} database: ${response.message}`);
            }
            
        } catch (error) {
            console.error('❌ Error switching database mode:', error);
            this.updateModeToggle(this.currentMode); // Revert toggle
            this.showUserFeedback('error', `Error switching to ${newMode} database`);
        } finally {
            this.showModeChangeLoading(false);
        }
    }

    // Local Storage Methods
    loadCachedStatus() {
        try {
            const cachedStatus = localStorage.getItem(this.storageKeys.connectionStatus);
            const cachedHistory = localStorage.getItem(this.storageKeys.connectionHistory);
            
            if (cachedStatus) {
                const status = JSON.parse(cachedStatus);
                this.updateConnectionStatus(status);
                this.currentMode = status.current_mode || 'local';
                console.log('📋 Loaded cached connection status');
            }
            
            if (cachedHistory) {
                this.connectionHistory = JSON.parse(cachedHistory);
                this.updateConnectionHistoryDisplay();
                console.log('📋 Loaded cached connection history');
            }
            
        } catch (error) {
            console.error('❌ Error loading cached status:', error);
        }
    }

    saveStatusToCache(status) {
        try {
            localStorage.setItem(this.storageKeys.connectionStatus, JSON.stringify(status));
            localStorage.setItem(this.storageKeys.lastScan, new Date().toISOString());
            console.log('💾 Status saved to cache');
        } catch (error) {
            console.error('❌ Error saving status to cache:', error);
        }
    }

    saveHistoryToCache() {
        try {
            localStorage.setItem(this.storageKeys.connectionHistory, JSON.stringify(this.connectionHistory));
        } catch (error) {
            console.error('❌ Error saving history to cache:', error);
        }
    }

    // Connection History Management
    addToConnectionHistory(mode, success, message) {
        const historyEntry = {
            mode: mode,
            success: success,
            message: message,
            timestamp: new Date().toISOString()
        };
        
        this.connectionHistory.unshift(historyEntry);
        
        // Keep only last 10 entries
        if (this.connectionHistory.length > 10) {
            this.connectionHistory = this.connectionHistory.slice(0, 10);
        }
        
        this.updateConnectionHistoryDisplay();
        this.saveHistoryToCache();
    }

    updateConnectionHistoryDisplay() {
        const historyContainer = $('#connectionHistoryList');
        if (historyContainer.length === 0) return;
        
        historyContainer.empty();
        
        if (this.connectionHistory.length === 0) {
            historyContainer.append('<div class="text-muted">No connection tests performed yet</div>');
            return;
        }
        
        this.connectionHistory.forEach(entry => {
            const statusClass = entry.success ? 'text-success' : 'text-danger';
            const statusIcon = entry.success ? '✅' : '❌';
            const timestamp = new Date(entry.timestamp).toLocaleString();
            
            const historyItem = $(`
                <div class="history-item mb-2 p-2 border rounded">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="${statusClass}">${statusIcon} ${entry.mode.toUpperCase()}</span>
                            <div class="small text-muted">${timestamp}</div>
                        </div>
                        <div class="small ${statusClass}" style="max-width: 60%;">
                            ${entry.message}
                        </div>
                    </div>
                </div>
            `);
            
            historyContainer.append(historyItem);
        });
    }

    // Status Update Methods
    updateConnectionStatus(status) {
        this.updateStatusIndicator('local', status.local_status);
        this.updateStatusIndicator('remote', status.remote_status);
        this.updateCurrentModeIndicator(status.current_mode);
        this.updateFallbackIndicator(status.fallback_enabled);
        
        if (status.application_health) {
            this.updateHealthIndicators(status.application_health);
        }
    }

    updateStatusIndicator(mode, status) {
        const indicator = $(`#${mode}StatusIndicator`);
        const text = $(`#${mode}StatusText`);
        
        if (indicator.length === 0) return;
        
        indicator.removeClass('status-connected status-disconnected status-unknown');
        
        if (status.connected === true) {
            indicator.addClass('status-connected');
            text.text('Connected');
        } else if (status.connected === false) {
            indicator.addClass('status-disconnected');
            text.text('Disconnected');
        } else {
            indicator.addClass('status-unknown');
            text.text('Unknown');
        }
        
        // Update last attempt time
        if (status.last_attempt) {
            const lastAttempt = new Date(status.last_attempt).toLocaleString();
            $(`#${mode}LastAttempt`).text(`Last: ${lastAttempt}`);
        }
        
        // Update error message
        if (status.error) {
            $(`#${mode}ErrorMessage`).text(status.error).show();
        } else {
            $(`#${mode}ErrorMessage`).hide();
        }
    }

    updateCurrentModeIndicator(mode) {
        this.currentMode = mode;
        this.updateModeToggle(mode);
        
        $('#currentModeDisplay').text(mode.charAt(0).toUpperCase() + mode.slice(1));
    }

    updateModeToggle(mode) {
        const toggle = $('#databaseModeToggle');
        
        if (mode === 'remote') {
            toggle.prop('checked', true);
        } else {
            toggle.prop('checked', false);
        }
        
        console.log(`🔄 Toggle updated to: ${mode} mode`);
    }

    updateFallbackIndicator(enabled) {
        const indicator = $('#fallbackEnabledIndicator');
        if (indicator.length > 0) {
            indicator.text(enabled ? 'Enabled' : 'Disabled');
            indicator.removeClass('text-success text-muted').addClass(enabled ? 'text-success' : 'text-muted');
        }
    }

    updateHealthIndicators(health) {
        // Update health indicators if they exist
        if (health.enhanced_reporter !== undefined) {
            $('#reporterHealthIndicator').text(health.enhanced_reporter ? 'OK' : 'Failed');
        }
        
        if (health.db_manager !== undefined) {
            $('#dbManagerHealthIndicator').text(health.db_manager ? 'OK' : 'Failed');
        }
        
        if (health.staging_db !== undefined) {
            $('#stagingDbHealthIndicator').text(health.staging_db ? 'OK' : 'Failed');
        }
    }

    // UI Feedback Methods
    showUserFeedback(type, message, persistent = false) {
        // Remove existing feedback
        $('.connection-feedback').remove();
        
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const icon = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }[type] || 'ℹ️';
        
        const feedback = $(`
            <div class="alert ${alertClass} alert-dismissible fade show connection-feedback" role="alert">
                <span>${icon} ${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('#connectionFeedbackContainer').append(feedback);
        
        // Auto-hide non-persistent messages
        if (!persistent) {
            setTimeout(() => {
                feedback.fadeOut();
            }, 5000);
        }
    }

    // Loading States
    showStatusLoading(show) {
        this.toggleLoadingState('statusLoadingSpinner', show);
    }

    showConnectionTestLoading(show) {
        this.toggleLoadingState('connectionTestSpinner', show);
        $('#scanConnectionsBtn').prop('disabled', show);
    }
    
    showScanLoading(show) {
        this.toggleLoadingState('scanLoading', show);
        $('#scanConnectionsBtn').prop('disabled', show);
    }

    showModeChangeLoading(show) {
        this.toggleLoadingState('modeChangeSpinner', show);
        $('#databaseModeToggle').prop('disabled', show);
    }

    toggleLoadingState(elementId, show) {
        const element = $(`#${elementId}`);
        if (element.length > 0) {
            if (show) {
                element.show();
            } else {
                element.hide();
            }
        }
    }

    // Status Monitoring
    startStatusMonitoring() {
        // Clear existing timer
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
        }
        
        // Start new timer for periodic status updates
        this.statusTimer = setInterval(() => {
            this.loadCurrentStatus();
        }, this.statusRefreshInterval);
        
        console.log('🔄 Status monitoring started');
    }

    stopStatusMonitoring() {
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
            this.statusTimer = null;
            console.log('⏹️ Status monitoring stopped');
        }
    }

    // HTTP Request Helper
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: this.connectionTestTimeout
        };
        
        const requestOptions = { ...defaultOptions, ...options };
        
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, requestOptions.timeout);
            
            $.ajax({
                url: url,
                method: requestOptions.method,
                data: requestOptions.body,
                headers: requestOptions.headers,
                dataType: 'json'
            })
            .done((response) => {
                clearTimeout(timeoutId);
                resolve(response);
            })
            .fail((xhr, status, error) => {
                clearTimeout(timeoutId);
                reject(new Error(error || 'Request failed'));
            });
        });
    }

    // Cleanup
    destroy() {
        this.stopStatusMonitoring();
        console.log('🧹 Simple Database Manager destroyed');
    }

    async performConnectionScan() {
        console.log('🔍 Starting connection scan for all databases...');
        
        try {
            this.showScanLoading(true);
            
            const response = await this.makeRequest('/api/database/test-connection', {
                method: 'POST',
                body: JSON.stringify({ mode: 'all' }),
                headers: { 'Content-Type': 'application/json' },
                timeout: this.connectionTestTimeout * 2 // Double timeout for testing both
            });
            
            if (response.success) {
                console.log('✅ Connection scan completed');
                
                // Update results for each connection with color coding
                if (response.results.local) {
                    this.updateConnectionResult('local', response.results.local.success, response.results.local.message);
                    this.addToConnectionHistory('local', response.results.local.success, response.results.local.message);
                }
                
                if (response.results.remote) {
                    this.updateConnectionResult('remote', response.results.remote.success, response.results.remote.message);
                    this.addToConnectionHistory('remote', response.results.remote.success, response.results.remote.message);
                }
                
                this.updateConnectionStatus(response.status);
                this.saveStatusToCache(response.status);
                
                // Show summary feedback in Indonesian
                const localOk = response.results.local?.success || false;
                const remoteOk = response.results.remote?.success || false;
                
                if (localOk && remoteOk) {
                    this.showUserFeedback('success', 'Koneksi database lokal dan remote berhasil!');
                } else if (localOk) {
                    this.showUserFeedback('warning', 'Database lokal terhubung, database remote gagal');
                } else if (remoteOk) {
                    this.showUserFeedback('warning', 'Database remote terhubung, database lokal gagal');
                } else {
                    this.showUserFeedback('error', 'Kedua koneksi database gagal');
                }
                
                // Show suggestion if available
                if (response.suggestion) {
                    this.showUserFeedback('info', response.suggestion, true);
                }
                
            } else {
                throw new Error(response.error || 'Failed to scan connections');
            }
            
        } catch (error) {
            console.error('❌ Connection scan failed:', error);
            this.showUserFeedback('error', 'Gagal memindai koneksi database');
        } finally {
            this.showScanLoading(false);
        }
    }

    updateConnectionResult(mode, success, message) {
        // Update connection status text with color coding
        const statusText = $(`#${mode}StatusText`);
        const statusIndicator = $(`#${mode}StatusIndicator`);
        const lastAttempt = $(`#${mode}LastAttempt`);
        
        // Clear previous classes
        statusText.removeClass('connection-status-connected connection-status-disconnected connection-status-unknown');
        statusIndicator.removeClass('status-connected status-disconnected status-unknown');
        
        if (success) {
            statusText.text('Terhubung').addClass('connection-status-connected');
            statusIndicator.addClass('status-connected');
        } else {
            statusText.text('Terputus').addClass('connection-status-disconnected');
            statusIndicator.addClass('status-disconnected');
        }
        
        // Update last attempt time
        const timestamp = new Date().toLocaleString('id-ID');
        lastAttempt.text(`Pemindaian terakhir: ${timestamp}`);
        
        console.log(`✅ Updated ${mode} connection result: ${success ? 'Connected' : 'Disconnected'}`);
    }
}

// Global instance
let simpleDatabaseManager = null;

// Initialize when document is ready
$(document).ready(function() {
    console.log('🚀 Initializing Simple Database Manager...');
    
    simpleDatabaseManager = new SimpleDatabaseManager();
    simpleDatabaseManager.init().catch(error => {
        console.error('❌ Failed to initialize Simple Database Manager:', error);
    });
});

// Export for global access
window.SimpleDatabaseManager = SimpleDatabaseManager;
window.simpleDatabaseManager = simpleDatabaseManager; 