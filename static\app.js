/**
 * Attendance Report System - Frontend JavaScript
 * Handles user interactions, API calls, and data display
 */

// Global variables
let currentSelectedMonth = null;
let availableMonths = [];
let syncModeActive = false;
let selectedRows = new Set();
// Monthly Grid Sync Variables
let monthlyGridSyncModeActive = false;
let monthlyGridSelectedRows = new Set();
let currentMonthlyGridData = null;
// Employee Charge Job Data
let employeeChargeJobData = null;
// Staging Selection Variables
let stagingSelectionModeActive = false; // Default to false - checkboxes hidden initially
let attendanceSelectedRows = new Set();

$(document).ready(function() {
    console.log('Document ready, initializing application...');

    // Initialize the application
    initializeApp();

    // Set default dates (last 7 days)
    setDefaultDates();

    // Load initial data for custom tab
    loadEmployees();
    loadShifts();

    // Auto-load available months on page load
    console.log('Auto-loading available months...');
    loadAvailableMonths();
    
    // Load employee charge job data
    console.log('Loading employee charge job data...');
    loadEmployeeChargeJobData()
        .then(() => {
            console.log('Employee charge job data loaded successfully');
        })
        .catch((error) => {
            console.warn('Could not load employee charge job data during initialization:', error);
        });

    // Initialize staging functionality when document is ready
    if (window.location.hash === '#staging') {
        initializeStagingTab();
    }
    
    // Initialize database connection management
    console.log('Initializing database connection management...');
    databaseConnectionManager.init();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Initializing application...');

    // Don't initialize DataTable immediately - wait for data
    // We'll initialize it in displayAttendanceData function

    // Bind event handlers
    $('#filterForm').on('submit', handleFormSubmit);
    $('#exportBtn').on('click', handleExport);
    $('#exportJsonBtn').on('click', handleJsonExport);
    $('#toggleSyncMode').on('click', toggleSyncMode);
    $('#syncToSheetBtn').on('click', syncToSpreadsheet);
    $('#selectAll').on('change', handleSelectAll);
    $('#selectAllAttendance').on('change', handleSelectAllAttendance);
    $('#toggleStagingSelectionMode').on('click', toggleStagingSelectionMode);
    $('#useFullMonthData').on('change', function() {
        console.log('useFullMonthData checkbox changed!');
        toggleCustomDateRange();
    });
    $('#transferSelectedToStagingBtn').on('click', transferSelectedToStaging);
    $('#clearBtn').on('click', clearFilters);
    $('#busCode').on('change', function() {
        loadEmployees();
        loadShifts();
    });

    // Monthly tab event handlers
    $('#busCodeMonthly').on('change', loadAvailableMonths);
    $('#exportMonthlyBtn').on('click', handleMonthlyExport);

    // Monthly Grid Sync event handlers
    $('#toggleMonthlyGridSyncMode').on('click', toggleMonthlyGridSyncMode);
    $('#toggleMonthlyGridSyncModeHeader').on('click', toggleMonthlyGridSyncMode);
    $('#syncMonthlyGridToSheetBtn').on('click', syncMonthlyGridToSpreadsheet);
    
    // Enable Monthly Grid Sync checkbox handler
    $('#enableMonthlyGridSync').on('change', function() {
        const isEnabled = $(this).prop('checked');
        const syncActions = $('#monthlyGridSyncActions');
        
        if (isEnabled) {
            syncActions.show();
            updateMonthlyGridSyncInfo();
            showAlert('Grid sync functionality enabled. Activate sync mode to select rows.', 'info');
        } else {
            syncActions.hide();
            showAlert('Grid sync functionality disabled.', 'info');
        }
    });
    
    // Debug sync mode button
    $('#debugSyncMode').on('click', function() {
        console.log('=== SYNC MODE DEBUG ===');
        console.log('monthlyGridSyncModeActive:', monthlyGridSyncModeActive, '(type:', typeof monthlyGridSyncModeActive, ')');
        console.log('currentMonthlyGridData exists:', !!currentMonthlyGridData);
        console.log('Enable sync checkbox checked:', $('#enableMonthlyGridSync').prop('checked'));
        console.log('Sync actions visible:', $('#monthlyGridSyncActions').is(':visible'));
        console.log('Sync controls visible:', $('#monthlyGridSyncControls').is(':visible'));
        console.log('Number of checkboxes in DOM:', $('.monthly-grid-row-checkbox').length);
        console.log('Number of rows in table:', $('#attendanceTableBody tr').length);
        console.log('Header checkbox exists:', $('#selectAllMonthlyGrid').length > 0);
        console.log('=== END DEBUG ===');
        
        // Provide user-friendly feedback
        let debugMessage = 'Debug Info:\n';
        debugMessage += `• Sync Mode: ${monthlyGridSyncModeActive ? '✅ ON' : '❌ OFF'}\n`;
        debugMessage += `• Grid Data: ${currentMonthlyGridData ? '✅ Loaded' : '❌ Not Loaded'}\n`;
        debugMessage += `• Enable Sync: ${$('#enableMonthlyGridSync').prop('checked') ? '✅ Checked' : '❌ Unchecked'}\n`;
        debugMessage += `• Checkboxes: ${$('.monthly-grid-row-checkbox').length} found\n`;
        debugMessage += `• Rows: ${$('#attendanceTableBody tr').length} total`;
        
        showAlert(debugMessage, 'info');
        
        // Force re-render if needed
        if (currentMonthlyGridData && monthlyGridSyncModeActive) {
            console.log('Force re-rendering grid with sync...');
            displayMonthlyGridWithSync(currentMonthlyGridData);
        } else if (currentMonthlyGridData && !monthlyGridSyncModeActive) {
            console.log('Sync mode not active, displaying regular grid');
            displayMonthlyGrid(currentMonthlyGridData);
        } else {
            console.log('No grid data available for re-render');
        }
    });

    // Force render sync button
    $('#forceRenderSync').on('click', function() {
        console.log('=== FORCE RENDER SYNC ACTIVATED ===');
        if (!currentMonthlyGridData) {
            showAlert('❌ No monthly grid data available. Please load a monthly grid first.', 'warning');
            return;
        }
        
        if (!$('#enableMonthlyGridSync').prop('checked')) {
            showAlert('❌ Please enable "Enable grid sync functionality" first.', 'warning');
            return;
        }
        
        // Force set sync mode to true
        monthlyGridSyncModeActive = true;
        
        // Update UI state
        const button = $('#toggleMonthlyGridSyncMode');
        const headerButton = $('#toggleMonthlyGridSyncModeHeader');
        
        button.removeClass('btn-warning').addClass('btn-sync-active');
        button.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        
        headerButton.removeClass('btn-warning').addClass('btn-sync-active');
        headerButton.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Mode Sinkronisasi ON');
        
        $('#monthlyGridSyncControls').addClass('monthly-grid-sync-active');
        $('#monthlyGridSyncActions').show();
        
        // Force render with sync
        console.log('Force rendering grid with sync mode...');
        displayMonthlyGridWithSync(currentMonthlyGridData);
        
        // Auto-sync employee list
        autoSyncEmployeeListWithMonthlyGrid();
        
        // Verify after force render
        setTimeout(() => {
            const checkboxCount = $('.monthly-grid-row-checkbox').length;
            const headerCheckbox = $('#selectAllMonthlyGrid').length;
            
            showAlert(`🔧 Force render completed! Found ${checkboxCount} row checkboxes and ${headerCheckbox} header checkbox.`, 
                     checkboxCount > 0 ? 'success' : 'warning');
            
            console.log('=== FORCE RENDER VERIFICATION ===');
            console.log('Row checkboxes:', checkboxCount);
            console.log('Header checkbox:', headerCheckbox);
            console.log('monthlyGridSyncModeActive:', monthlyGridSyncModeActive);
            console.log('=== END FORCE RENDER VERIFICATION ===');
        }, 500);
    });

    // Test date filter button
    $('#testDateFilter').on('click', function() {
        console.log('=== TEST DATE FILTER ===');
        console.log('useFullMonthData checkbox checked:', $('#useFullMonthData').prop('checked'));
        console.log('customDateRangeSection exists:', $('#customDateRangeSection').length);
        console.log('customDateRangeSection visible:', $('#customDateRangeSection').is(':visible'));
        console.log('customDateRangeSection2 exists:', $('#customDateRangeSection2').length);
        console.log('customDateRangeSection2 visible:', $('#customDateRangeSection2').is(':visible'));
        console.log('stagingTransferControls visible:', $('#stagingTransferControls').is(':visible'));
        
        // Force toggle the date filter
        const currentState = $('#useFullMonthData').prop('checked');
        $('#useFullMonthData').prop('checked', !currentState).trigger('change');
        
        showAlert(`🔧 Date filter test completed. Check console for details. Toggled to: ${!currentState ? 'Full Month' : 'Custom Range'}`, 'info');
    });

    // Staging configuration event handlers
    $('input[name="stagingType"]').on('change', function() {
        updateMonthlyGridSyncInfo(); // Update button text when staging type changes
    });

    // Initialize UI for default selection mode (checkboxes always visible)
    initializeDefaultSelectionMode();
    
    // Initialize date filter state
    initializeDateFilterState();
    
    console.log('Application initialized');
}

/**
 * Initialize UI for default selection mode (checkboxes hidden initially)
 */
function initializeDefaultSelectionMode() {
    console.log('Initializing default selection mode...');
    
    // Set button to "OFF" state initially
    const button = $('#toggleStagingSelectionMode');
    button.removeClass('btn-success').addClass('btn-primary');
    button.html('<i class="fas fa-check-square me-1"></i>Selection Mode');
    
    // Hide transfer controls
    $('#stagingTransferControls').hide();
    
    // Hide staging selection controls and show default header
    $('#selectAllAttendance, #selectAllAttendanceLabel').hide();
    $('#defaultTableHeader').show();
    
    console.log('Default selection mode initialized - checkboxes hidden initially');
}

/**
 * Initialize date filter state
 */
function initializeDateFilterState() {
    console.log('Initializing date filter state...');
    
    // Ensure the useFullMonthData checkbox is checked by default
    $('#useFullMonthData').prop('checked', true);
    
    // Hide custom date range sections initially
    $('#customDateRangeSection, #customDateRangeSection2').hide();
    
    // Verify elements exist
    console.log('Date filter initialization check:', {
        useFullMonthDataExists: $('#useFullMonthData').length > 0,
        customDateRangeSection1Exists: $('#customDateRangeSection').length > 0,
        customDateRangeSection2Exists: $('#customDateRangeSection2').length > 0,
        transferStartDateExists: $('#transferStartDate').length > 0,
        transferEndDateExists: $('#transferEndDate').length > 0,
        stagingTransferControlsExists: $('#stagingTransferControls').length > 0
    });
    
    console.log('Date filter state initialized');
}

/**
 * Set default date range (last 7 days)
 */
function setDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    $('#endDate').val(formatDate(today));
    $('#startDate').val(formatDate(lastWeek));
}

/**
 * Format date to YYYY-MM-DD
 */
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

/**
 * Handle form submission
 */
function handleFormSubmit(e) {
    e.preventDefault();

    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        showAlert('Start date cannot be later than end date', 'warning');
        return;
    }

    loadAttendanceData();
    loadSummaryData();
}

// Global variable to track DataTable instance
let attendanceDataTable = null;

/**
 * Display attendance data in the table
 */
function displayAttendanceData(data) {
    console.log('Displaying attendance data:', data.length, 'records');

    // Store current data globally for selection functionality
    window.currentAttendanceData = data;

    // Destroy existing DataTable if it exists
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    const tableBody = $('#attendanceTableBody');
    tableBody.empty();

    if (!data || data.length === 0) {
        console.log('No data to display');
        tableBody.html(`
            <tr>
                <td colspan="12" class="text-center text-muted">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    No attendance data found for the selected criteria
                </td>
            </tr>
        `);
        return;
    }

    // Store data globally for sync functionality
    window.currentAttendanceData = data;

    // Add data rows to table body
    data.forEach(function(record, index) {
        const totalHours = (record.RegularHours + record.OvertimeHours).toFixed(2);
        const rowId = `row_${index}`;

        let checkboxHtml = '';
        let rowNumber = index + 1;
        
        // Enhanced debugging and checkbox creation
        console.log(`Row ${index} - Checkbox creation check:`, {
            syncModeActive: syncModeActive,
            stagingSelectionModeActive: stagingSelectionModeActive,
            rowIndex: index
        });
        
        if (syncModeActive) {
            checkboxHtml = `<input type="checkbox" class="form-check-input row-checkbox" data-row-id="${index}" id="check_${index}">`;
            console.log(`✅ Created sync checkbox for row ${index}`);
        } else if (stagingSelectionModeActive) {
            checkboxHtml = `<input type="checkbox" class="form-check-input attendance-row-checkbox" data-row-id="${index}" id="attendance_check_${index}">`;
            console.log(`✅ Created staging checkbox for row ${index}`);
        } else {
            // Normal mode - just show row number
            checkboxHtml = `<span class="text-muted">${rowNumber}</span>`;
            console.log(`ℹ️  Normal mode - showing row number for row ${index}`);
        }
        
        // Enhanced debug logging for all rows (not just first 3)
        console.log(`Row ${index} final HTML:`, {
            checkboxHtml: checkboxHtml,
            hasCheckbox: checkboxHtml.includes('checkbox'),
            hasInput: checkboxHtml.includes('input'),
            className: checkboxHtml.includes('attendance-row-checkbox') ? 'attendance-row-checkbox' : 
                       checkboxHtml.includes('row-checkbox') ? 'row-checkbox' : 'none'
        });

        const row = `
            <tr id="${rowId}" data-row-index="${index}" class="${stagingSelectionModeActive ? 'attendance-row-selectable' : ''}">
                <td style="width: 60px;">
                    ${checkboxHtml}
                </td>
                <td>${record.EmployeeID || ''}</td>
                <td>${record.EmployeeName || ''}</td>
                <td>${record.Date || ''}</td>
                <td>${record.DayOfWeek || ''}</td>
                <td>${record.Shift || ''}</td>
                <td>${record.CheckIn || ''}</td>
                <td>${record.CheckOut || ''}</td>
                <td>${record.RegularHours ? record.RegularHours.toFixed(2) : '0.00'}</td>
                <td>${record.OvertimeHours ? record.OvertimeHours.toFixed(2) : '0.00'}</td>
                <td>${totalHours}</td>
            </tr>
        `;
        tableBody.append(row);
    });

    // Bind row selection events if sync mode is active
    if (syncModeActive) {
        $('.row-checkbox').on('change', handleRowSelection);
        $('#attendanceTable tbody tr').on('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('.row-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
    }

    // Bind row selection events if staging selection mode is active
    if (stagingSelectionModeActive) {
        // Use event delegation to handle dynamic content
        $(document).off('change.stagingSelection').on('change.stagingSelection', '.attendance-row-checkbox', handleAttendanceRowSelection);
        $(document).off('click.stagingSelection').on('click.stagingSelection', '.attendance-row-selectable', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('.attendance-row-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
        
        // Enhanced debug: Check if checkboxes were actually created
        setTimeout(() => {
            const checkboxCount = $('.attendance-row-checkbox').length;
            const syncCheckboxCount = $('.row-checkbox').length;
            const totalRows = $('#attendanceTableBody tr').length;
            
            console.log('=== CHECKBOX CREATION SUMMARY ===');
            console.log(`Mode: stagingSelectionModeActive=${stagingSelectionModeActive}, syncModeActive=${syncModeActive}`);
            console.log(`Rows created: ${totalRows}`);
            console.log(`Staging checkboxes found: ${checkboxCount}`);
            console.log(`Sync checkboxes found: ${syncCheckboxCount}`);
            console.log(`Expected checkboxes: ${stagingSelectionModeActive ? totalRows : 0}`);
            
            if (stagingSelectionModeActive) {
                if (checkboxCount === 0) {
                    console.error('❌ PROBLEM: No staging checkboxes found despite stagingSelectionModeActive=true!');
                    console.error('This indicates checkbox HTML generation failed.');
                    showAlert('Warning: Checkboxes not created. Debugging mode is active - check console for details.', 'warning');
                } else if (checkboxCount !== totalRows) {
                    console.warn(`⚠️  WARNING: Found ${checkboxCount} checkboxes but expected ${totalRows}`);
                } else {
                    console.log('✅ All staging checkboxes created successfully');
                    // Update select all checkbox visibility
                    $('#selectAllAttendance, #selectAllAttendanceLabel').show();
                }
            }
            
            // Sample HTML check
            const firstRow = $('#attendanceTableBody tr:first');
            const firstRowHTML = firstRow.html();
            console.log('First row HTML sample:', firstRowHTML ? firstRowHTML.substring(0, 200) : 'No rows found');
            console.log('================================');
        }, 100); // Small delay to ensure DOM is updated
    } else {
        // Remove staging selection event handlers when not in staging mode
        $(document).off('change.stagingSelection');
        $(document).off('click.stagingSelection');
        console.log('Staging selection event handlers removed');
        
        // Hide select all controls when not in staging mode
        $('#selectAllAttendance, #selectAllAttendanceLabel').hide();
    }

    // Initialize DataTable after data is loaded (if available and not in selection mode)
    if (typeof $.fn.DataTable !== 'undefined' && window.useDataTables !== false && !stagingSelectionModeActive && !syncModeActive) {
        try {
            console.log('Initializing DataTable with', data.length, 'rows...');
            attendanceDataTable = $('#attendanceTable').DataTable({
                "pageLength": 25,
                "order": [[3, "desc"]], // Sort by date descending (adjusted for checkbox column)
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6, 7] } // Disable sorting for checkbox, check in/out columns
                ],
                "destroy": true // Allow re-initialization
            });
            console.log('DataTable initialized successfully');
        } catch (error) {
            console.log('DataTable initialization failed:', error);
            console.log('Continuing without DataTable functionality');
        }
    } else {
        console.log('DataTables disabled due to selection mode or not available - showing plain table');
        // Add basic table styling for better UX
        $('#attendanceTable').addClass('table-responsive');
    }
}

/**
 * Load summary statistics
 */
function loadSummaryData() {
    const params = {
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        bus_code: $('#busCode').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    $.ajax({
        url: '/api/summary',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                displaySummaryData(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load summary data:', error);
        }
    });
}

/**
 * Display summary statistics
 */
function displaySummaryData(data) {
    $('#totalEmployees').text(data.total_employees);
    $('#totalRecords').text(data.total_records);
    $('#totalRegularHours').text(data.total_regular_hours);
    $('#totalOvertimeHours').text(data.total_overtime_hours);
    $('#avgRegularHours').text(data.avg_regular_hours);
    $('#avgOvertimeHours').text(data.avg_overtime_hours);

    $('#summarySection').show();
}

/**
 * Load employees list
 */
function loadEmployees() {
    const busCode = $('#busCode').val();
    const params = busCode ? { bus_code: busCode } : {};

    $.ajax({
        url: '/api/employees',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                populateEmployeeSelect(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load employees:', error);
        }
    });
}

/**
 * Populate employee select dropdown
 */
function populateEmployeeSelect(employees) {
    const select = $('#employeeSelect');
    select.empty();
    select.append('<option value="">All Employees</option>');

    employees.forEach(function(employee) {
        select.append(`<option value="${employee.EmployeeID}">${employee.EmployeeName} (${employee.EmployeeID})</option>`);
    });
}

/**
 * Load shifts list
 */
function loadShifts() {
    const busCode = $('#busCode').val();
    const params = busCode ? { bus_code: busCode } : {};

    $.ajax({
        url: '/api/shifts',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                populateShiftSelect(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load shifts:', error);
        }
    });
}

/**
 * Populate shift select dropdown
 */
function populateShiftSelect(shifts) {
    const select = $('#shiftSelect');
    select.empty();
    select.append('<option value="">All Shifts</option>');

    shifts.forEach(function(shift) {
        if (shift.Shift) {
            select.append(`<option value="${shift.Shift}">${shift.Shift}</option>`);
        }
    });
}

/**
 * Handle export to Excel
 */
function handleExport() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    const params = {
        start_date: startDate,
        end_date: endDate,
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('Export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Handle JSON export for custom tab
 */
function handleJsonExport() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    const params = {
        start_date: startDate,
        end_date: endDate,
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val(),
        format: 'json'
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportJsonBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('JSON export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Clear all filters
 */
function clearFilters() {
    console.log('Clearing filters...');

    $('#filterForm')[0].reset();
    setDefaultDates();
    $('#summarySection').hide();

    // Clear table
    if (attendanceDataTable) {
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    // Hide monthly grid sync controls and header toggle
    $('#monthlyGridSyncControls').hide();
    $('#monthlyGridSyncToggleContainer').hide();
    
    $('#attendanceTableBody').html(`
        <tr>
            <td colspan="10" class="text-center text-muted">
                <i class="fas fa-info-circle me-2"></i>
                Please select date range and click "Generate Report" to view attendance data
            </td>
        </tr>
    `);

    // Reload dropdowns
    loadEmployees();
    loadShifts();

    showAlert('Filters cleared', 'info');
}

/**
 * Load employee charge job data from Google Apps Script API
 */
function loadEmployeeChargeJobData() {
    console.log('Loading employee charge job data...');
    
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '/api/employee-charge-jobs',
            method: 'GET',
            success: function(response) {
                console.log('Employee charge job data response:', response);
                
                if (response.success) {
                    employeeChargeJobData = response.data;
                    console.log(`Loaded charge job data for ${response.total_records} employees`);
                    resolve(employeeChargeJobData);
                } else {
                    console.error('Failed to load employee charge job data:', response.error);
                    employeeChargeJobData = {};
                    reject(response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading employee charge job data:', {xhr, status, error});
                employeeChargeJobData = {};
                reject(error);
            }
        });
    });
}

/**
 * Get charge job data for an employee by name or ID
 */
function getEmployeeChargeJobData(employeeName, employeeId = null) {
    if (!employeeChargeJobData) {
        return {
            task_code: '',
            station_code: '',
            machine_code: '',
            expense_code: '',
            raw_charge_job: ''
        };
    }
    
    // Try exact match by employee ID first (if provided)
    if (employeeId && employeeChargeJobData[employeeId]) {
        return employeeChargeJobData[employeeId];
    }
    
    // Try exact match by employee name
    if (employeeChargeJobData[employeeName]) {
        return employeeChargeJobData[employeeName];
    }
    
    // Try case-insensitive match
    const nameUpper = employeeName.toUpperCase();
    for (const [key, data] of Object.entries(employeeChargeJobData)) {
        if (key.toUpperCase() === nameUpper) {
            return data;
        }
    }
    
    // Try partial match (for names that might be slightly different)
    for (const [key, data] of Object.entries(employeeChargeJobData)) {
        if (key.toUpperCase().includes(nameUpper) || nameUpper.includes(key.toUpperCase())) {
            return data;
        }
    }
    
    // Try matching by employee_name field within the data
    for (const [key, data] of Object.entries(employeeChargeJobData)) {
        if (data.employee_name && 
            (data.employee_name.toUpperCase() === nameUpper || 
             data.employee_name.toUpperCase().includes(nameUpper) ||
             nameUpper.includes(data.employee_name.toUpperCase()))) {
            return data;
        }
    }
    
    // Return empty data structure if no match found
    return {
        task_code: '',
        station_code: '',
        machine_code: '',
        expense_code: '',
        raw_charge_job: ''
    };
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
    if (show) {
        $('#loadingIndicator').show();
    } else {
        $('#loadingIndicator').hide();
    }
}

/**
 * Get month name from month number
 */
function getMonthName(month) {
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month - 1] || 'Unknown';
}

/**
 * Show alert message
 */
function showAlert(message, type) {
    // Remove existing alerts
    $('.alert').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container-fluid').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

/**
 * Load available months from the API
 */
function loadAvailableMonths() {
    const busCode = 'PTRJ'; // Default to PTRJ
    const params = { bus_code: busCode };

    console.log('Loading available months with params:', params);
    
    // Show improved loading indicator
    $('#monthsContainer').html(`
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 class="text-primary">Memuat Bulan Tersedia...</h5>
            <p class="text-muted">Sedang mengambil data bulan yang memiliki record absensi</p>
        </div>
    `);

    $.ajax({
        url: '/api/months',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('API response:', response);

            if (response.success) {
                availableMonths = response.data;
                console.log('Available months loaded:', availableMonths.length);
                displayAvailableMonths(response.data);
                
                // Show success message with better styling
                showAlert(`✅ Berhasil memuat ${response.data.length} bulan dengan data absensi`, 'success');
            } else {
                console.error('API returned error:', response.error);
                displayMonthsError('Error loading available months: ' + response.error);
                showAlert('❌ Gagal memuat daftar bulan: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr, status, error});
            console.error('Response text:', xhr.responseText);

            let errorMessage = 'Failed to load available months: ' + error;
            if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = 'Error: ' + (errorResponse.error || error);
                } catch (e) {
                    errorMessage = 'Server error: ' + xhr.status;
                }
            }

            displayMonthsError(errorMessage);
            showAlert('❌ ' + errorMessage, 'danger');
        }
    });
}

/**
 * Display error state for months loading
 */
function displayMonthsError(errorMessage) {
    const container = $('#monthsContainer');
    container.html(`
        <div class="col-12">
            <div class="alert alert-danger text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                <h5 class="alert-heading">Gagal Memuat Daftar Bulan</h5>
                <p class="mb-3">${errorMessage}</p>
                <hr>
                <p class="mb-0">
                    <button class="btn btn-outline-danger" onclick="loadAvailableMonths()">
                        <i class="fas fa-refresh me-2"></i>Coba Lagi
                    </button>
                </p>
            </div>
        </div>
    `);
}

/**
 * Display available months as clickable cards
 */
function displayAvailableMonths(months) {
    console.log('Displaying months:', months);
    const container = $('#monthsContainer');
    container.empty();

    if (!months || months.length === 0) {
        console.log('No months to display');
        container.html(`
            <div class="col-12">
                <div class="text-center text-muted">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p>No months with attendance data found</p>
                    <small>Check console for debugging information</small>
                </div>
            </div>
        `);
        $('#monthsGrid').show();
        return;
    }

    console.log(`Creating ${months.length} month cards`);

    months.forEach(function(month, index) {
        console.log(`Creating card for month ${index + 1}:`, month);

        const monthCard = `
            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                <div class="card month-card h-100" data-year="${month.year}" data-month="${month.month}">
                    <div class="card-body text-center d-flex flex-column">
                        <div class="mb-3">
                            <i class="fas fa-calendar-alt text-primary mb-2" style="font-size: 2.5rem;"></i>
                            <h5 class="card-title text-primary fw-bold">
                                ${month.display_name || `${month.month_name} ${month.year}`}
                            </h5>
                        </div>
                        
                        <div class="row text-center mb-3 flex-grow-1">
                            <div class="col-6">
                                <div class="bg-light rounded p-2">
                                    <h6 class="text-success fw-bold mb-0">${month.record_count || 0}</h6>
                                    <small class="text-muted">Records</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="bg-light rounded p-2">
                                    <h6 class="text-info fw-bold mb-0">${month.employee_count || 0}</h6>
                                    <small class="text-muted">Karyawan</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-auto">
                            <p class="card-text mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    ${month.first_date || 'N/A'} - ${month.last_date || 'N/A'}
                                </small>
                            </p>
                            <div class="btn-hover-indicator">
                                <small class="text-primary">
                                    <i class="fas fa-mouse-pointer me-1"></i>
                                    Klik untuk lihat laporan
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.append(monthCard);
    });

    console.log('Month cards created, adding click handlers...');

    // Add click handlers to month cards
    $('.month-card').on('click', function() {
        const year = $(this).data('year');
        const month = $(this).data('month');
        console.log(`Month card clicked: ${month}/${year}`);
        selectMonth(year, month, $(this));
    });

    $('#monthsGrid').show();
    $('#attendanceColorLegend').show(); // Show color legend for monthly grid
    console.log('Months grid displayed');
}

/**
 * Handle month selection
 */
function selectMonth(year, month, cardElement) {
    // Show immediate visual feedback
    cardElement.find('.card-body').append(`
        <div class="loading-overlay">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `);
    
    // Update visual selection
    $('.month-card').removeClass('selected');
    cardElement.addClass('selected');

    // Store current selection
    currentSelectedMonth = { year: year, month: month };

    // Show loading message
    const monthName = getMonthName(month);
    showAlert(`📊 Memuat data absensi untuk ${monthName} ${year}...`, 'info');

    // Show report type options
    showReportTypeOptions(year, month);
}

/**
 * Show report type selection
 */
function showReportTypeOptions(year, month) {
    const monthName = getMonthName(month);
    
    // Show loading state immediately
    $('#monthlySummarySection').hide();
    $('#attendanceTableBody').html(`
        <tr>
            <td colspan="15" class="text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="text-primary">Memuat Data Absensi</h5>
                    <p class="text-muted">${monthName} ${year} - Mengambil data dari database...</p>
                    <div class="progress mt-2" style="width: 250px; height: 6px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </td>
        </tr>
    `);
    
    // Directly load the monthly grid with station categorization
    // No longer show report type options - go straight to the consolidated grid
    loadMonthlyGrid(year, month);
}

/**
 * Load monthly attendance grid
 */
function loadMonthlyGrid(year, month) {
    const busCode = 'PTRJ'; // Default to PTRJ
    const params = {
        year: year,
        month: month,
        bus_code: busCode
    };

    console.log('Loading monthly grid with params:', params);
    showLoading(true);

    // First, load employee charge job data, then load the attendance grid
    loadEmployeeChargeJobData()
        .then(() => {
            console.log('Employee charge job data loaded, now loading attendance grid...');
            return loadAttendanceGridData(params);
        })
        .catch((error) => {
            console.warn('Failed to load employee charge job data, continuing with attendance grid:', error);
            showAlert('Could not load employee job codes. Grid will show without job code columns.', 'warning');
            return loadAttendanceGridData(params);
        });
}

/**
 * Load attendance grid data from API
 */
function loadAttendanceGridData(params) {
    $.ajax({
        url: '/api/monthly-grid',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('Monthly grid response:', response);

            if (response.success) {
                // Remove loading overlays
                $('.loading-overlay').remove();
                
                // Show sync controls
                $('#monthlyGridSyncControls').show();
                
                // Show sync toggle in main table header
                $('#monthlyGridSyncToggleContainer').show();
                
                // Use sync-aware display function with enhanced checking
                console.log('=== GRID DISPLAY SELECTION ===');
                console.log('monthlyGridSyncModeActive:', monthlyGridSyncModeActive);
                console.log('typeof monthlyGridSyncModeActive:', typeof monthlyGridSyncModeActive);
                console.log('Enable sync checkbox checked:', $('#enableMonthlyGridSync').prop('checked'));
                
                if (monthlyGridSyncModeActive === true) {
                    console.log('✅ Using displayMonthlyGridWithSync');
                    displayMonthlyGridWithSync(response.data);
                } else {
                    console.log('❌ Using regular displayMonthlyGrid');
                    displayMonthlyGrid(response.data);
                }
                console.log('=== END GRID DISPLAY SELECTION ===');
                
                // Display summary for both sync and non-sync modes
                displayMonthlyGridSummary(response.data);
                
                // Show summary section with animation
                $('#monthlySummarySection').fadeIn('slow');
                
                // Show success message
                showAlert(`✅ Data absensi ${response.data.month_name} ${response.data.year} berhasil dimuat`, 'success');
            } else {
                console.error('API returned error:', response.error);
                displayGridError('Error loading monthly grid: ' + response.error);
                showAlert('❌ Gagal memuat grid absensi: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error loading monthly grid:', {xhr, status, error});
            console.error('Response text:', xhr.responseText);

            let errorMessage = 'Failed to load monthly grid: ' + error;
            if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = 'Error: ' + (errorResponse.error || error);
                    console.error('Parsed error response:', errorResponse);
                } catch (e) {
                    errorMessage = `Server error: ${xhr.status} - ${error}`;
                }
            }

            // Remove loading overlays
            $('.loading-overlay').remove();
            
            displayGridError(errorMessage);
            showAlert('❌ ' + errorMessage, 'danger');
        }
    });
}

/**
 * Display error state for grid loading
 */
function displayGridError(errorMessage) {
    $('#attendanceTableBody').html(`
        <tr>
            <td colspan="15" class="text-center py-5">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3 text-danger"></i>
                    <h5 class="alert-heading">Gagal Memuat Data Grid</h5>
                    <p class="mb-3">${errorMessage}</p>
                    <hr>
                    <p class="mb-0">
                        <button class="btn btn-outline-danger" onclick="loadMonthlyGrid(currentSelectedMonth.year, currentSelectedMonth.month)">
                            <i class="fas fa-refresh me-2"></i>Coba Lagi
                        </button>
                    </p>
                </div>
            </td>
        </tr>
    `);
}

/**
 * Display monthly attendance grid grouped by stations
 * REMOVED: Station categorization is now integrated into the main displayMonthlyGrid function
 */
/*
function displayMonthlyGridByStation(gridData) {
    // Function removed - station categorization integrated into main displayMonthlyGrid
}
*/

/**
 * Display monthly attendance grid
 */
function displayMonthlyGrid(gridData) {
    console.log('Displaying monthly grid:', gridData);
    
    // Store data globally for sync functionality
    currentMonthlyGridData = gridData;
    
    // Completely disable DataTables for monthly grids
    window.useDataTables = false;
    
    // Destroy any existing DataTable to prevent conflicts
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable for monthly grid...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    // Update title with data availability indicator
    let titleHtml = `<i class="fas fa-table me-2"></i>${gridData.month_name} ${gridData.year} - Enhanced Daily Attendance Grid`;
    
    // Add data availability indicator if available
    if (gridData.data_availability && gridData.data_availability.latest_available_date) {
        const latestDate = gridData.data_availability.latest_available_date;
        const hasUnavailableDates = gridData.data_availability.has_unavailable_dates;
        
        if (hasUnavailableDates) {
            titleHtml += `<br><small class="text-warning">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Data available up to: <strong>${latestDate}</strong> 
                (${gridData.data_availability.available_days_count}/${gridData.data_availability.total_days_in_month} days)
            </small>`;
        } else {
            titleHtml += `<br><small class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                Complete data available (${gridData.data_availability.available_days_count}/${gridData.data_availability.total_days_in_month} days)
            </small>`;
        }
    }
    
    $('#monthlyReportTitle').html(titleHtml);

    // Create grid table header
    let headerHtml = `
        <tr>
            <th style="min-width: 50px; position: sticky; top: 0; left: 0; background: #212529; z-index: 20;">No</th>
            <th style="min-width: 120px; position: sticky; top: 0; left: 50px; background: #212529; z-index: 20;">Employee ID</th>
            <th style="min-width: 120px; position: sticky; top: 0; left: 170px; background: #212529; z-index: 20; color: #17a2b8;">PTRJ Employee ID</th>
            <th style="min-width: 200px; position: sticky; top: 0; left: 290px; background: #212529; z-index: 20;">Employee Name</th>
    `;

    // Add day columns with day names and data availability indicators
    for (let day = 1; day <= gridData.days_in_month; day++) {
        const date = new Date(gridData.year, gridData.month - 1, day);
        const dayOfWeek = date.getDay(); // 0=Sunday, 6=Saturday
        const isSunday = dayOfWeek === 0;
        const dayName = getDayNameIndonesian(gridData.year, gridData.month, day);
        
        // Check if data is available for this date
        const dateStr = `${gridData.year}-${String(gridData.month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const isDataAvailable = !gridData.data_availability || 
                               !gridData.data_availability.available_dates || 
                               gridData.data_availability.available_dates.includes(dateStr);
        
        // Check if this day is a national holiday by looking at the first employee's data
        let isNationalHoliday = false;
        let holidayInfo = '';
        if (gridData.grid_data && gridData.grid_data.length > 0) {
            const firstEmployee = gridData.grid_data[0];
            const dayData = firstEmployee.days[day.toString()];
            if (dayData && dayData.is_national_holiday) {
                isNationalHoliday = true;
                holidayInfo = dayData.holiday_info || '';
            }
        }
        
        // Set background color with data availability priority
        let backgroundColor = '#212529'; // Default dark
        let textColor = 'white';
        let additionalStyle = '';
        let headerClass = '';
        
        if (!isDataAvailable) {
            // Data unavailable - highest priority styling
            backgroundColor = '#6c757d'; // Gray for unavailable data
            textColor = '#e9ecef';
            additionalStyle = 'font-weight: bold; opacity: 0.7;';
            headerClass = 'header-data-unavailable';
        } else if (isSunday || isNationalHoliday) {
            backgroundColor = '#dc3545'; // Red for Sunday or National Holiday
            textColor = 'white';
            additionalStyle = 'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);';
        }
        
        // Create header with holiday info and data availability indicator
        let headerContent = `<div>${day}</div><div style="font-size: 0.7rem; color: #adb5bd;">${dayName}</div>`;
        
        if (!isDataAvailable) {
            headerContent += `<div style="font-size: 0.6rem; color: #e9ecef; margin-top: 2px;" title="Data not available for this date">N/A</div>`;
        } else if (isNationalHoliday && holidayInfo) {
            headerContent += `<div style="font-size: 0.6rem; color: #ffeb3b; margin-top: 2px;" title="${holidayInfo}">🎉 LIBUR</div>`;
        }
        
        headerHtml += `<th style="min-width: 100px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: ${backgroundColor}; z-index: 10; color: ${textColor}; ${additionalStyle}" ${headerClass ? `class="${headerClass}"` : ''}>
            ${headerContent}
        </th>`;
    }
    
    // Add totals columns
    headerHtml += `
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>DAYS</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Total</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>REG</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>OT</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
    `;

    headerHtml += '</tr>';

    // Create grid table body with enhanced format
    let bodyHtml = '';
    
    // Process each employee
    gridData.grid_data.forEach(function(employee) {
        // Get charge job data for this employee (using both name and ID for better matching)
        const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName, employee.EmployeeID);
        
        let rowHtml = `
            <tr>
                <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.No}</td>
                <td style="position: sticky; left: 50px; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.EmployeeID}</td>
                <td style="position: sticky; left: 170px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center; color: #17a2b8; font-weight: 500;">${employee.PTRJEmployeeID || 'N/A'}</td>
                <td style="position: sticky; left: 290px; background: white; z-index: 5; border-right: 2px solid #dee2e6; font-weight: 500; padding-left: 20px;">${employee.EmployeeName}</td>
        `;

        // Track totals for this employee
        let totalWorkingDays = 0;
        let totalRegularHours = 0;
        let totalOvertimeHours = 0;

        // Add working hours for each day with enhanced format
        for (let day = 1; day <= gridData.days_in_month; day++) {
            const dayData = employee.days[day.toString()];
            
            if (!dayData) {
                // No data for this day
                const date = new Date(gridData.year, gridData.month - 1, day);
                const isSunday = date.getDay() === 0;
                const cellContent = isSunday ? 'OFF' : '-';
                const cellClass = isSunday ? 'hours-off' : 'hours-absent';
                rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${cellContent}</td>`;
                continue;
            }

            // Handle enhanced data format
            if (typeof dayData === 'object') {
                const normalHours = dayData.normal_hours || 0;
                const overtimeHours = dayData.overtime_hours || 0;
                const status = dayData.status || 'absent';
                const hasOvertime = dayData.has_overtime || false;
                const needsVerification = dayData.needs_verification || false;
                const isSunday = dayData.is_sunday || false;
                const isSaturday = dayData.is_saturday || false;

                let cellContent = '';
                let cellClass = '';

                // Format cell content - ALWAYS show (normal | overtime) format
                // Enhanced cell content formatting with absence, leave and ALFA logic
                // PRIORITY 0: Data unavailable (highest priority)
                if (status === 'data_unavailable' || dayData.is_data_unavailable) {
                    // Data not available for this date - CRITICAL: Should not be selectable for staging
                    cellContent = 'N/A';
                    cellClass = 'hours-data-unavailable';
                    // Add data attribute to prevent selection for staging operations
                    cellClass += ' data-unavailable-cell';
                }
                // PRIORITY 1: HR_T_Absence data (highest priority)
                else if (status === 'absence_unpaid_alfa' && dayData.absence_data) {
                    // Unpaid leave displays as ALFA with red background
                    cellContent = 'ALFA';
                    cellClass = 'absence-unpaid-alfa';
                } else if (status === 'absence_other' && dayData.absence_data) {
                    // Other absence types show actual absence type
                    cellContent = dayData.absence_data.AbsType || 'ABSENCE';
                    cellClass = getAbsenceTypeClass(dayData.absence_data.AbsType);
                } 
                // PRIORITY 2: HR_H_Leave data (secondary priority)
                else if (status === 'on_leave' && dayData.leave_data) {
                    // Display leave type code for leave days
                    cellContent = dayData.leave_data.leave_type_code || 'LEAVE';
                    cellClass = 'hours-on-leave';
                    
                    // Add specific leave type class for color coding
                    if (dayData.leave_data.leave_type_code) {
                        cellClass += ` leave-${dayData.leave_data.leave_type_code.toLowerCase()}`;
                    }
                } 
                // PRIORITY 3: ALFA for absent without leave/absence
                else if (status === 'alfa') {
                    // Display ALFA for absent without leave
                    cellContent = 'ALFA';
                    cellClass = 'hours-alfa';
                } else if (status === 'off') {
                    cellContent = 'OFF';
                    cellClass = 'hours-off';
                } else if (status === 'absent') {
                    cellContent = '-';
                    cellClass = 'hours-absent';
                } else {
                    // Always display in (normal) | (overtime) format
                    cellContent = `(${normalHours}) | (${overtimeHours})`;
                    
                    // Determine cell class based on user requirements
                    if (status === "partial_check_in_only") {
                        // Blue: partial attendance - check in only, default hours applied
                        cellClass = 'hours-partial-check-in-only';
                    } else if (status === "partial_check_out_only") {
                        // Blue: partial attendance - check out only, default hours applied
                        cellClass = 'hours-partial-check-out-only';
                    } else if (needsVerification) {
                        // Pink: needs verification - employee forgot to check in/out
                        cellClass = 'hours-needs-verification'; 
                    } else if (normalHours > 0 && overtimeHours > 0) {
                        // Green: ada jam kerja normal, ada overtime, check in ada, check out ada
                        cellClass = 'hours-normal-overtime';
                    } else if (normalHours === 0 && overtimeHours > 0) {
                        // Yellow: cuman overtime, checkin dan checkout lengkap
                        cellClass = 'hours-overtime-only';
                    } else if (normalHours > 0 && overtimeHours === 0) {
                        // Light green: normal hours only
                        cellClass = 'hours-normal';
                    } else {
                        // Fallback
                        cellClass = 'hours-absent';
                    }
                }

                // Update totals
                if (normalHours > 0 || overtimeHours > 0) {
                    totalWorkingDays++;
                    totalRegularHours += normalHours;
                    totalOvertimeHours += overtimeHours;
                }

                rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${cellContent}</td>`;
            } else {
                // Legacy format fallback - convert to (normal | overtime) format
                const cellContent = dayData || '-';
                let displayContent = '';
                let cellClass = '';
                
                if (cellContent === 'OFF') {
                    displayContent = 'OFF';
                    cellClass = 'hours-off';
                } else if (cellContent === '-') {
                    displayContent = '-';
                    cellClass = 'hours-absent';
                } else {
                    // Convert legacy format to (normal | overtime)
                    const hours = parseFloat(cellContent) || 0;
                    displayContent = `(${hours}) | (0)`;
                    cellClass = 'hours-normal';
                    
                    if (hours > 0) {
                        totalWorkingDays++;
                        totalRegularHours += hours;
                    }
                }
                
                rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${displayContent}</td>`;
            }
        }

        // Add totals cells
        rowHtml += `
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalWorkingDays}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalRegularHours.toFixed(1)}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalOvertimeHours.toFixed(1)}</td>
        `;

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    // Update table
    $('#attendanceTable thead').html(headerHtml);
    $('#attendanceTableBody').html(bodyHtml);

    // Apply grid styling but NO DataTables
    $('#attendanceTable').addClass('attendance-grid');
    $('#attendanceTable').removeClass('dataTable');

    console.log('Enhanced monthly grid displayed with new (normal | overtime) format');

    // Use shared summary display function
    displayMonthlyGridSummary(gridData);
}

/**
 * Helper function to get CSS class for absence types
 */
function getAbsenceTypeClass(absType) {
    if (!absType) return 'absence-other';
    
    const type = absType.toLowerCase();
    if (type === 'sick') {
        return 'absence-sick';
    } else if (type === 'duty') {
        return 'absence-duty';
    } else if (type === 'menstrual leave') {
        return 'absence-menstrual';
    } else if (type === 'additional leave') {
        return 'absence-additional';
    } else if (type === 'unpaid leave') {
        return 'absence-unpaid-alfa';
    } else {
        return 'absence-other';
    }
}

/**
 * Helper function to determine employee station from name
 * This is a simplified approach - can be enhanced with actual station data lookup
 */
function getEmployeeStationFromName(employeeName) {
    // This is a placeholder - in a real implementation, we would call the backend
    // to get the actual station data. For now, we'll use some simple pattern matching
    const name = employeeName.toUpperCase();
    
    // You can enhance this logic based on your station data patterns
    if (name.includes('DEDY') || name.includes('TIGO') || name.includes('DANANG') || 
        name.includes('JUMHADI') || name.includes('RIDHO') || name.includes('LIO')) {
        return 'STATION GRADER';
    } else if (name.includes('HUSAINI') || name.includes('ADI APRIADI') || name.includes('DAFID')) {
        return 'STATION LOADING RAMP';
    } else if (name.includes('RASTANTO') || name.includes('NURSAMSI') || name.includes('DARMA')) {
        return 'STATION STERILIZER';
    } else if (name.includes('AZRUL') || name.includes('ANDRI JULIANDRI')) {
        return 'STATION THRESING';
    } else if (name.includes('FERDY')) {
        return 'STATION DRIVER PURCHASING DAN GENERAL';
    } else if (name.includes('RIZKY SUGANDA') || name.includes('ANWAR') || name.includes('ARIANTO')) {
        return 'STATION EFB PRESS';
    } else if (name.includes('DERRY') || name.includes('YULIZAR') || name.includes('REZA')) {
        return 'STATION PRESS';
    } else if (name.includes('SYAHRIL') || name.includes('SIAGA') || name.includes('RIVALDI')) {
        return 'STATION CLARIFIKASI';
    } else if (name.includes('ANDRIANO') || name.includes('EDDO') || name.includes('HERU')) {
        return 'STATION KERNEL';
    } else if (name.includes('PORDIMAN') || name.includes('RENDY NOPRIYANDI') || name.includes('ADE PRASETYA')) {
        return 'STATION BOILER';
    } else if (name.includes('RENDY GUSTOYO') || name.includes('WAHYU') || name.includes('RIDHO PURNAMA')) {
        return 'STATION WTP';
    } else if (name.includes('HELMI') || name.includes('FIKRIANSYAH') || name.includes('SUHARTOMO')) {
        return 'STATION ENGINE ROOM';
    } else if (name.includes('NAJMI') || name.includes('IRWAN') || name.includes('CHARYADI')) {
        return 'STATION COMPOUND 9234';
    } else if (name.includes('ICHSANUDIN') || name.includes('ATURI') || name.includes('JAYA SAPUTRA')) {
        return 'STATION LABORATORY 240';
    } else if (name.includes('ANDISON') || name.includes('SOFIYAN')) {
        return 'STATION EFFLUENT';
    }
    
    return 'Unknown';
}

/**
 * Get CSS class for attendance status
 */
function getAttendanceStatusClass(status) {
    switch (status) {
        case '1': return 'table-success'; // Present
        case '-': return 'table-danger';  // Absent
        case 'H': return 'table-warning'; // Half day
        case 'S': return 'table-secondary'; // Saturday
        case 'M': return 'table-info';    // Sunday
        case 'C': return 'table-primary'; // Cuti
        default: return '';
    }
}

/**
 * Get day name in Indonesian (abbreviated)
 */
function getDayNameIndonesian(year, month, day) {
    const date = new Date(year, month - 1, day); // month is 0-indexed in JavaScript
    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']; // Sunday=0, Monday=1, etc.
    return dayNames[date.getDay()];
}

/**
 * Get CSS class for working hours display with enhanced status handling
 */
function getWorkingHoursClass(hours, isSaturday, employee, day) {
    // Add type checking and debugging
    if (hours === null || hours === undefined) {
        console.warn('getWorkingHoursClass: hours is null/undefined', {hours, employee: employee?.name, day});
        return 'hours-absent';
    }

    // Handle new object format from enhanced backend
    if (typeof hours === 'object' && hours !== null) {
        // Extract display string from object format
        if (hours.display_format === 'always_both') {
            const normalHours = hours.normal_hours || 0;
            const overtimeHours = hours.overtime_hours || 0;

            // Handle special cases
            if (hours.is_sunday && normalHours === 0 && overtimeHours === 0) {
                return 'hours-off';
            }
            if (hours.is_national_holiday) {
                if (overtimeHours > 0) {
                    return 'hours-overtime-only'; // Yellow for holiday overtime
                } else {
                    return 'hours-off'; // Holiday off
                }
            }
            if (hours.status === 'absent') {
                return 'hours-absent';
            }

            // Convert to string format for processing
            const overtimeDisplay = overtimeHours > 0 ? overtimeHours.toString() : '-';
            hours = `(${normalHours}) | (${overtimeDisplay})`;
        } else {
            console.warn('getWorkingHoursClass: unknown object format', hours);
            return 'hours-absent';
        }
    }

    // Convert to string if it's not already
    if (typeof hours !== 'string' && typeof hours !== 'number') {
        console.warn('getWorkingHoursClass: unexpected hours type', typeof hours, hours);
        return 'hours-absent';
    }
    
    // Get status information if available
    let status = null;
    let checkInOnly = false;
    let checkOutOnly = false;
    let completeRecord = false;
    let regularHours = 0;
    let overtimeHours = 0;
    
    if (employee && employee.days && employee.days[`${day}_status`]) {
        const statusInfo = employee.days[`${day}_status`];
        status = statusInfo.status;
        checkInOnly = statusInfo.check_in_only;
        checkOutOnly = statusInfo.check_out_only;
        completeRecord = statusInfo.complete_record;
        regularHours = statusInfo.regular;
        overtimeHours = statusInfo.overtime;
    }
    
    // Handle special cases first
    if (hours === 'ALFA') {
        return 'hours-alfa';
    } else if (hours === 'OFF') {
        return 'hours-off';
    } else if (hours === '-') {
        return 'hours-absent';
    } else if (typeof hours === 'string' && ['CT', 'H2', 'P1', 'P2', 'P3', 'CB', 'S', 'I'].includes(hours)) {
        // Handle leave type codes
        const leaveTypeClass = `hours-on-leave leave-${hours.toLowerCase()}`;
        return leaveTypeClass;
    }
    
    // Handle incomplete records with specific background colors
    if (checkInOnly || status === 'partial_check_in_only') {
        return 'hours-partial-check-in-only';  // Blue background
    } else if (checkOutOnly || status === 'partial_check_out_only') {
        return 'hours-partial-check-out-only'; // Blue background
    }
    
    // Handle overtime-only records (new status)
    if (status === 'overtime_only') {
        return 'hours-overtime-only'; // Yellow background
    }
    
    // Handle complete records with enhanced color coding
    if (typeof hours === 'string' && hours.includes('|')) {
        const parts = hours.split('|');
        if (parts.length === 2) {
            const regularPart = parts[0].trim().replace(/[()]/g, '');
            const overtimePart = parts[1].trim().replace(/[()]/g, '');
            
            const parsedRegularHours = parseFloat(regularPart) || 0;
            const parsedOvertimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
            
            // NEW: Check for overtime-only records (0 regular hours but has overtime)
            if (parsedRegularHours === 0 && parsedOvertimeHours > 0) {
                return 'hours-overtime-only'; // Yellow background for overtime-only
            }
            
            // Apply the new color scheme requirements:
            
            // 1. Dark Green: Normal hours > 7 AND both check-in/check-out present
            if (parsedRegularHours > 7 && completeRecord) {
                return 'hours-dark-green';
            }
            
            // 2. Light Green: Normal hours present (any amount) AND no overtime
            if (parsedRegularHours > 0 && parsedOvertimeHours === 0) {
                return 'hours-light-green';
            }
            
            // Handle Saturday vs Weekday thresholds for legacy logic
            const threshold = isSaturday ? 5 : 7;
            
            // Exactly 7 hours normal work (Monday-Friday) - Bright green highlighting
            if (parsedRegularHours === 7 && !isSaturday) {
                return 'hours-full'; // Green - Full regular day
            } else if (parsedRegularHours === 5 && isSaturday) {
                return 'hours-full'; // Green - Full regular day for Saturday
            } else if (parsedRegularHours >= threshold && parsedOvertimeHours === 0) {
                return 'hours-full'; // Green - Full regular hours
            } else if (parsedRegularHours > 0) {
                if (parsedOvertimeHours > 0) {
                    return 'hours-overtime'; // Yellow for overtime
                } else {
                    return 'hours-partial'; // Red for partial hours
                }
            }
        }
    } else if (typeof hours === 'string' && hours.match(/^\d+(\.\d+)?$/)) {
        // Legacy format: single number
        const hoursNum = parseFloat(hours);
    } else if (typeof hours === 'number') {
        // Handle numeric hours values
        const hoursNum = hours;
        
        if (isSaturday) {
            if (hoursNum >= 5) {
                return 'hours-full'; // Green - Full day for Saturday
            } else if (hoursNum > 0) {
                return 'hours-partial'; // Red - Partial day for Saturday
            }
        } else {
            if (hoursNum >= 7) {
                return 'hours-full'; // Green - Full day
            } else if (hoursNum > 0) {
                return 'hours-partial'; // Red - Partial day
            }
        }
    }
    
    return 'hours-absent'; // Default for unknown values
}

/**
 * Display monthly report summary and data
 * REMOVED: No longer showing summary reports - only consolidated daily grid with station categorization
 */
/*
function displayMonthlyReport(summary, data) {
    // Function removed - only showing consolidated daily grid
}
*/

/**
 * Handle monthly export
 */
function handleMonthlyExport() {
    if (!currentSelectedMonth) {
        showAlert('Please select a month first', 'warning');
        return;
    }

    const busCode = 'PTRJ'; // Default to PTRJ
    const params = {
        year: currentSelectedMonth.year,
        month: currentSelectedMonth.month,
        bus_code: busCode
    };

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportMonthlyBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('Monthly export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export grid data to Excel
 */
function exportGridToExcel(year, month) {
    const busCode = 'PTRJ';
    const params = new URLSearchParams({
        year: year,
        month: month,
        bus_code: busCode,
        format: 'excel',
        type: 'grid'
    });

    // Create download URL
    const downloadUrl = `/api/export-grid?${params.toString()}`;
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        showAlert('Grid export to Excel initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export grid data to JSON
 */
function exportGridToJSON(year, month) {
    const busCode = 'PTRJ';
    const params = new URLSearchParams({
        year: year,
        month: month,
        bus_code: busCode,
        format: 'json',
        type: 'grid'
    });

    // Create download URL
    const downloadUrl = `/api/export-grid?${params.toString()}`;
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        showAlert('Grid export to JSON initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export station grid data to Excel
 * REMOVED: Station grid functionality consolidated into main grid
 */
/*
function exportStationGridToExcel(year, month) {
    // Function removed - use main grid export functions
}
*/

/**
 * Export station grid data to JSON
 * REMOVED: Station grid functionality consolidated into main grid
 */
/*
function exportStationGridToJSON(year, month) {
    // Function removed - use main grid export functions
}
*/

/**
 * Toggle sync mode
 */
function toggleSyncMode() {
    syncModeActive = !syncModeActive;
    const button = $('#toggleSyncMode');
    const resultsCard = $('.card').last();
    
    if (syncModeActive) {
        button.removeClass('btn-warning').addClass('btn-sync-active');
        button.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        resultsCard.addClass('sync-mode-active');
        
        // Show sync controls
        $('#selectAll, #selectAllLabel').show();
        $('#syncToSheetBtn').show();
        
        // Re-render table if data exists
        if (window.currentAttendanceData) {
            displayAttendanceData(window.currentAttendanceData);
        }
        
        showAlert('Sync mode activated. You can now select rows to sync to spreadsheet.', 'info');
    } else {
        button.removeClass('btn-sync-active').addClass('btn-warning');
        button.html('<i class="fas fa-sync me-1"></i>Sync Mode');
        resultsCard.removeClass('sync-mode-active');
        
        // Hide sync controls
        $('#selectAll, #selectAllLabel').hide();
        $('#syncToSheetBtn').hide();
        
        // Clear selections
        selectedRows.clear();
        
        // Re-render table if data exists
        if (window.currentAttendanceData) {
            displayAttendanceData(window.currentAttendanceData);
        }
        
        showAlert('Sync mode deactivated.', 'info');
    }
}

/**
 * Handle select all checkbox
 */
function handleSelectAll() {
    const isChecked = $('#selectAll').prop('checked');
    $('.row-checkbox').prop('checked', isChecked);
    
    if (isChecked) {
        $('.row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            selectedRows.add(rowIndex);
            $(`#row_${rowIndex}`).addClass('row-selected');
        });
    } else {
        selectedRows.clear();
        $('.row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            $(`#row_${rowIndex}`).removeClass('row-selected');
        });
    }
    
    updateSyncButton();
}

/**
 * Handle individual row selection
 */
function handleRowSelection() {
    const checkbox = $(this);
    const rowIndex = checkbox.data('row-id');
    const isChecked = checkbox.prop('checked');
    
    if (isChecked) {
        selectedRows.add(rowIndex);
        $(`#row_${rowIndex}`).addClass('row-selected');
    } else {
        selectedRows.delete(rowIndex);
        $(`#row_${rowIndex}`).removeClass('row-selected');
    }
    
    // Update select all checkbox
    const totalCheckboxes = $('.row-checkbox').length;
    const checkedCheckboxes = $('.row-checkbox:checked').length;
    $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    
    updateSyncButton();
}

/**
 * Update sync button state
 */
function updateSyncButton() {
    const syncBtn = $('#syncToSheetBtn');
    if (selectedRows.size > 0) {
        syncBtn.html(`<i class="fas fa-cloud-upload-alt me-1"></i>Sync ${selectedRows.size} Records`);
        syncBtn.prop('disabled', false);
    } else {
        syncBtn.html('<i class="fas fa-cloud-upload-alt me-1"></i>Sync to Spreadsheet');
        syncBtn.prop('disabled', true);
    }
}

/**
 * Toggle staging selection mode
 */
function toggleStagingSelectionMode() {
    stagingSelectionModeActive = !stagingSelectionModeActive;
    const button = $('#toggleStagingSelectionMode');
    const transferControls = $('#stagingTransferControls');

    console.log('Toggling staging selection mode to:', stagingSelectionModeActive); // Debug log

    if (stagingSelectionModeActive) {
        button.removeClass('btn-primary').addClass('btn-success');
        button.html('<i class="fas fa-check-square me-1"></i>Selection Mode ON');
        transferControls.show();

        // Show staging selection controls and hide default header
        $('#selectAllAttendance, #selectAllAttendanceLabel').show();
        $('#defaultTableHeader').hide();
        
        // Initialize date filter state when showing transfer controls
        const useFullMonth = $('#useFullMonthData').prop('checked');
        if (!useFullMonth) {
            $('#customDateRangeSection, #customDateRangeSection2').show();
            console.log('Custom date range sections shown because useFullMonth is false');
        } else {
            $('#customDateRangeSection, #customDateRangeSection2').hide();
            console.log('Custom date range sections hidden because useFullMonth is true');
        }
        
        console.log('Selection controls shown'); // Debug log

        // Re-render table if data exists
        if (window.currentAttendanceData && window.currentAttendanceData.length > 0) {
            console.log('Re-rendering table with', window.currentAttendanceData.length, 'records'); // Debug log
            displayAttendanceData(window.currentAttendanceData);
        } else {
            console.log('No attendance data available for selection mode'); // Debug log
            showAlert('Please load attendance data first by clicking "Buat Laporan" before activating selection mode.', 'warning');
        }

        showAlert('Staging selection mode activated. Select records to transfer to staging.', 'info');
    } else {
        button.removeClass('btn-success').addClass('btn-primary');
        button.html('<i class="fas fa-check-square me-1"></i>Selection Mode');
        transferControls.hide();

        // Hide staging selection controls and show default header
        $('#selectAllAttendance, #selectAllAttendanceLabel').hide();
        $('#defaultTableHeader').show();
        console.log('Selection controls hidden'); // Debug log

        // Clear selections
        attendanceSelectedRows.clear();
        console.log('Selections cleared'); // Debug log

        // Re-render table if data exists
        if (window.currentAttendanceData && window.currentAttendanceData.length > 0) {
            console.log('Re-rendering table without selection mode'); // Debug log
            displayAttendanceData(window.currentAttendanceData);
        }

        showAlert('Staging selection mode deactivated.', 'info');
    }

    updateSelectedRecordsInfo();
    
    // Debug: Log current state
    console.log('Selection mode state:', {
        stagingSelectionModeActive: stagingSelectionModeActive,
        hasData: !!(window.currentAttendanceData && window.currentAttendanceData.length > 0),
        dataLength: window.currentAttendanceData ? window.currentAttendanceData.length : 0
    });
}

/**
 * Handle select all attendance checkbox
 */
function handleSelectAllAttendance() {
    const isChecked = $('#selectAllAttendance').prop('checked');
    $('.attendance-row-checkbox').prop('checked', isChecked);

    if (isChecked) {
        $('.attendance-row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            attendanceSelectedRows.add(rowIndex);
            $(`#row_${rowIndex}`).addClass('table-primary');
        });
    } else {
        attendanceSelectedRows.clear();
        $('.attendance-row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            $(`#row_${rowIndex}`).removeClass('table-primary');
        });
    }

    updateSelectedRecordsInfo();
}

/**
 * Handle attendance row selection
 */
function handleAttendanceRowSelection() {
    console.log('Row selection triggered'); // Debug log
    
    const checkbox = $(this);
    const rowIndex = checkbox.data('row-id');
    const isChecked = checkbox.prop('checked');
    
    console.log(`Row ${rowIndex} selection: ${isChecked}`); // Debug log

    if (isChecked) {
        attendanceSelectedRows.add(rowIndex);
        $(`#row_${rowIndex}`).addClass('table-primary');
        console.log(`Added row ${rowIndex} to selection`); // Debug log
    } else {
        attendanceSelectedRows.delete(rowIndex);
        $(`#row_${rowIndex}`).removeClass('table-primary');
        console.log(`Removed row ${rowIndex} from selection`); // Debug log
    }

    // Update select all checkbox
    const totalCheckboxes = $('.attendance-row-checkbox').length;
    const checkedCheckboxes = $('.attendance-row-checkbox:checked').length;
    $('#selectAllAttendance').prop('checked', checkedCheckboxes === totalCheckboxes);
    
    console.log(`Selection status: ${checkedCheckboxes}/${totalCheckboxes} selected`); // Debug log

    updateSelectedRecordsInfo();
}

/**
 * Update selected records information
 */
function updateSelectedRecordsInfo() {
    const selectedCount = attendanceSelectedRows.size;
    const infoElement = $('#selectedRecordsInfo');
    const detailsElement = $('#selectedRecordsDetails');
    const transferButton = $('#transferSelectedToStagingBtn');

    if (selectedCount === 0) {
        infoElement.text('No records selected');
        detailsElement.empty();
        transferButton.prop('disabled', true);
    } else {
        infoElement.text(`${selectedCount} record(s) selected`);

        // Get unique employees from selected records
        const selectedEmployees = new Set();
        attendanceSelectedRows.forEach(rowIndex => {
            if (window.currentAttendanceData && window.currentAttendanceData[rowIndex]) {
                const record = window.currentAttendanceData[rowIndex];
                selectedEmployees.add(record.EmployeeName || record.EmployeeID);
            }
        });

        detailsElement.html(`
            <small class="text-muted">
                <strong>Employees:</strong> ${Array.from(selectedEmployees).slice(0, 3).join(', ')}
                ${selectedEmployees.size > 3 ? ` and ${selectedEmployees.size - 3} more` : ''}
            </small>
        `);

        transferButton.prop('disabled', false);
    }
}

/**
 * Toggle custom date range visibility
 */
function toggleCustomDateRange() {
    const useFullMonth = $('#useFullMonthData').prop('checked');
    const customSections = $('#customDateRangeSection, #customDateRangeSection2');

    console.log('=== TOGGLE CUSTOM DATE RANGE ===');
    console.log('useFullMonth:', useFullMonth);
    console.log('customSections found:', customSections.length);
    console.log('transferStartDate element exists:', $('#transferStartDate').length > 0);
    console.log('transferEndDate element exists:', $('#transferEndDate').length > 0);

    if (useFullMonth) {
        console.log('✅ Full month mode: Hiding custom date range sections');
        customSections.hide();
        
        // Also ensure the staging transfer controls section is visible
        $('#stagingTransferControls').show();
        
        showAlert('📅 Mode: Include Full Month Data - Seluruh data bulan akan disertakan', 'info');
    } else {
        console.log('📅 Custom date range mode: Showing date range sections');
        
        // Ensure the staging transfer controls section is visible first
        $('#stagingTransferControls').show();
        
        // Then show the date range sections
        customSections.show();
        
        // Force display with additional CSS to ensure visibility
        customSections.css('display', 'block');
        
        console.log('After showing sections:');
        console.log('customDateRangeSection visible:', $('#customDateRangeSection').is(':visible'));
        console.log('customDateRangeSection2 visible:', $('#customDateRangeSection2').is(':visible'));
        
        // Set default dates based on current monthly grid data if available
        if (!$('#transferStartDate').val() || !$('#transferEndDate').val()) {
            let startDate, endDate;
            
            if (currentMonthlyGridData && currentMonthlyGridData.year && currentMonthlyGridData.month) {
                // Use current monthly grid date range
                const year = currentMonthlyGridData.year;
                const month = currentMonthlyGridData.month;
                startDate = new Date(year, month - 1, 1);
                endDate = new Date(year, month, 0);
                
                console.log(`Setting dates from current grid: ${month}/${year}`);
            } else {
                // Fallback to current month
                const today = new Date();
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                
                console.log('Setting dates from current month (fallback)');
            }

            $('#transferStartDate').val(startDate.toISOString().split('T')[0]);
            $('#transferEndDate').val(endDate.toISOString().split('T')[0]);
            
            console.log('Default dates set:', {
                start: $('#transferStartDate').val(),
                end: $('#transferEndDate').val()
            });
        }
        
        showAlert('📅 Mode: Custom Date Range - Hanya data dalam rentang tanggal yang dipilih akan disertakan. Lihat bagian "Transfer Selected Records to Staging" di bawah.', 'warning');
    }
    
    console.log('=== END TOGGLE CUSTOM DATE RANGE ===');
}

/**
 * Transfer selected records to staging
 */
function transferSelectedToStaging() {
    if (attendanceSelectedRows.size === 0) {
        showAlert('Please select records to transfer to staging.', 'warning');
        return;
    }

    const useFullMonth = $('#useFullMonthData').prop('checked');
    const transferButton = $('#transferSelectedToStagingBtn');

    // Get selected records
    const selectedRecords = [];
    const selectedEmployeeIds = new Set();

    attendanceSelectedRows.forEach(rowIndex => {
        if (window.currentAttendanceData && window.currentAttendanceData[rowIndex]) {
            const record = window.currentAttendanceData[rowIndex];
            selectedRecords.push(record);
            selectedEmployeeIds.add(record.EmployeeID);
        }
    });

    if (selectedRecords.length === 0) {
        showAlert('No valid records found for transfer.', 'warning');
        return;
    }

    // Prepare transfer data
    let transferData;

    if (useFullMonth) {
        // Use selective copy API with employee IDs and full month data
        const dates = selectedRecords.map(r => new Date(r.Date));
        const minDate = new Date(Math.min(...dates));
        const maxDate = new Date(Math.max(...dates));

        // Get full month range
        const startDate = new Date(minDate.getFullYear(), minDate.getMonth(), 1);
        const endDate = new Date(maxDate.getFullYear(), maxDate.getMonth() + 1, 0);

        transferData = {
            employee_ids: Array.from(selectedEmployeeIds),
            start_date: startDate.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0],
            bus_code: $('#busCode').val() || 'PTRJ'
        };

        // Use selective copy endpoint
        transferSelectedUsingSelectiveCopy(transferData, transferButton);
    } else {
        // Use custom date range
        const startDate = $('#transferStartDate').val();
        const endDate = $('#transferEndDate').val();

        if (!startDate || !endDate) {
            showAlert('Please specify start and end dates for custom range.', 'warning');
            return;
        }

        transferData = {
            employee_ids: Array.from(selectedEmployeeIds),
            start_date: startDate,
            end_date: endDate,
            bus_code: $('#busCode').val() || 'PTRJ'
        };

        // Use selective copy endpoint
        transferSelectedUsingSelectiveCopy(transferData, transferButton);
    }
}

/**
 * Transfer selected records using selective copy API
 */
function transferSelectedUsingSelectiveCopy(transferData, transferButton) {
    const originalText = transferButton.html();
    transferButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Transferring...');

    $.ajax({
        url: '/api/staging/selective-copy',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(transferData),
        success: function(response) {
            if (response.success) {
                // Build detailed success message
                let successMessage = `<div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Transfer Completed Successfully</h6>
                    <ul class="mb-2">
                        <li><strong>Employees:</strong> ${response.selected_employees}</li>
                        <li><strong>Date Range:</strong> ${response.date_range}</li>`;

                if (response.new_records > 0) {
                    successMessage += `<li><strong>New Records:</strong> ${response.new_records}</li>`;
                }
                if (response.updated_records > 0) {
                    successMessage += `<li><strong>Updated Records:</strong> ${response.updated_records}</li>`;
                }
                if (response.skipped_duplicates > 0) {
                    successMessage += `<li><strong>Duplicates Prevented:</strong> ${response.skipped_duplicates}</li>`;
                }

                successMessage += `<li><strong>Total Processed:</strong> ${response.total_processed}</li>
                    </ul></div>`;

                showAlert(successMessage, 'success', 8000);

                // Clear selections
                attendanceSelectedRows.clear();
                $('.attendance-row-checkbox').prop('checked', false);
                $('#selectAllAttendance').prop('checked', false);
                $('.table-primary').removeClass('table-primary');
                updateSelectedRecordsInfo();

                // Refresh staging data if staging tab is visible
                if ($('#staging-tab').hasClass('active')) {
                    loadStagingData();
                }
            } else {
                showAlert('Transfer failed: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Transfer error:', error);
            showAlert('Transfer failed: ' + error, 'danger');
        },
        complete: function() {
            transferButton.prop('disabled', false).html(originalText);
        }
    });
}

/**
 * Sync selected rows to Google Spreadsheet
 */
function syncToSpreadsheet() {
    const syncUrl = $('#syncUrl').val();
    const enableSync = $('#enableSync').prop('checked');
    
    if (!enableSync) {
        showAlert('Please enable sync functionality first.', 'warning');
        return;
    }
    
    if (!syncUrl) {
        showAlert('Please enter Google Apps Script URL first.', 'warning');
        return;
    }
    
    if (selectedRows.size === 0) {
        showAlert('Please select at least one row to sync.', 'warning');
        return;
    }
    
    // Prepare data for sync
    const dataToSync = [];
    selectedRows.forEach(rowIndex => {
        const record = window.currentAttendanceData[rowIndex];
        if (record) {
            dataToSync.push({
                employeeId: record.EmployeeID || '',
                employeeName: record.EmployeeName || '',
                date: record.Date || '',
                dayOfWeek: record.DayOfWeek || '',
                shift: record.Shift || '',
                checkIn: record.CheckIn || '',
                checkOut: record.CheckOut || '',
                regularHours: record.RegularHours || 0,
                overtimeHours: record.OvertimeHours || 0,
                totalHours: (record.RegularHours || 0) + (record.OvertimeHours || 0)
            });
        }
    });
    
    // Show progress
    const syncBtn = $('#syncToSheetBtn');
    const originalText = syncBtn.html();
    syncBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Syncing...').prop('disabled', true);
    
    // Send data to Flask proxy API
    $.ajax({
        url: '/api/sync-to-spreadsheet',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            sync_url: syncUrl,
            action: 'sync_attendance',
            data: dataToSync
        }),
        success: function(response) {
            console.log('Sync response:', response);
            
            if (response.success) {
                showAlert(`Successfully synced ${dataToSync.length} records to spreadsheet!`, 'success');
                
                // Clear selections after successful sync
                selectedRows.clear();
                $('.row-checkbox').prop('checked', false);
                $('#selectAll').prop('checked', false);
                $('.row-selected').removeClass('row-selected');
            } else {
                showAlert('Sync failed: ' + (response.error || 'Unknown error'), 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Sync error:', error);
            
            let errorMessage = 'Sync failed: ' + error;
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = 'Sync failed: ' + xhr.responseJSON.error;
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            syncBtn.html(originalText).prop('disabled', false);
            updateSyncButton();
        }
    });
}

/**
 * Toggle Monthly Grid Sync Mode
 */
function toggleMonthlyGridSyncMode() {
    // Check if grid sync functionality is enabled first
    const isGridSyncEnabled = $('#enableMonthlyGridSync').prop('checked');
    if (!isGridSyncEnabled) {
        showAlert('⚠️ Harap aktifkan "Enable grid sync functionality" terlebih dahulu sebelum mengaktifkan Sync Mode!', 'warning');
        return;
    }

    // Check if monthly grid data is available
    if (!currentMonthlyGridData) {
        showAlert('⚠️ Harap muat grid bulanan terlebih dahulu sebelum mengaktifkan Sync Mode!', 'warning');
        return;
    }

    monthlyGridSyncModeActive = !monthlyGridSyncModeActive;
    console.log('Toggling monthly grid sync mode to:', monthlyGridSyncModeActive);
    
    const button = $('#toggleMonthlyGridSyncMode');
    const headerButton = $('#toggleMonthlyGridSyncModeHeader');
    const monthlyGridControls = $('#monthlyGridSyncControls');
    const syncActions = $('#monthlyGridSyncActions');
    
    if (monthlyGridSyncModeActive) {
        // Update both buttons to active state
        button.removeClass('btn-warning').addClass('btn-sync-active');
        button.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        
        headerButton.removeClass('btn-warning').addClass('btn-sync-active');
        headerButton.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Mode Sinkronisasi ON');
        
        monthlyGridControls.addClass('monthly-grid-sync-active');
        syncActions.show();
        
        console.log('✅ Monthly Grid Sync Mode ACTIVATED');
        console.log('Re-rendering grid with sync mode...');
        
        // Force re-render grid with sync functionality
        console.log('=== FORCING SYNC GRID RE-RENDER ===');
        console.log('currentMonthlyGridData exists:', !!currentMonthlyGridData);
        console.log('Grid data length:', currentMonthlyGridData?.grid_data?.length || 0);
        
        displayMonthlyGridWithSync(currentMonthlyGridData);
        
        // Auto-sync employee list with current monthly grid
        autoSyncEmployeeListWithMonthlyGrid();
        
        // Verification after re-render
        setTimeout(() => {
            console.log('=== POST RE-RENDER CHECK ===');
            console.log('Checkboxes found:', $('.monthly-grid-row-checkbox').length);
            console.log('Header checkbox found:', $('#selectAllMonthlyGrid').length);
            console.log('=== END POST RE-RENDER CHECK ===');
        }, 200);
        
        showAlert('✅ Mode Sinkronisasi Grid Bulanan aktif! Klik pada checkbox untuk memilih baris data karyawan.', 'success');
        
    } else {
        // Update both buttons to inactive state
        button.removeClass('btn-sync-active').addClass('btn-warning');
        button.html('<i class="fas fa-sync me-1"></i>Sync Mode');
        
        headerButton.removeClass('btn-sync-active').addClass('btn-warning');
        headerButton.html('<i class="fas fa-sync me-1"></i>Mode Sinkronisasi');
        
        monthlyGridControls.removeClass('monthly-grid-sync-active');
        syncActions.hide();
        
        // Clear selections
        monthlyGridSelectedRows.clear();
        updateMonthlyGridSyncInfo();
        
        console.log('❌ Monthly Grid Sync Mode DEACTIVATED');
        console.log('Re-rendering grid in normal mode...');
        
        // Re-render grid in normal mode
        displayMonthlyGrid(currentMonthlyGridData);
        
        showAlert('❌ Mode Sinkronisasi Grid Bulanan dinonaktifkan.', 'info');
    }
    
    // Force verification after toggle
    setTimeout(() => {
        console.log('=== POST-TOGGLE VERIFICATION ===');
        console.log('monthlyGridSyncModeActive:', monthlyGridSyncModeActive);
        console.log('Checkboxes in DOM:', $('.monthly-grid-row-checkbox').length);
        console.log('Header checkbox in DOM:', $('#selectAllMonthlyGrid').length);
        console.log('Table rows:', $('#attendanceTableBody tr').length);
        console.log('=== END VERIFICATION ===');
    }, 500);
}

/**
 * Display Monthly Grid with Sync functionality
 */
function displayMonthlyGridWithSync(gridData) {
    console.log('=== displayMonthlyGridWithSync CALLED ===');
    console.log('monthlyGridSyncModeActive at start:', monthlyGridSyncModeActive);
    console.log('gridData exists:', !!gridData);
    console.log('Number of employees:', gridData?.grid_data?.length || 0);
    
    currentMonthlyGridData = gridData;
    
    // Completely disable DataTables for monthly grids
    window.useDataTables = false;
    
    // Destroy any existing DataTable to prevent conflicts
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable for monthly grid with sync...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }
    
    // Update title with data availability indicator for sync mode
    let titleHtml = `<i class="fas fa-table me-2"></i>${gridData.month_name} ${gridData.year} - Enhanced Daily Attendance Grid (Sync Mode)`;
    
    // Add data availability indicator if available
    if (gridData.data_availability && gridData.data_availability.latest_available_date) {
        const latestDate = gridData.data_availability.latest_available_date;
        const hasUnavailableDates = gridData.data_availability.has_unavailable_dates;
        
        if (hasUnavailableDates) {
            titleHtml += `<br><small class="text-warning">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Data available up to: <strong>${latestDate}</strong> 
                (${gridData.data_availability.available_days_count}/${gridData.data_availability.total_days_in_month} days)
                <br><span class="text-muted">N/A dates cannot be selected for staging</span>
            </small>`;
        } else {
            titleHtml += `<br><small class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                Complete data available (${gridData.data_availability.available_days_count}/${gridData.data_availability.total_days_in_month} days)
            </small>`;
        }
    }
    
    $('#monthlyReportTitle').html(titleHtml);
    
    // Create header with checkbox column if sync mode is active
    let headerHtml = '<tr>';
    
    console.log('=== HEADER CHECKBOX CREATION ===');
    console.log('monthlyGridSyncModeActive:', monthlyGridSyncModeActive);
    console.log('typeof monthlyGridSyncModeActive:', typeof monthlyGridSyncModeActive);
    
    if (monthlyGridSyncModeActive === true) {
        console.log('✅ Creating header checkbox column - sync mode is ACTIVE');
        headerHtml += `
            <th style="width: 50px; position: sticky; left: 0; background: #212529; z-index: 21; text-align: center;">
                <div class="form-check">
                    <input class="form-check-input monthly-grid-checkbox" type="checkbox" id="selectAllMonthlyGrid">
                    <label class="form-check-label text-white" for="selectAllMonthlyGrid" style="font-size: 0.8rem;">All</label>
                </div>
            </th>
        `;
    } else {
        console.log('❌ Header checkbox NOT created - sync mode not active:', monthlyGridSyncModeActive);
    }
    console.log('=== END HEADER CHECKBOX CREATION ===');
    
    headerHtml += `
        <th style="min-width: 50px; position: sticky; left: ${monthlyGridSyncModeActive ? '50px' : '0'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">No</th>
        <th style="min-width: 120px; position: sticky; left: ${monthlyGridSyncModeActive ? '120px' : '50px'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee ID</th>
        <th style="min-width: 120px; position: sticky; left: ${monthlyGridSyncModeActive ? '240px' : '170px'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem; color: #17a2b8;">PTRJ Employee ID</th>
        <th style="min-width: 200px; position: sticky; left: ${monthlyGridSyncModeActive ? '360px' : '290px'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee Name</th>
    `;

    // Add day headers with data availability indicators
    for (let day = 1; day <= gridData.days_in_month; day++) {
        const date = new Date(gridData.year, gridData.month - 1, day);
        const dayOfWeek = date.getDay();
        const isSunday = dayOfWeek === 0;
        const dayName = getDayNameIndonesian(gridData.year, gridData.month, day);
        
        // Check if data is available for this date
        const dateStr = `${gridData.year}-${String(gridData.month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const isDataAvailable = !gridData.data_availability || 
                               !gridData.data_availability.available_dates || 
                               gridData.data_availability.available_dates.includes(dateStr);
        
        // Check if this day is a national holiday by looking at the first employee's data
        let isNationalHoliday = false;
        let holidayInfo = '';
        if (gridData.grid_data && gridData.grid_data.length > 0) {
            const firstEmployee = gridData.grid_data[0];
            const dayData = firstEmployee.days[day.toString()];
            if (dayData && dayData.is_national_holiday) {
                isNationalHoliday = true;
                holidayInfo = dayData.holiday_info || '';
            }
        }
        
        // Set background color with data availability priority
        let backgroundColor = '#0d6efd'; // Default blue
        let textColor = 'white';
        let additionalStyle = '';
        let headerClass = '';
        
        if (!isDataAvailable) {
            // Data unavailable - highest priority styling
            backgroundColor = '#6c757d'; // Gray for unavailable data
            textColor = '#e9ecef';
            additionalStyle = 'font-weight: bold; opacity: 0.7;';
            headerClass = 'header-data-unavailable';
        } else if (isSunday || isNationalHoliday) {
            backgroundColor = '#dc3545'; // Red for Sunday or National Holiday
            textColor = 'white';
            additionalStyle = 'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);';
        }
        
        // Create header with holiday info and data availability indicator
        let headerContent = `<div>${day}</div><div style="font-size: 0.6rem; color: #adb5bd;">${dayName}</div>`;
        
        if (!isDataAvailable) {
            headerContent += `<div style="font-size: 0.5rem; color: #e9ecef; margin-top: 1px;" title="Data not available for this date">N/A</div>`;
        } else if (isNationalHoliday && holidayInfo) {
            headerContent += `<div style="font-size: 0.5rem; color: #ffeb3b; margin-top: 1px;" title="${holidayInfo}">🎉 LIBUR</div>`;
        }
        
        const headerStyle = `min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: ${backgroundColor}; z-index: 10; color: ${textColor}; ${additionalStyle}`;
        
        headerHtml += `
            <th style="${headerStyle}" ${headerClass ? `class="${headerClass}"` : ''}>
                ${headerContent}
            </th>
        `;
    }
    
    // Add totals columns
    headerHtml += `
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>DAYS</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Total</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>REG</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>OT</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
    `;

    // Add charge job columns (Task Code, Machine Code, Expense Code)
    headerHtml += `
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>TASK</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>MACHINE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>EXPENSE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
    `;

    headerHtml += '</tr>';

    // Create grid table body
    let bodyHtml = '';
    console.log(`Creating table body. Sync mode active: ${monthlyGridSyncModeActive}, Employee count: ${gridData.grid_data.length}`);
    gridData.grid_data.forEach(function(employee, index) {
        // Get charge job data for this employee (using both name and ID for better matching)
        const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName, employee.EmployeeID);
        
        const rowId = `monthly_row_${index}`;
        const isSelected = monthlyGridSelectedRows.has(index);
        const rowClass = isSelected ? 'monthly-grid-row-selected' : '';
        const selectableClass = monthlyGridSyncModeActive ? 'monthly-grid-row-selectable' : '';
        
        let rowHtml = `<tr id="${rowId}" data-row-index="${index}" class="${rowClass} ${selectableClass}">`;
        
        // Add checkbox column if sync mode is active
        console.log(`=== ROW ${index} CHECKBOX CREATION ===`);
        console.log(`monthlyGridSyncModeActive: ${monthlyGridSyncModeActive} (type: ${typeof monthlyGridSyncModeActive})`);
        
        if (monthlyGridSyncModeActive === true) {
            console.log(`✅ Creating checkbox for row ${index}`);
            rowHtml += `
                <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                    <input type="checkbox" class="form-check-input monthly-grid-row-checkbox" 
                           data-row-index="${index}" ${isSelected ? 'checked' : ''}>
                </td>
            `;
            console.log(`✅ Checkbox HTML created for row ${index}:`, rowHtml.includes('monthly-grid-row-checkbox'));
        } else {
            console.log(`❌ Sync mode false for row ${index}, monthlyGridSyncModeActive = ${monthlyGridSyncModeActive}`);
        }
        console.log(`=== END ROW ${index} CHECKBOX CREATION ===`);
        
        rowHtml += `
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '50px' : '0'}; background: white; z-index: 5; border-right: 2px solid #dee2e6;">
                ${monthlyGridSyncModeActive ? 
                    `<span class="monthly-grid-row-number ${isSelected ? 'selected' : ''}" data-row-index="${index}">${employee.No}</span>` :
                    employee.No
                }
            </td>
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '120px' : '50px'}; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.EmployeeID}</td>
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '240px' : '170px'}; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center; color: #17a2b8; font-weight: 500;">${employee.PTRJEmployeeID || 'N/A'}</td>
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '360px' : '290px'}; background: white; z-index: 5; border-right: 2px solid #dee2e6; font-weight: 500;">${employee.EmployeeName}</td>
        `;

        // Track totals for this employee
        let totalWorkingDays = 0;
        let totalRegularHours = 0;
        let totalOvertimeHours = 0;

        // Add working hours for each day
        for (let day = 1; day <= gridData.days_in_month; day++) {
            const date = new Date(gridData.year, gridData.month - 1, day);
            const dayOfWeek = date.getDay();
            const isSaturday = dayOfWeek === 6;
            const hours = employee.days[day.toString()] || '-';
            let cellClass = getWorkingHoursClass(hours, isSaturday, employee, day);

            // Handle display and totals calculation
            let displayHours = hours;
            let regularHours = 0;
            let overtimeHours = 0;

            // Handle new object format
            if (typeof hours === 'object' && hours !== null) {
                regularHours = hours.normal_hours || 0;
                overtimeHours = hours.overtime_hours || 0;

                // Create display string with enhanced leave and ALFA logic  
                if (hours.status === 'data_unavailable' || hours.is_data_unavailable) {
                    // Data not available for this date - CRITICAL: Should not be selectable for staging
                    displayHours = 'N/A';
                    // Update cell class to prevent selection
                    cellClass = 'hours-data-unavailable data-unavailable-cell';
                } else if (hours.status === 'on_leave' && hours.leave_data) {
                    // Display leave type code for leave days
                    displayHours = hours.leave_data.leave_type_code || 'LEAVE';
                } else if (hours.status === 'alfa') {
                    // Display ALFA for absent without leave
                    displayHours = 'ALFA';
                } else if (hours.is_sunday && regularHours === 0 && overtimeHours === 0) {
                    displayHours = 'OFF';
                } else if (hours.is_national_holiday && regularHours === 0 && overtimeHours === 0) {
                    displayHours = 'OFF';
                } else if (hours.status === 'absent') {
                    displayHours = '-';
                } else if (hours.status === 'partial_check_in_only' || hours.status === 'partial_check_out_only') {
                    // Show default hours for partial attendance with blue background
                    const overtimeDisplay = overtimeHours > 0 ? overtimeHours.toString() : '0';
                    displayHours = `(${regularHours}) | (${overtimeDisplay})`;
                } else {
                    const overtimeDisplay = overtimeHours > 0 ? overtimeHours.toString() : '-';
                    displayHours = `(${regularHours}) | (${overtimeDisplay})`;
                }
            } else if (typeof hours === 'string' && hours !== '-' && hours !== 'OFF') {
                // Handle legacy string format
                if (hours.includes('|')) {
                    const parts = hours.split('|');
                    if (parts.length === 2) {
                        const regularPart = parts[0].trim().replace(/[()]/g, '');
                        const overtimePart = parts[1].trim().replace(/[()]/g, '');

                        regularHours = parseFloat(regularPart) || 0;
                        overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
                    }
                } else {
                    regularHours = parseFloat(hours) || 0;
                    overtimeHours = 0;
                }
                displayHours = hours;
            }

            // Calculate totals
            if (regularHours > 0 || overtimeHours > 0) {
                totalWorkingDays++;
                totalRegularHours += regularHours;
                totalOvertimeHours += overtimeHours;
            }

            rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${displayHours}</td>`;
        }

        // Add totals cells
        rowHtml += `
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalWorkingDays}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalRegularHours.toFixed(1)}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalOvertimeHours.toFixed(1)}</td>
        `;

        // Add charge job columns
        rowHtml += `
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.task_code}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.machine_code}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.expense_code}</td>
        `;

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    // Update table
    console.log('Updating table HTML...');
    console.log('Header HTML length:', headerHtml.length);
    console.log('Body HTML length:', bodyHtml.length);
    console.log('Header HTML contains checkbox:', headerHtml.includes('selectAllMonthlyGrid'));
    console.log('Body HTML contains checkbox:', bodyHtml.includes('monthly-grid-row-checkbox'));
    
    $('#attendanceTable thead').html(headerHtml);
    $('#attendanceTableBody').html(bodyHtml);

    // Apply grid styling but NO DataTables
    $('#attendanceTable').addClass('attendance-grid');
    $('#attendanceTable').removeClass('dataTable'); // Remove any DataTable classes

    // Ensure DataTables is completely disabled
    console.log('Monthly grid with sync displayed - DataTables completely disabled to prevent conflicts');
    
    // Verify checkboxes are in DOM after update
    setTimeout(() => {
        console.log('After DOM update - Checkboxes in DOM:', $('.monthly-grid-row-checkbox').length);
        console.log('After DOM update - Header checkbox in DOM:', $('#selectAllMonthlyGrid').length);
        console.log('Table head HTML sample:', $('#attendanceTable thead').html().substring(0, 200));
        console.log('First row HTML sample:', $('#attendanceTableBody tr').first().html().substring(0, 200));
    }, 100);

    // Bind event handlers for sync mode
    if (monthlyGridSyncModeActive) {
        console.log('Sync mode is active, binding events...');
        console.log('Number of checkboxes found:', $('.monthly-grid-row-checkbox').length);
        console.log('Select all checkbox found:', $('#selectAllMonthlyGrid').length);
        
        bindMonthlyGridSyncEvents();
        updateMonthlyGridSyncInfo();
    } else {
        console.log('Sync mode is NOT active');
    }

    // Rest of the function remains same as original displayMonthlyGrid
    displayMonthlyGridSummary(gridData);
}

/**
 * Bind Monthly Grid Sync Events
 */
function bindMonthlyGridSyncEvents() {
    // Handle select all checkbox
    $('#selectAllMonthlyGrid').off('change').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.monthly-grid-row-checkbox').prop('checked', isChecked);
        
        if (isChecked) {
            $('.monthly-grid-row-checkbox').each(function() {
                const rowIndex = parseInt($(this).data('row-index'));
                monthlyGridSelectedRows.add(rowIndex);
                $(`#monthly_row_${rowIndex}`).addClass('monthly-grid-row-selected');
                $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).addClass('selected');
            });
        } else {
            monthlyGridSelectedRows.clear();
            $('.monthly-grid-row-checkbox').each(function() {
                const rowIndex = parseInt($(this).data('row-index'));
                $(`#monthly_row_${rowIndex}`).removeClass('monthly-grid-row-selected');
                $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).removeClass('selected');
            });
        }
        
        updateMonthlyGridSyncInfo();
    });

    // Handle individual row checkboxes
    $('.monthly-grid-row-checkbox').off('change').on('change', function() {
        const rowIndex = parseInt($(this).data('row-index'));
        const isChecked = $(this).prop('checked');
        
        if (isChecked) {
            monthlyGridSelectedRows.add(rowIndex);
            $(`#monthly_row_${rowIndex}`).addClass('monthly-grid-row-selected');
            $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).addClass('selected');
        } else {
            monthlyGridSelectedRows.delete(rowIndex);
            $(`#monthly_row_${rowIndex}`).removeClass('monthly-grid-row-selected');
            $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).removeClass('selected');
        }
        
        // Update select all checkbox
        const totalCheckboxes = $('.monthly-grid-row-checkbox').length;
        const checkedCheckboxes = $('.monthly-grid-row-checkbox:checked').length;
        $('#selectAllMonthlyGrid').prop('checked', checkedCheckboxes === totalCheckboxes);
        
        updateMonthlyGridSyncInfo();
    });

    // Handle row number clicks
    $('.monthly-grid-row-number').off('click').on('click', function() {
        const rowIndex = parseInt($(this).data('row-index'));
        const checkbox = $(`.monthly-grid-row-checkbox[data-row-index="${rowIndex}"]`);
        checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });

    // Handle row clicks
    $('.monthly-grid-row-selectable').off('click').on('click', function(e) {
        // Don't trigger if clicking on checkbox
        if ($(e.target).hasClass('monthly-grid-row-checkbox') || $(e.target).hasClass('monthly-grid-row-number')) {
            return;
        }
        
        const rowIndex = parseInt($(this).data('row-index'));
        const checkbox = $(`.monthly-grid-row-checkbox[data-row-index="${rowIndex}"]`);
        checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });
}

/**
 * Update Monthly Grid Sync Info
 */
function updateMonthlyGridSyncInfo() {
    const selectedCount = monthlyGridSelectedRows.size;
    const syncBtn = $('#syncMonthlyGridToSheetBtn');
    const infoSpan = $('#monthlyGridSelectionInfo');
    const detailsSpan = $('#monthlyGridSelectionDetails');
    const stagingType = $('input[name="stagingType"]:checked').val();
    const stagingTypeText = stagingType === 'local' ? 'Local Staging' : 'Google Apps Script Staging';
    
    if (selectedCount > 0) {
        infoSpan.text(`${selectedCount} row${selectedCount > 1 ? 's' : ''} selected`);
        detailsSpan.text(`Ready to transfer to ${stagingTypeText}`);
        syncBtn.prop('disabled', false);
        
        // Update button text based on staging type
        const icon = stagingType === 'local' ? 'fas fa-database' : 'fas fa-cloud-upload-alt';
        const actionText = stagingType === 'local' ? 'Transfer to Local Staging' : 'Transfer to Google Staging';
        syncBtn.html(`<i class="${icon} me-1"></i>${actionText} (${selectedCount})`);
    } else {
        infoSpan.text('No rows selected');
        detailsSpan.text('');
        syncBtn.prop('disabled', true);
        
        // Update button text based on staging type
        const icon = stagingType === 'local' ? 'fas fa-database' : 'fas fa-cloud-upload-alt';
        const actionText = stagingType === 'local' ? 'Transfer to Local Staging' : 'Transfer to Google Staging';
        syncBtn.html(`<i class="${icon} me-1"></i>${actionText}`);
    }
}

/**
 * Sync Monthly Grid to Spreadsheet
 */
function syncMonthlyGridToSpreadsheet() {
    const syncUrl = $('#monthlyGridSyncUrl').val();
    const enableSync = $('#enableMonthlyGridSync').prop('checked');
    const stagingType = $('input[name="stagingType"]:checked').val();
    
    if (!enableSync) {
        showAlert('Please enable monthly grid sync functionality first.', 'warning');
        return;
    }
    
    if (monthlyGridSelectedRows.size === 0) {
        showAlert('Please select at least one employee row to stage.', 'warning');
        return;
    }
    
    if (!currentMonthlyGridData) {
        showAlert('No grid data available to stage.', 'warning');
        return;
    }
    
    // Validate based on staging type
    if (stagingType === 'google' && !syncUrl) {
        showAlert('Please enter Google Apps Script URL for Google Apps Script staging.', 'warning');
        return;
    }
    
    // Handle Local Staging
    if (stagingType === 'local') {
        handleLocalStaging();
        return;
    }
    
    // Handle Google Apps Script Staging
    if (stagingType === 'google') {
        handleGoogleAppsScriptStaging(syncUrl);
        return;
    }
}

/**
 * Handle Local Staging - Transfer selected rows to local staging database
 */
function handleLocalStaging() {
    // Debug current state
    console.log('=== LOCAL STAGING PREPARATION DEBUG ===');
    console.log('Selected rows:', Array.from(monthlyGridSelectedRows));
    console.log('Current monthly grid data exists:', !!currentMonthlyGridData);
    console.log('Current monthly grid data keys:', currentMonthlyGridData ? Object.keys(currentMonthlyGridData) : 'No data');
    console.log('Days in month:', currentMonthlyGridData ? currentMonthlyGridData.days_in_month : 'No data');
    console.log('Grid data length:', currentMonthlyGridData ? currentMonthlyGridData.grid_data.length : 'No data');
    console.log('Employee charge job data loaded:', !!employeeChargeJobData);
    
    // Check date filtering options
    const useFullMonth = $('#useFullMonthData').prop('checked');
    let startDate, endDate;
    
    console.log('=== DATE FILTERING SETUP ===');
    console.log('useFullMonth:', useFullMonth);
    
    if (useFullMonth) {
        // Use full month data
        startDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month - 1, 1);
        endDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month, 0);
        console.log('✅ Using full month data');
    } else {
        // Use custom date range
        const startDateStr = $('#transferStartDate').val();
        const endDateStr = $('#transferEndDate').val();
        
        if (!startDateStr || !endDateStr) {
            showAlert('❌ Please specify start and end dates for custom range.', 'warning');
            return;
        }
        
        startDate = new Date(startDateStr);
        endDate = new Date(endDateStr);
        console.log('📅 Using custom date range');
    }
    
    console.log('Date range:', {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0],
        startDay: startDate.getDate(),
        endDay: endDate.getDate()
    });
    
    // Prepare data for local staging
    const stagingRecords = [];
    let skippedDays = 0;
    monthlyGridSelectedRows.forEach(rowIndex => {
        const employee = currentMonthlyGridData.grid_data[rowIndex];
        console.log(`Processing employee at index ${rowIndex}:`, employee ? `${employee.EmployeeName} (${employee.EmployeeID})` : 'Not found');
        if (employee) {
            // Get charge job data for this employee (enhanced lookup)
            const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName, employee.EmployeeID);
            console.log(`  Charge job data for ${employee.EmployeeName} (${employee.EmployeeID}):`, chargeJobData);
            
            // Convert daily hours to individual daily records for staging
            let processedDays = 0;
            console.log(`  Employee ${employee.EmployeeName} daily hours sample:`, {
                day1: employee.days['1'],
                day2: employee.days['2'],
                day15: employee.days['15'],
                totalDays: Object.keys(employee.days || {}).length
            });
            
            for (let day = 1; day <= currentMonthlyGridData.days_in_month; day++) {
                const dayKey = day.toString();
                const hours = employee.days[dayKey] || '-';
                
                // Apply date filter
                const currentDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month - 1, day);
                if (currentDate < startDate || currentDate > endDate) {
                    skippedDays++;
                    console.log(`  Skipping day ${day} for ${employee.EmployeeName}: Outside date range`);
                    continue;
                }
                
                // Skip OFF days and absent days
                if (hours === 'OFF' || hours === '-') {
                    continue;
                }
                
                // Skip data unavailable days (critical for staging accuracy)
                if (typeof hours === 'object' && hours !== null && 
                    (hours.status === 'data_unavailable' || hours.is_data_unavailable)) {
                    console.log(`  Skipping day ${day} for ${employee.EmployeeName}: Data unavailable`);
                    continue;
                }
                
                processedDays++;
                
                // Parse hours (handle both object and string formats)
                let regularHours = 0;
                let overtimeHours = 0;

                if (typeof hours === 'object' && hours !== null) {
                    // New object format
                    regularHours = hours.normal_hours || 0;
                    overtimeHours = hours.overtime_hours || 0;
                } else if (typeof hours === 'string' && hours.includes('|')) {
                    // Legacy string format: "(7) | (2)" or "(7) | (-)"
                    const parts = hours.split('|');
                    if (parts.length === 2) {
                        const regularPart = parts[0].trim().replace(/[()]/g, '');
                        const overtimePart = parts[1].trim().replace(/[()]/g, '');

                        regularHours = parseFloat(regularPart) || 0;
                        overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
                    }
                } else if (typeof hours === 'string' || typeof hours === 'number') {
                    // Simple numeric format
                    regularHours = parseFloat(hours) || 0;
                    overtimeHours = 0;
                }
                
                // Create staging record for this day with enhanced charge job data
                const dateObj = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month - 1, day);
                const stagingRecord = {
                    employee_id: employee.EmployeeID,
                    employee_name: employee.EmployeeName,
                    date: dateObj.toISOString().split('T')[0], // YYYY-MM-DD format
                    day_of_week: getDayNameIndonesian(currentMonthlyGridData.year, currentMonthlyGridData.month, day),
                    shift: 'Regular', // Default shift
                    check_in: '08:00', // Default check-in
                    check_out: '17:00', // Default check-out (will be adjusted based on hours)
                    regular_hours: regularHours,
                    overtime_hours: overtimeHours,
                    task_code: chargeJobData.task_code || '',
                    station_code: chargeJobData.station_code || '',
                    machine_code: chargeJobData.machine_code || '',
                    expense_code: chargeJobData.expense_code || '',
                    raw_charge_job: chargeJobData.raw_charge_job || '',  // Enhanced raw charge job data
                    notes: `Transferred from Monthly Grid - ${currentMonthlyGridData.month_name} ${currentMonthlyGridData.year}${useFullMonth ? ' (Full Month)' : ' (Custom Range)'}`,
                    _debug_info: {
                        date_filter_applied: !useFullMonth,
                        start_date: startDate.toISOString().split('T')[0],
                        end_date: endDate.toISOString().split('T')[0],
                        original_hours_data: hours,
                        parsed_regular: regularHours,
                        parsed_overtime: overtimeHours
                    }
                };
                
                stagingRecords.push(stagingRecord);
            }
            console.log(`  Employee ${employee.EmployeeName} processed ${processedDays} working days, created ${stagingRecords.length} total records so far`);
        }
    });
    
    console.log('=== STAGING SUMMARY ===');
    console.log(`Total staging records: ${stagingRecords.length}`);
    console.log(`Skipped days (date filter): ${skippedDays}`);
    console.log(`Date filter mode: ${useFullMonth ? 'Full Month' : 'Custom Range'}`);
    console.log('=== END LOCAL STAGING PREPARATION DEBUG ===');
    
    if (stagingRecords.length === 0) {
        showAlert('❌ No valid working days found in selected rows to transfer to staging. Check date range and selected employees.', 'warning');
        return;
    }
    
    // Show progress
    const syncBtn = $('#syncMonthlyGridToSheetBtn');
    const originalText = syncBtn.html();
    syncBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Transferring to Local Staging...').prop('disabled', true);
    
    // Debug the data being sent
    console.log('=== STAGING TRANSFER DEBUG ===');
    console.log('Number of staging records:', stagingRecords.length);
    console.log('First 2 staging records:', stagingRecords.slice(0, 2));
    console.log('Sample record structure:', stagingRecords[0] ? Object.keys(stagingRecords[0]) : 'No records');
    console.log('JSON.stringify test:', JSON.stringify({records: stagingRecords.slice(0, 1)}));
    console.log('=== END DEBUG ===');
    
    // Send data to local staging API
    $.ajax({
        url: '/api/staging/data',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            records: stagingRecords,
            source: 'monthly_grid_sync',
            month: currentMonthlyGridData.month,
            year: currentMonthlyGridData.year,
            date_filter: {
                use_full_month: useFullMonth,
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            }
        }),
        success: function(response) {
            console.log('Local Staging response:', response);
            
            if (response.success) {
                let message = `✅ Successfully transferred ${response.added_records} records to local staging!`;
                
                if (!useFullMonth) {
                    message += `<br>📅 Date range: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
                }
                
                if (skippedDays > 0) {
                    message += `<br>📊 Filtered out ${skippedDays} days outside date range.`;
                }
                
                message += `<br>You can view them in the "Staging" tab.`;
                
                showAlert(message, 'success', 8000);
                
                // Clear selections after successful transfer
                clearMonthlyGridSelections();
            } else {
                showAlert('Local staging failed: ' + (response.error || 'Unknown error'), 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('=== LOCAL STAGING ERROR DEBUG ===');
            console.error('Status:', status);
            console.error('Error:', error);
            console.error('XHR Status:', xhr.status);
            console.error('XHR Response Text:', xhr.responseText);
            console.error('XHR Response JSON:', xhr.responseJSON);
            console.error('=== END ERROR DEBUG ===');
            
            let errorMessage = 'Local staging failed: ' + error;
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = 'Local staging failed: ' + xhr.responseJSON.error;
                
                // Show debug info if available
                if (xhr.responseJSON.debug_info) {
                    console.error('Server debug info:', xhr.responseJSON.debug_info);
                    errorMessage += '. Check console for details.';
                }
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            syncBtn.html(originalText).prop('disabled', false);
            updateMonthlyGridSyncInfo();
        }
    });
}

/**
 * Handle Google Apps Script Staging - Transfer selected rows to Google Apps Script
 */
function handleGoogleAppsScriptStaging(syncUrl) {
    // Check date filtering options
    const useFullMonth = $('#useFullMonthData').prop('checked');
    let startDate, endDate;
    
    if (useFullMonth) {
        // Use full month data
        startDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month - 1, 1);
        endDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month, 0);
    } else {
        // Use custom date range
        const startDateStr = $('#transferStartDate').val();
        const endDateStr = $('#transferEndDate').val();
        
        if (!startDateStr || !endDateStr) {
            showAlert('❌ Please specify start and end dates for custom range.', 'warning');
            return;
        }
        
        startDate = new Date(startDateStr);
        endDate = new Date(endDateStr);
    }
    
    // Prepare daily data for sync with date filtering
    const dataToSync = [];
    let filteredDaysCount = 0;
    
    monthlyGridSelectedRows.forEach(rowIndex => {
        const employee = currentMonthlyGridData.grid_data[rowIndex];
        if (employee) {
            // Get charge job data for this employee
            const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName);
            
            // Filter daily hours based on date range if not using full month
            let filteredDailyHours = {};
            if (useFullMonth) {
                filteredDailyHours = employee.days;
            } else {
                // Apply date filter to daily hours
                for (let day = 1; day <= currentMonthlyGridData.days_in_month; day++) {
                    const currentDate = new Date(currentMonthlyGridData.year, currentMonthlyGridData.month - 1, day);
                    if (currentDate >= startDate && currentDate <= endDate) {
                        filteredDailyHours[day.toString()] = employee.days[day.toString()];
                    } else {
                        filteredDaysCount++;
                    }
                }
            }
            
            dataToSync.push({
                no: employee.No,
                employeeId: employee.EmployeeID,
                employeeName: employee.EmployeeName,
                year: currentMonthlyGridData.year,
                month: currentMonthlyGridData.month,
                monthName: getMonthName(currentMonthlyGridData.month),
                daysInMonth: currentMonthlyGridData.days_in_month,
                dailyHours: filteredDailyHours,  // Send filtered daily hours data
                // Add charge job data for enhanced sync
                taskCode: chargeJobData.task_code,
                machineCode: chargeJobData.machine_code,
                expenseCode: chargeJobData.expense_code,
                // Add date filter info
                dateFilterApplied: !useFullMonth,
                startDate: startDate.toISOString().split('T')[0],
                endDate: endDate.toISOString().split('T')[0]
            });
        }
    });
    
    // Show progress
    const syncBtn = $('#syncMonthlyGridToSheetBtn');
    const originalText = syncBtn.html();
    syncBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Transferring to Google Apps Script...').prop('disabled', true);
    
    // Send data to Flask proxy API
    $.ajax({
        url: '/api/sync-to-spreadsheet',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            sync_url: syncUrl,
            action: 'sync_daily_grid_staging',  // Changed action name for staging
            data: dataToSync
        }),
        success: function(response) {
            console.log('Google Apps Script Staging response:', response);
            
            if (response.success) {
                let message = `✅ Successfully transferred ${dataToSync.length} employee records to Google Apps Script staging!`;
                
                if (!useFullMonth) {
                    message += `<br>📅 Date range: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
                }
                
                if (filteredDaysCount > 0) {
                    message += `<br>📊 Filtered out ${filteredDaysCount} days outside date range.`;
                }
                
                showAlert(message, 'success', 8000);
                
                // Clear selections after successful transfer
                clearMonthlyGridSelections();
            } else {
                showAlert('Google Apps Script staging failed: ' + (response.error || 'Unknown error'), 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Google Apps Script Staging error:', error);
            
            let errorMessage = 'Google Apps Script staging failed: ' + error;
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = 'Google Apps Script staging failed: ' + xhr.responseJSON.error;
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            syncBtn.html(originalText).prop('disabled', false);
            updateMonthlyGridSyncInfo();
        }
    });
}

/**
 * Clear monthly grid selections
 */
function clearMonthlyGridSelections() {
    monthlyGridSelectedRows.clear();
    $('.monthly-grid-row-checkbox').prop('checked', false);
    $('#selectAllMonthlyGrid').prop('checked', false);
    $('.monthly-grid-row-selected').removeClass('monthly-grid-row-selected');
    $('.monthly-grid-row-number.selected').removeClass('selected');
}

/**
 * Display Monthly Grid Summary (separated from main display function)
 */
function displayMonthlyGridSummary(gridData) {
    // Apply grid styling
    $('#attendanceTable').addClass('attendance-grid');

    // Add CSS for fixed header
    const styleId = 'fixedHeaderStyle';
    if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
            .table-container {
                max-height: 600px;
                overflow-y: auto;
                overflow-x: auto;
            }
            .attendance-grid th {
                position: sticky;
                top: 0;
                background-color: #212529;
                color: white;
                z-index: 10;
            }
            .attendance-grid th:nth-child(1),
            .attendance-grid th:nth-child(2),
            .attendance-grid th:nth-child(3) {
                z-index: 20;
            }
        `;
        document.head.appendChild(style);
    }

    // Show summary info
    let summaryHtml = `
        <div class="alert alert-success">
            <h5><i class="fas fa-info-circle me-2"></i>Grid Summary by Station</h5>
            <div class="row">
                <div class="col-md-2">
                    <strong>Total Stations:</strong> ${gridData.total_stations || 'N/A'}
                </div>
                <div class="col-md-2">
                    <strong>Total Employees:</strong> ${gridData.total_employees}
                </div>
                <div class="col-md-2">
                    <strong>Days in Month:</strong> ${gridData.days_in_month}
                </div>
                <div class="col-md-2">
                    <strong>Working Days:</strong> ${gridData.total_working_days}
                </div>
                <div class="col-md-4">
                    <strong>Period:</strong> ${gridData.date_range}
                </div>
            </div>
            <div class="mt-2">
                <small>
                    <strong>Legend:</strong>
                    <span class="badge bg-success">(7) | (-)</span> Full Regular Hours |
                    <span class="badge bg-warning">Partial Hours</span> Incomplete Hours |
                    <span class="badge bg-secondary">-</span> Absent |
                    <span class="badge bg-info">OFF</span> Sunday |
                    <span class="badge bg-danger" style="background-color: #dc3545;">Sunday Header</span> Red Background
                </small>
            </div>
            <div class="mt-3">
                <button class="btn btn-success me-2" onclick="exportGridToExcel(${gridData.year}, ${gridData.month})">
                    <i class="fas fa-file-excel me-1"></i>
                    Export to Excel
                </button>
                <button class="btn btn-info" onclick="exportGridToJSON(${gridData.year}, ${gridData.month})">
                    <i class="fas fa-file-code me-1"></i>
                    Export to JSON
                </button>
            </div>
        </div>
    `;

    // Add overtime summary if available
    if (gridData.overtime_summary && gridData.overtime_summary.length > 0) {
        summaryHtml += `
            <div class="alert alert-warning mt-3">
                <h5><i class="fas fa-clock me-2"></i>Overtime Summary</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Employee ID</th>
                                <th>Employee Name</th>
                                <th>Regular Hours</th>
                                <th>Overtime Hours</th>
                                <th>Total Hours</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        gridData.overtime_summary.forEach(function(emp) {
            summaryHtml += `
                <tr>
                    <td>${emp.No}</td>
                    <td>${emp.EmployeeID}</td>
                    <td>${emp.EmployeeName}</td>
                    <td class="text-success">${emp.TotalRegular}</td>
                    <td class="text-warning">${emp.TotalOvertime}</td>
                    <td class="text-primary"><strong>${emp.TotalHours}</strong></td>
                </tr>
            `;
        });

        summaryHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Only employees with overtime hours are shown above.
                    </small>
                </div>
            </div>
        `;
    }

    // Create or update grid summary section
    let gridSummarySection = $('#gridSummarySection');
    if (gridSummarySection.length === 0) {
        // Create new section after monthlySummarySection
        $('#monthlySummarySection').after('<div id="gridSummarySection" class="row mb-4"><div class="col-12"></div></div>');
        gridSummarySection = $('#gridSummarySection');
    }

    gridSummarySection.find('.col-12').html(summaryHtml);
    gridSummarySection.show();

    // Scroll to table
    $('html, body').animate({
        scrollTop: $('#attendanceTable').offset().top - 100
    }, 500);
}

/**
 * Load attendance data from API
 */
function loadAttendanceData() {
    showLoading(true);
    
    // Hide monthly grid sync controls for custom reports
    $('#monthlyGridSyncControls').hide();
    $('#monthlyGridSyncToggleContainer').hide();

    const params = {
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    $.ajax({
        url: '/api/attendance',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                displayAttendanceData(response.data);
                showAlert(`Successfully loaded ${response.total_records} records`, 'success');
                
                // Auto-sync employee list in staging tab after loading main attendance data
                if (response.data && response.data.length > 0) {
                    console.log('Auto-syncing staging employee list with main report data');
                    autoSyncStagingEmployeeList();
                }
            } else {
                showAlert('Error loading attendance data: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('Failed to load attendance data: ' + error, 'danger');
        },
        complete: function() {
            showLoading(false);
        }
    });
}

/**
 * Auto-sync employee list with current monthly grid data
 */
function autoSyncEmployeeListWithMonthlyGrid() {
    if (!currentMonthlyGridData || !currentMonthlyGridData.grid_data || currentMonthlyGridData.grid_data.length === 0) {
        console.log('No monthly grid data available for employee sync');
        return;
    }
    
    console.log('Auto-syncing employee list with monthly grid data...');
    
    // Extract unique employees from current monthly grid
    const employeeMap = new Map();
    currentMonthlyGridData.grid_data.forEach(function(employee) {
        const employeeId = employee.EmployeeID;
        const employeeName = employee.EmployeeName;
        if (employeeId && !employeeMap.has(employeeId)) {
            employeeMap.set(employeeId, {
                employee_id: employeeId,
                employee_name: employeeName,
                display_name: `${employeeId} - ${employeeName}`,
                value: employeeId
            });
        }
    });
    
    const employees = Array.from(employeeMap.values());
    employees.sort((a, b) => a.employee_name.localeCompare(b.employee_name));
    
    const employeeSelect = $('#selectiveEmployees');
    if (employeeSelect.length > 0) {
        // Clear existing options
        employeeSelect.empty();
        
        // Add synchronized employees to select
        employees.forEach(function(employee) {
            employeeSelect.append(
                `<option value="${employee.value}">${employee.display_name}</option>`
            );
        });
        
        console.log(`✅ Synced ${employees.length} employees from monthly grid to selective employee list`);
        
        // Update selected employee count
        updateSelectedEmployeeCount();
        
        // Show brief notification
        const notification = $('#monthlyGridEmployeeSyncNotification');
        if (notification.length === 0) {
            // Create notification element if it doesn't exist
            $('#selectiveEmployees').after(`
                <div id="monthlyGridEmployeeSyncNotification" class="alert alert-success alert-sm mt-2" style="display: none;">
                    <i class="fas fa-sync me-1"></i>
                    Employee list synchronized with monthly grid (<span id="syncedEmployeeCount">${employees.length}</span> employees)
                </div>
            `);
        } else {
            $('#syncedEmployeeCount').text(employees.length);
        }
        
        $('#monthlyGridEmployeeSyncNotification').show().delay(3000).fadeOut();
        
        showAlert(`✅ Employee list synchronized with monthly grid (${employees.length} employees)`, 'success');
    }
}

/**
 * Auto-sync staging employee list with main report data
 */
function autoSyncStagingEmployeeList() {
    if (!window.currentAttendanceData || window.currentAttendanceData.length === 0) {
        console.log('No attendance data available for auto-sync');
        return;
    }
    
    console.log('Auto-syncing staging employee list...');
    
    // Extract unique employees from current attendance data
    const employeeMap = new Map();
    window.currentAttendanceData.forEach(function(record) {
        const employeeId = record.EmployeeID;
        const employeeName = record.EmployeeName;
        if (employeeId && !employeeMap.has(employeeId)) {
            employeeMap.set(employeeId, {
                employee_id: employeeId,
                employee_name: employeeName,
                display_name: `${employeeId} - ${employeeName}`,
                value: employeeId
            });
        }
    });
    
    const employees = Array.from(employeeMap.values());
    employees.sort((a, b) => a.employee_name.localeCompare(b.employee_name));
    
    const employeeSelect = $('#selectiveEmployees');
    if (employeeSelect.length > 0) {
        // Clear existing options
        employeeSelect.empty();
        
        // Add synchronized employees to select
        employees.forEach(function(employee) {
            employeeSelect.append(
                `<option value="${employee.value}">${employee.display_name}</option>`
            );
        });
        
        console.log(`✅ Auto-synced ${employees.length} employees to staging list`);
        
        // Update selected employee count
        updateSelectedEmployeeCount();
        
        // Show brief notification in staging tab
        const stagingTabNotification = $('#stagingAutoSyncNotification');
        if (stagingTabNotification.length === 0) {
            // Create notification element if it doesn't exist
            $('#selectiveEmployees').after(`
                <div id="stagingAutoSyncNotification" class="alert alert-info alert-sm mt-2" style="display: none;">
                    <i class="fas fa-sync me-1"></i>
                    Employee list synchronized with main report (${employees.length} employees)
                </div>
            `);
        }
        
        $('#stagingAutoSyncNotification').show().delay(3000).fadeOut();
    }
}

// =====================================
// STAGING FUNCTIONALITY
// =====================================

// Global staging variables
let stagingData = [];
let stagingSyncMode = true; // Default to local sync staging
let stagingSelectedRows = new Set();
let stagingDataTable = null;

/**
 * Initialize staging functionality
 */
function initializeStagingTab() {
    console.log('Initializing staging tab...');
    
    // Bind staging event handlers
    $('#stagingModeToggle').on('change', handleStagingModeToggle);
    $('#moveToStagingBtn').on('click', handleMoveToStaging);
    $('#refreshStagingBtn').on('click', loadStagingData);
    $('#prepareStagingUploadBtn').on('click', handlePrepareStagingUpload);
    $('#addStagingRecordBtn').on('click', handleAddStagingRecord);
    $('#deleteStagingSelectedBtn').on('click', handleDeleteStagingSelected);
    $('#selectAllStaging').on('change', handleSelectAllStaging);
    
    // Enhanced staging functionality
    $('#deleteAllStagingBtn').on('click', handleDeleteAllStaging);
    $('#loadEmployeesBtn').on('click', loadEmployeesForStaging);
    $('#selectiveCopyBtn').on('click', handleSelectiveCopy);
    $('#selectiveEmployees').on('change', updateSelectedEmployeeCount);
    $('#checkDuplicatesBtn').on('click', handleCheckDuplicates);
    $('#cleanupDuplicatesBtn').on('click', handleCleanupDuplicates);
    
    // Add optimized structure toggle handler
    $('#optimizeStructureToggle').on('change', function() {
        const isOptimized = $(this).prop('checked');
        console.log('Structure optimization toggled:', isOptimized);
        
        // Reload staging data with optimization setting
        loadStagingData(1000, 0, isOptimized);
        
        // Show appropriate info message
        if (isOptimized) {
            showAlert('Optimized view enabled: Data grouped by employee with raw charge job information', 'info');
        } else {
            showAlert('Standard view enabled: Flat table structure for compatibility', 'info');
        }
    });
    
    // Bind log history event handlers
    $('#refreshStagingLogsBtn').on('click', loadStagingLogs);
    $('#filterStagingLogsBtn').on('click', filterStagingLogs);
    $('#clearLogFiltersBtn').on('click', clearLogFilters);
    $('#exportStagingLogsBtn').on('click', exportStagingLogs);
    
    // Set default staging dates to current week
    setStagingDefaultDates();
    
    // Set default dates for selective copy (same as staging dates)
    setSelectiveDefaultDates();
    
    // Auto-load employees on initialization
    loadEmployeesForStaging();
    
    // Initialize selective copy button state
    updateSelectedEmployeeCount();
    
    // Set default log dates to current week
    setLogDefaultDates();
    
    // Load initial staging data and stats
    loadStagingData();
    loadStagingStats();
    
    console.log('Staging tab initialized');
}

/**
 * Set default staging dates
 */
function setStagingDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#stagingStartDate').val(formatDate(lastWeek));
    $('#stagingEndDate').val(formatDate(today));
}

/**
 * Set default selective dates
 */
function setSelectiveDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#selectiveStartDate').val(formatDate(lastWeek));
    $('#selectiveEndDate').val(formatDate(today));
}

/**
 * Set default log filter dates
 */
function setLogDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#logStartDate').val(formatDate(lastWeek));
    $('#logEndDate').val(formatDate(today));
}

/**
 * Handle staging mode toggle
 */
function handleStagingModeToggle() {
    stagingSyncMode = $('#stagingModeToggle').is(':checked');
    console.log('Staging sync mode:', stagingSyncMode ? 'Local Sync Staging' : 'External Sync');
    
    const alertBox = $('.filter-section .alert');
    if (stagingSyncMode) {
        alertBox.removeClass('alert-warning').addClass('alert-info');
        alertBox.html(`
            <i class="fas fa-info-circle me-2"></i>
            <strong>Local Sync Staging Mode Active:</strong> Data will be stored locally in staging database instead of being uploaded to Google Apps Script.
        `);
    } else {
        alertBox.removeClass('alert-info').addClass('alert-warning');
        alertBox.html(`
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>External Sync Mode Active:</strong> Data will be uploaded directly to Google Apps Script.
        `);
    }
}

/**
 * Handle move to staging button click
 */
function handleMoveToStaging() {
    const startDate = $('#stagingStartDate').val();
    const endDate = $('#stagingEndDate').val();
    const busCode = $('#stagingBusCode').val();
    
    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates', 'warning');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        showAlert('Start date cannot be later than end date', 'warning');
        return;
    }
    
    console.log('Moving records to staging:', { startDate, endDate, busCode });
    
    const moveData = {
        start_date: startDate,
        end_date: endDate,
        bus_code: busCode
    };
    
    // Show loading indicator
    $('#moveToStagingBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Moving...');
    
    // Call API to move records to staging
    $.ajax({
        url: '/api/staging/move-to-staging',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(moveData),
        success: function(response) {
            console.log('Move to staging response:', response);
            
            if (response.success) {
                showAlert(`Successfully moved ${response.moved_records} records to staging`, 'success');
                
                // Refresh staging data and stats
                loadStagingData();
                loadStagingStats();
            } else {
                showAlert('Failed to move records to staging: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error moving records to staging:', error);
            showAlert('Error moving records to staging: ' + error, 'danger');
        },
        complete: function() {
            $('#moveToStagingBtn').prop('disabled', false).html('<i class="fas fa-arrow-right me-1"></i>Move to Staging');
        }
    });
}

/**
 * Load staging data from API
 */
function loadStagingData(limit = 1000, offset = 0, optimizeStructure = false) {
    console.log('Loading staging data...');
    
    const params = {
        limit: limit,
        offset: offset,
        status: 'staged',
        optimize: optimizeStructure
    };
    
    $.ajax({
        url: '/api/staging/data',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('Staging data loaded:', response);
            console.log('Structure optimized:', response.structure_optimized);
            
            if (response.success) {
                stagingData = response.data;
                
                if (response.structure_optimized) {
                    displayOptimizedStagingData(stagingData, response);
                } else {
                    displayStagingData(stagingData);
                }
            } else {
                console.error('Failed to load staging data:', response.error);
                showAlert('Failed to load staging data: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading staging data:', error);
            showAlert('Error loading staging data: ' + error, 'danger');
        }
    });
}

/**
 * Display staging data in the table
 */
function displayStagingData(data) {
    console.log('Displaying staging data:', data.length, 'records');
    
    // Destroy existing DataTable if it exists
    if (stagingDataTable) {
        console.log('Destroying existing staging DataTable...');
        stagingDataTable.destroy();
        stagingDataTable = null;
    }
    
    const tableBody = $('#stagingTableBody');
    tableBody.empty();
    
    if (!data || data.length === 0) {
        console.log('No staging data to display');
        tableBody.html(`
            <tr>
                <td colspan="19" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No staging data available. Use "Move to Staging" to add records.
                </td>
            </tr>
        `);
        
        // Update record count
        $('#stagingRecordCount').text('0 of 0 records transferred');
        return;
    }
    
    // Update record count with transfer status information
    const successfulTransfers = data.filter(record => record.transfer_status === 'success').length;
    $('#stagingRecordCount').text(`${successfulTransfers} of ${data.length} records transferred`);
    
    // Add data rows to table body with monthly grid styling
    data.forEach(function(record, index) {
        const rowId = `staging_row_${index}`;
        const statusClass = `staging-record-status-${record.status}`;
        const transferStatusClass = `transfer-status-${record.transfer_status}`;
        
        // Create transfer status indicator
        const transferIcon = record.transfer_status === 'success' ? 
            '<i class="fas fa-check-circle text-success me-1"></i>' : 
            '<i class="fas fa-clock text-warning me-1"></i>';
        
        const row = `
            <tr id="${rowId}" data-row-index="${index}" data-staging-id="${record.id}" class="staging-row-selectable">
                <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                    <input type="checkbox" class="form-check-input staging-row-checkbox" data-row-id="${index}" data-staging-id="${record.id}">
                </td>
                <td style="position: sticky; left: 50px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                    <span class="staging-row-number">${index + 1}</span>
                </td>
                <td style="position: sticky; left: 100px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: left; font-size: 0.85rem;">${record.employee_id || ''}</td>
                <td style="position: sticky; left: 220px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center; font-size: 0.85rem; color: #17a2b8; font-weight: 500;">${record.ptrj_employee_id || 'N/A'}</td>
                <td style="position: sticky; left: 340px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: left; font-weight: 500; font-size: 0.85rem;">${record.employee_name || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">${record.date || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">${record.day_of_week || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">${record.shift || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">${record.check_in || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">${record.check_out || ''}</td>
                <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; font-weight: bold;">${record.regular_hours || '0.00'}</td>
                <td style="text-align: center; font-size: 0.85rem; background-color: #fff3cd; font-weight: bold;">${record.overtime_hours || '0.00'}</td>
                <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; font-weight: bold;">${record.total_hours || '0.00'}</td>
                <td contenteditable="true" class="staging-editable" data-field="task_code" style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8;">${record.task_code || ''}</td>
                <td contenteditable="true" class="staging-editable" data-field="station_code" style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8;">${record.station_code || ''}</td>
                <td contenteditable="true" class="staging-editable" data-field="machine_code" style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8;">${record.machine_code || ''}</td>
                <td contenteditable="true" class="staging-editable" data-field="expense_code" style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8;">${record.expense_code || ''}</td>
                <td style="text-align: center; font-size: 0.85rem;">
                    ${transferIcon}
                    <span class="${transferStatusClass}">${record.transfer_status}</span>
                    <br>
                    <small class="${statusClass}">${record.status}</small>
                </td>
                <td style="text-align: center;">
                    <button class="btn btn-staging-edit me-1" data-staging-id="${record.id}" title="Save Changes">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn btn-staging-delete" data-staging-id="${record.id}" title="Delete Record">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tableBody.append(row);
    });
    
    // Bind staging events
    bindStagingEvents();
    
    // Apply grid styling (no DataTables for consistency with monthly grid)
    $('#stagingTable').addClass('attendance-grid');
    $('#stagingTable').removeClass('dataTable'); // Remove any DataTable classes
    
    console.log('Staging data displayed with monthly grid styling - DataTables disabled for consistency');
}

/**
 * Bind staging-specific events
 */
function bindStagingEvents() {
    // Row selection
    $('.staging-row-checkbox').on('change', handleStagingRowSelection);
    
    // Row click (for selection)
    $('.staging-row-selectable').on('click', function(e) {
        if (e.target.type !== 'checkbox' && !$(e.target).hasClass('staging-editable') && !$(e.target).closest('button').length) {
            const checkbox = $(this).find('.staging-row-checkbox');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });
    
    // Editable cell blur (save on focus loss)
    $('.staging-editable').on('blur', handleStagingCellEdit);
    
    // Action buttons
    $('.btn-staging-edit').on('click', handleStagingRecordSave);
    $('.btn-staging-delete').on('click', handleStagingRecordDelete);
}

/**
 * Handle staging row selection
 */
function handleStagingRowSelection() {
    const rowIndex = $(this).data('row-id');
    const stagingId = $(this).data('staging-id');
    const isChecked = $(this).is(':checked');
    
    const row = $(`#staging_row_${rowIndex}`);
    
    if (isChecked) {
        stagingSelectedRows.add(stagingId);
        row.addClass('staging-row-selected');
    } else {
        stagingSelectedRows.delete(stagingId);
        row.removeClass('staging-row-selected');
    }
    
    // Update select all checkbox
    const totalRows = $('.staging-row-checkbox').length;
    const selectedRows = stagingSelectedRows.size;
    
    if (selectedRows === 0) {
        $('#selectAllStaging').prop('indeterminate', false).prop('checked', false);
    } else if (selectedRows === totalRows) {
        $('#selectAllStaging').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#selectAllStaging').prop('indeterminate', true);
    }
    
    // Update delete button state
    $('#deleteStagingSelectedBtn').prop('disabled', stagingSelectedRows.size === 0);
    
    console.log('Staging selected rows:', stagingSelectedRows);
}

/**
 * Handle select all staging checkbox
 */
function handleSelectAllStaging() {
    const isChecked = $(this).is(':checked');
    
    $('.staging-row-checkbox').prop('checked', isChecked).trigger('change');
}

/**
 * Handle editable cell editing
 */
function handleStagingCellEdit() {
    const cell = $(this);
    const newValue = cell.text().trim();
    const field = cell.data('field');
    const row = cell.closest('tr');
    const stagingId = row.data('staging-id');
    
    console.log('Cell edited:', { stagingId, field, newValue });
    
    // Mark row as edited (visual feedback)
    row.addClass('table-warning');
}

/**
 * Handle staging record save
 */
function handleStagingRecordSave() {
    const stagingId = $(this).data('staging-id');
    const row = $(`tr[data-staging-id="${stagingId}"]`);
    
    console.log('Saving staging record:', stagingId);
    
    // Get edited values from editable cells
    const updateData = {};
    row.find('.staging-editable').each(function() {
        const field = $(this).data('field');
        const value = $(this).text().trim();
        updateData[field] = value;
    });
    
    console.log('Update data:', updateData);
    
    // Save to API
    $.ajax({
        url: `/api/staging/data/${stagingId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(updateData),
        success: function(response) {
            console.log('Staging record saved:', response);
            
            if (response.success) {
                showAlert('Record updated successfully', 'success');
                row.removeClass('table-warning').addClass('table-success');
                setTimeout(() => row.removeClass('table-success'), 2000);
            } else {
                showAlert('Failed to update record: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error saving staging record:', error);
            showAlert('Error saving record: ' + error, 'danger');
        }
    });
}

/**
 * Handle staging record deletion
 */
function handleStagingRecordDelete() {
    const stagingId = $(this).data('staging-id');
    
    if (!confirm('Are you sure you want to delete this staging record?')) {
        return;
    }
    
    console.log('Deleting staging record:', stagingId);
    
    $.ajax({
        url: `/api/staging/data/${stagingId}`,
        method: 'DELETE',
        success: function(response) {
            console.log('Staging record deleted:', response);
            
            if (response.success) {
                showAlert('Record deleted successfully', 'success');
                
                // Remove from UI
                $(`tr[data-staging-id="${stagingId}"]`).fadeOut(function() {
                    $(this).remove();
                    
                    // Update stats
                    loadStagingStats();
                });
                
                // Remove from selected set if selected
                stagingSelectedRows.delete(stagingId);
            } else {
                showAlert('Failed to delete record: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error deleting staging record:', error);
            showAlert('Error deleting record: ' + error, 'danger');
        }
    });
}

/**
 * Handle delete selected staging records
 */
function handleDeleteStagingSelected() {
    if (stagingSelectedRows.size === 0) {
        showAlert('No records selected for deletion', 'warning');
        return;
    }
    
    if (!confirm(`Are you sure you want to delete ${stagingSelectedRows.size} selected staging records?`)) {
        return;
    }
    
    console.log('Deleting selected staging records:', stagingSelectedRows);
    
    // Delete each selected record
    const deletePromises = Array.from(stagingSelectedRows).map(stagingId => {
        return $.ajax({
            url: `/api/staging/data/${stagingId}`,
            method: 'DELETE'
        });
    });
    
    Promise.all(deletePromises)
        .then(results => {
            console.log('Bulk delete results:', results);
            showAlert(`Successfully deleted ${stagingSelectedRows.size} staging records`, 'success');
            
            // Clear selection and reload data
            stagingSelectedRows.clear();
            loadStagingData();
            loadStagingStats();
        })
        .catch(error => {
            console.error('Error in bulk delete:', error);
            showAlert('Error deleting some records: ' + error, 'danger');
            
            // Reload data to refresh state
            loadStagingData();
            loadStagingStats();
        });
}

/**
 * Load staging statistics
 */
function loadStagingStats() {
    console.log('Loading staging statistics...');
    
    $.ajax({
        url: '/api/staging/stats',
        method: 'GET',
        success: function(response) {
            console.log('Staging stats loaded:', response);
            
            if (response.success) {
                displayStagingStats(response.stats);
            } else {
                console.error('Failed to load staging stats:', response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading staging stats:', error);
        }
    });
}

/**
 * Display staging statistics
 */
function displayStagingStats(stats) {
    console.log('Displaying staging stats:', stats);
    
    // Update total records
    $('#stagingTotalRecords').text(stats.total_records || 0);
    
    // Update status counts
    const statusCounts = stats.status_counts || {};
    $('#stagingStaged').text(statusCounts.staged || 0);
    $('#stagingReady').text(statusCounts.ready_for_upload || 0);
    
    // Update date range
    const dateRange = stats.date_range || {};
    if (dateRange.earliest && dateRange.latest) {
        $('#stagingDateRange').text(`${dateRange.earliest} to ${dateRange.latest}`);
    } else {
        $('#stagingDateRange').text('No data');
    }
}

/**
 * Handle prepare staging upload
 */
function handlePrepareStagingUpload() {
    const selectedIds = Array.from(stagingSelectedRows);
    
    if (selectedIds.length === 0) {
        // Prepare all staged records
        if (!confirm('No records selected. Prepare all staged records for upload?')) {
            return;
        }
    } else {
        // Prepare selected records
        if (!confirm(`Prepare ${selectedIds.length} selected records for upload?`)) {
            return;
        }
    }
    
    console.log('Preparing staging upload for records:', selectedIds);
    
    const uploadData = selectedIds.length > 0 ? { record_ids: selectedIds } : {};
    
    $('#prepareStagingUploadBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Preparing...');
    
    $.ajax({
        url: '/api/staging/upload',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(uploadData),
        success: function(response) {
            console.log('Staging upload prepared:', response);
            
            if (response.success) {
                showAlert(`Prepared ${response.affected_records} records for upload`, 'success');
                
                // Refresh data to show updated status
                loadStagingData();
                loadStagingStats();
            } else {
                showAlert('Failed to prepare upload: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error preparing staging upload:', error);
            showAlert('Error preparing upload: ' + error, 'danger');
        },
        complete: function() {
            $('#prepareStagingUploadBtn').prop('disabled', false).html('<i class="fas fa-cloud-upload-alt me-1"></i>Prepare Upload');
        }
    });
}

/**
 * Handle delete all staging data
 */
function handleDeleteAllStaging() {
    // Double confirmation for delete all
    if (!confirm('⚠️ WARNING: This will permanently delete ALL staging data.\n\nThis action cannot be undone. Are you sure you want to continue?')) {
        return;
    }
    
    if (!confirm('🚨 FINAL CONFIRMATION: Delete ALL staging data?\n\nClick OK to proceed with complete data purge.')) {
        return;
    }
    
    console.log('Deleting all staging data...');
    
    $('#deleteAllStagingBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Deleting...');
    
    $.ajax({
        url: '/api/staging/delete-all',
        method: 'DELETE',
        success: function(response) {
            console.log('Delete all staging response:', response);
            
            if (response.success) {
                showAlert(`Successfully deleted ${response.deleted_records} staging records`, 'success');
                
                // Clear selections and refresh data
                stagingSelectedRows.clear();
                loadStagingData();
                loadStagingStats();
                
                // Reset UI elements
                $('#selectAllStaging').prop('checked', false).prop('indeterminate', false);
                $('#deleteStagingSelectedBtn').prop('disabled', true);
            } else {
                showAlert('Failed to delete all staging data: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error deleting all staging data:', error);
            showAlert('Error deleting all staging data: ' + error, 'danger');
        },
        complete: function() {
            $('#deleteAllStagingBtn').prop('disabled', false).html('<i class="fas fa-trash-alt me-1"></i>Delete All Staging Data');
        }
    });
}

/**
 * Load employees for staging selection
 */
function loadEmployeesForStaging() {
    console.log('Loading employees for staging selection...');
    
    $('#loadEmployeesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Loading...');
    
    // Get bus code to ensure consistent filtering with main report
    const busCode = $('#busCode').val() || $('#busCodeMonthly').val() || 'PTRJ';
    
    // Always use unfiltered API call to show all employees in staging tab
    // This matches the behavior of the staging data grid which shows unfiltered data
    $.ajax({
        url: '/api/staging/employees-unfiltered',
        method: 'GET',
        data: { bus_code: busCode },
        success: function(response) {
            console.log('Unfiltered employees loaded for staging from API:', response);
            
            if (response.success) {
                const employeeSelect = $('#selectiveEmployees');
                employeeSelect.empty();
                
                // Add employees to select
                response.employees.forEach(function(employee) {
                    employeeSelect.append(
                        `<option value="${employee.value}">${employee.display_name}</option>`
                    );
                });
                
                let successMessage = `Loaded ${response.total_employees} employees (unfiltered - matches staging data)`;
                
                showAlert(successMessage, 'success');
                updateSelectedEmployeeCount();
            } else {
                showAlert('Failed to load employees: ' + response.error, 'danger');
                $('#selectiveEmployees').html('<option value="" disabled>Failed to load employees</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading employees for staging:', error);
            // Fallback to filtered API if unfiltered endpoint is not available
            console.log('Falling back to filtered employee API...');
            loadEmployeesForStagingFiltered(busCode);
        },
        complete: function() {
            $('#loadEmployeesBtn').prop('disabled', false).html('<i class="fas fa-users me-1"></i>Load Employees');
        }
    });
}

/**
 * Fallback function to load filtered employees for staging (original behavior)
 */
function loadEmployeesForStagingFiltered(busCode) {
    // Prioritize monthly grid data if available, then fall back to current attendance data
    if (currentMonthlyGridData && currentMonthlyGridData.grid_data && currentMonthlyGridData.grid_data.length > 0) {
        console.log('Using current monthly grid data for employee synchronization');
        
        // Extract unique employees from current monthly grid
        const employeeMap = new Map();
        currentMonthlyGridData.grid_data.forEach(function(employee) {
            const employeeId = employee.EmployeeID;
            const employeeName = employee.EmployeeName;
            if (employeeId && !employeeMap.has(employeeId)) {
                employeeMap.set(employeeId, {
                    employee_id: employeeId,
                    employee_name: employeeName,
                    display_name: `${employeeId} - ${employeeName}`,
                    value: employeeId
                });
            }
        });
        
        const employees = Array.from(employeeMap.values());
        employees.sort((a, b) => a.employee_name.localeCompare(b.employee_name));
        
        const employeeSelect = $('#selectiveEmployees');
        employeeSelect.empty();
        
        // Add employees to select
        employees.forEach(function(employee) {
            employeeSelect.append(
                `<option value="${employee.value}">${employee.display_name}</option>`
            );
        });
        
        showAlert(`Loaded ${employees.length} employees (synchronized with monthly grid)`, 'success');
        updateSelectedEmployeeCount();
        return;
    }
    // Check if we have current attendance data that we can use for consistency  
    else if (window.currentAttendanceData && window.currentAttendanceData.length > 0) {
        console.log('Using current attendance data for employee synchronization');
        
        // Extract unique employees from current attendance data
        const employeeMap = new Map();
        window.currentAttendanceData.forEach(function(record) {
            const employeeId = record.EmployeeID;
            const employeeName = record.EmployeeName;
            if (employeeId && !employeeMap.has(employeeId)) {
                employeeMap.set(employeeId, {
                    employee_id: employeeId,
                    employee_name: employeeName,
                    display_name: `${employeeId} - ${employeeName}`,
                    value: employeeId
                });
            }
        });
        
        const employees = Array.from(employeeMap.values());
        employees.sort((a, b) => a.employee_name.localeCompare(b.employee_name));
        
        const employeeSelect = $('#selectiveEmployees');
        employeeSelect.empty();
        
        // Add employees to select
        employees.forEach(function(employee) {
            employeeSelect.append(
                `<option value="${employee.value}">${employee.display_name}</option>`
            );
        });
        
        showAlert(`Loaded ${employees.length} employees (synchronized with main report)`, 'success');
        updateSelectedEmployeeCount();
        return;
    }
    
    // Fallback to filtered API call
    $.ajax({
        url: '/api/staging/employees',
        method: 'GET',
        data: { bus_code: busCode },
        success: function(response) {
            console.log('Filtered employees loaded for staging from API:', response);
            
            if (response.success) {
                const employeeSelect = $('#selectiveEmployees');
                employeeSelect.empty();
                
                // Add employees to select
                response.employees.forEach(function(employee) {
                    employeeSelect.append(
                        `<option value="${employee.value}">${employee.display_name}</option>`
                    );
                });
                
                let successMessage = `Loaded ${response.total_employees} employees for selection`;
                if (response.total_employees_before_filtering > response.total_employees) {
                    successMessage += ` (${response.total_employees_before_filtering - response.total_employees} filtered out)`;
                }
                successMessage += ' (filtered - consider using unfiltered endpoint for consistency with staging data)';
                
                showAlert(successMessage, 'warning');
                updateSelectedEmployeeCount();
            } else {
                showAlert('Failed to load employees: ' + response.error, 'danger');
                $('#selectiveEmployees').html('<option value="" disabled>Failed to load employees</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading employees for staging:', error);
            showAlert('Error loading employees: ' + error, 'danger');
            $('#selectiveEmployees').html('<option value="" disabled>Error loading employees</option>');
        }
    });
}

/**
 * Update selected employee count display
 */
function updateSelectedEmployeeCount() {
    const selectedOptions = $('#selectiveEmployees option:selected');
    const count = selectedOptions.length;
    $('#selectedEmployeeCount').text(count);
    
    // Enable/disable copy button based on selection
    $('#selectiveCopyBtn').prop('disabled', count === 0);
}

/**
 * Handle selective copy to staging
 */
function handleSelectiveCopy() {
    const selectedEmployees = $('#selectiveEmployees').val() || [];
    const startDate = $('#selectiveStartDate').val();
    const endDate = $('#selectiveEndDate').val();
    const busCode = $('#selectiveBusCode').val() || 'PTRJ';
    
    // Validation
    if (selectedEmployees.length === 0) {
        showAlert('Please select at least one employee', 'warning');
        return;
    }
    
    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates', 'warning');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        showAlert('Start date cannot be later than end date', 'warning');
        return;
    }
    
    // Confirmation
    if (!confirm(`Copy attendance data for ${selectedEmployees.length} selected employees from ${startDate} to ${endDate}?`)) {
        return;
    }
    
    console.log('Selective copy to staging:', {
        employees: selectedEmployees,
        startDate: startDate,
        endDate: endDate,
        busCode: busCode
    });
    
    const copyData = {
        employee_ids: selectedEmployees,
        start_date: startDate,
        end_date: endDate,
        bus_code: busCode
    };
    
    $('#selectiveCopyBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Copying...');
    
    $.ajax({
        url: '/api/staging/selective-copy',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(copyData),
        success: function(response) {
            console.log('Selective copy response:', response);
            
            if (response.success) {
                // Build detailed success message
                let successMessage = `<div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Selective Copy Completed</h6>
                    <ul class="mb-2">
                        <li><strong>Employees Processed:</strong> ${response.selected_employees}</li>
                        <li><strong>Date Range:</strong> ${response.date_range}</li>`;

                if (response.new_records > 0) {
                    successMessage += `<li><strong>New Records Added:</strong> ${response.new_records}</li>`;
                }
                if (response.updated_records > 0) {
                    successMessage += `<li><strong>Existing Records Updated:</strong> ${response.updated_records}</li>`;
                }
                if (response.skipped_duplicates > 0) {
                    successMessage += `<li><strong>Duplicates Prevented:</strong> ${response.skipped_duplicates}</li>`;
                }

                successMessage += `<li><strong>Total Processed:</strong> ${response.total_processed}</li>
                    </ul>`;

                if (response.duplicate_prevention && response.duplicate_prevention.enabled) {
                    successMessage += `<p class="mb-0"><small><i class="fas fa-shield-alt me-1"></i>
                        Duplicate prevention active: ${response.duplicate_prevention.strategy}
                        (${response.duplicate_prevention.unique_constraint})</small></p>`;
                }

                successMessage += `</div>`;

                showAlert(successMessage, 'success', 8000);

                // Refresh staging data and stats
                loadStagingData();
                loadStagingStats();

                // Show detailed results if there are errors
                if (response.errors && response.errors.length > 0) {
                    console.warn('Selective copy errors:', response.errors);
                    showAlert(`${response.total_errors} errors occurred during copy. Check console for details.`, 'warning');
                }

                // Show skipped records info if any
                if (response.skipped_records && response.skipped_records.length > 0) {
                    console.info('Skipped duplicate records:', response.skipped_records);
                }
            } else {
                showAlert('Failed to copy selected data: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error in selective copy:', error);
            showAlert('Error copying selected data: ' + error, 'danger');
        },
        complete: function() {
            $('#selectiveCopyBtn').prop('disabled', false).html('<i class="fas fa-copy me-1"></i>Copy Selected Data');
            updateSelectedEmployeeCount(); // Re-enable if employees still selected
        }
    });
}

/**
 * Handle add staging record (placeholder)
 */
function handleAddStagingRecord() {
    showAlert('Add staging record functionality will be implemented in a future update', 'info');
}

/**
 * Handle check duplicates button click
 */
function handleCheckDuplicates() {
    $('#checkDuplicatesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Checking...');

    $.ajax({
        url: '/api/staging/check-duplicates',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                if (response.has_duplicates) {
                    const message = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Duplicate Records Found</h6>
                            <ul class="mb-0">
                                <li><strong>Total Records:</strong> ${response.total_records}</li>
                                <li><strong>Duplicate Sets:</strong> ${response.duplicate_sets}</li>
                                <li><strong>Total Duplicate Records:</strong> ${response.total_duplicate_records}</li>
                                <li><strong>Records to be Removed:</strong> ${response.records_that_would_be_removed}</li>
                            </ul>
                            <hr>
                            <p class="mb-0"><strong>Summary:</strong> ${response.summary}</p>
                        </div>
                    `;
                    showAlert(message, 'warning', 10000);

                    // Enable cleanup button if duplicates found
                    $('#cleanupDuplicatesBtn').prop('disabled', false);
                } else {
                    showAlert('No duplicate records found in staging database.', 'success');
                    $('#cleanupDuplicatesBtn').prop('disabled', true);
                }
            } else {
                showAlert('Failed to check for duplicates: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error checking duplicates:', error);
            showAlert('Error checking for duplicates: ' + error, 'danger');
        },
        complete: function() {
            $('#checkDuplicatesBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>Check Duplicates');
        }
    });
}

/**
 * Handle cleanup duplicates button click
 */
function handleCleanupDuplicates() {
    // Show confirmation dialog
    if (!confirm('Are you sure you want to cleanup duplicate records? This will remove older duplicate entries and keep the most recent ones. This action cannot be undone.')) {
        return;
    }

    $('#cleanupDuplicatesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Cleaning...');

    $.ajax({
        url: '/api/staging/cleanup-duplicates',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                const message = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Cleanup Completed Successfully</h6>
                        <ul class="mb-0">
                            <li><strong>Duplicate Sets Found:</strong> ${response.duplicates_found}</li>
                            <li><strong>Records Removed:</strong> ${response.records_removed}</li>
                        </ul>
                        <hr>
                        <p class="mb-0">${response.details}</p>
                    </div>
                `;
                showAlert(message, 'success', 8000);

                // Refresh staging data to show updated results
                loadStagingData();

                // Disable cleanup button after successful cleanup
                $('#cleanupDuplicatesBtn').prop('disabled', true);
            } else {
                showAlert('Failed to cleanup duplicates: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error cleaning up duplicates:', error);
            showAlert('Error cleaning up duplicates: ' + error, 'danger');
        },
        complete: function() {
            $('#cleanupDuplicatesBtn').prop('disabled', false).html('<i class="fas fa-broom me-1"></i>Cleanup Duplicates');
        }
    });
}

// Initialize staging when the staging tab is clicked
$(document).on('shown.bs.tab', '#staging-tab', function () {
    console.log('Staging tab shown, initializing...');
    initializeStagingTab();
});

/**
 * Load staging operation logs
 */
function loadStagingLogs(limit = 100, offset = 0) {
    console.log('Loading staging operation logs...');
    
    const params = {
        start_date: $('#logStartDate').val(),
        end_date: $('#logEndDate').val(),
        operation_type: $('#logOperationType').val(),
        result_status: $('#logResultStatus').val(),
        limit: limit,
        offset: offset
    };
    
    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });
    
    $.ajax({
        url: '/api/staging/logs',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('Staging logs loaded:', response);
            
            if (response.success) {
                displayStagingLogs(response.logs);
                displayLogStatistics(response.operation_statistics);
                updateLogPagination(response.pagination, response.total_logs, response.returned_logs);
            } else {
                console.error('Failed to load staging logs:', response.error);
                showAlert('Failed to load staging logs: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading staging logs:', error);
            showAlert('Error loading staging logs: ' + error, 'danger');
        }
    });
}

/**
 * Display staging operation logs
 */
function displayStagingLogs(logs) {
    console.log('Displaying staging logs:', logs.length, 'entries');
    
    const tableBody = $('#stagingLogsTableBody');
    tableBody.empty();
    
    if (!logs || logs.length === 0) {
        tableBody.html(`
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No log entries found for the selected criteria
                </td>
            </tr>
        `);
        return;
    }
    
    logs.forEach(function(log, index) {
        const timestamp = new Date(log.timestamp).toLocaleString();
        const operationClass = `log-operation-${log.operation_type.toLowerCase()}`;
        const statusClass = `log-status-${log.result_status}`;
        
        // Format operation details for display
        const detailsText = log.operation_details || 'N/A';
        const shortDetails = detailsText.length > 50 ? detailsText.substring(0, 50) + '...' : detailsText;
        
        // Format affected records
        const recordsInfo = log.affected_record_ids ? 
            `${log.data_volume} records` : 
            (log.data_volume > 0 ? `${log.data_volume} records` : '-');
        
        // Format user info
        const userInfo = log.ip_address ? 
            `${log.ip_address}` : 
            'System';
        
        const row = `
            <tr data-log-id="${log.id}">
                <td style="font-size: 0.85rem; white-space: nowrap;">${timestamp}</td>
                <td style="text-align: center;">
                    <span class="${operationClass}">${log.operation_type}</span>
                </td>
                <td style="font-size: 0.85rem;">${log.table_name}</td>
                <td class="log-details-cell" style="font-size: 0.85rem;" title="${detailsText}">${shortDetails}</td>
                <td style="text-align: center; font-size: 0.85rem;">${recordsInfo}</td>
                <td style="text-align: center;">
                    <span class="${statusClass}">${log.result_status}</span>
                </td>
                <td style="font-size: 0.85rem;">${userInfo}</td>
                <td style="text-align: center;">
                    <button class="btn btn-outline-info btn-sm" onclick="viewLogDetails('${log.id}')" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        
        tableBody.append(row);
    });
    
    console.log('Staging logs displayed successfully');
}

/**
 * Display log statistics
 */
function displayLogStatistics(stats) {
    console.log('Displaying log statistics:', stats);
    
    const statsContainer = $('#logStatsSummary');
    
    if (!stats || Object.keys(stats).length === 0) {
        statsContainer.html('<span class="text-muted">No operation statistics available</span>');
        return;
    }
    
    let statsHtml = '';
    
    Object.keys(stats).forEach(operation => {
        const opStats = stats[operation];
        const successRate = opStats.total > 0 ? ((opStats.success || 0) / opStats.total * 100).toFixed(1) : 0;
        
        statsHtml += `
            <span class="operation-stat-badge ${operation.toLowerCase()}" title="${operation} Operations">
                <strong>${operation.toUpperCase()}:</strong> ${opStats.total} total 
                (${opStats.success || 0} success, ${opStats.failure || 0} failed, ${successRate}% success rate)
            </span>
        `;
    });
    
    statsContainer.html(statsHtml);
}

/**
 * Update log pagination
 */
function updateLogPagination(pagination, totalLogs, returnedLogs) {
    const paginationSection = $('#logPaginationSection');
    const paginationInfo = $('#logPaginationInfo');
    
    if (totalLogs > 0) {
        const startRecord = pagination.offset + 1;
        const endRecord = pagination.offset + returnedLogs;
        
        paginationInfo.text(`Showing ${startRecord}-${endRecord} of ${totalLogs} log entries`);
        paginationSection.show();
        
        // Update pagination buttons
        const prevBtn = $('#logPrevPage');
        const nextBtn = $('#logNextPage');
        
        if (pagination.offset > 0) {
            prevBtn.removeClass('disabled').find('button').prop('disabled', false);
        } else {
            prevBtn.addClass('disabled').find('button').prop('disabled', true);
        }
        
        if (pagination.has_more) {
            nextBtn.removeClass('disabled').find('button').prop('disabled', false);
        } else {
            nextBtn.addClass('disabled').find('button').prop('disabled', true);
        }
    } else {
        paginationSection.hide();
    }
}

/**
 * Filter staging logs based on form inputs
 */
function filterStagingLogs() {
    console.log('Filtering staging logs...');
    loadStagingLogs(); // Reload with current filter values
}

/**
 * Clear log filters
 */
function clearLogFilters() {
    console.log('Clearing log filters...');
    
    $('#logStartDate').val('');
    $('#logEndDate').val('');
    $('#logOperationType').val('');
    $('#logResultStatus').val('');
    
    // Set default dates
    setLogDefaultDates();
    
    // Reload logs
    loadStagingLogs();
    
    showAlert('Log filters cleared', 'info');
}

/**
 * Export staging logs
 */
function exportStagingLogs() {
    console.log('Exporting staging logs...');
    
    const params = {
        start_date: $('#logStartDate').val(),
        end_date: $('#logEndDate').val(),
        operation_type: $('#logOperationType').val(),
        result_status: $('#logResultStatus').val(),
        format: 'csv',
        limit: 10000 // Large limit for export
    };
    
    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });
    
    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/staging/logs?${queryString}`;
    
    // Show loading state
    const exportBtn = $('#exportStagingLogsBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...').prop('disabled', true);
    
    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `staging-logs-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button state
    setTimeout(function() {
        exportBtn.html(originalText).prop('disabled', false);
        showAlert('Log export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * View detailed log information
 */
function viewLogDetails(logId) {
    // Implementation for viewing detailed log information
    alert(`View log details for ID: ${logId} - Feature to be implemented`);
}

// ===================================
// Database Connection Management
// ===================================

let databaseConnectionManager = {
    currentMode: 'local',
    connectionStatus: {
        local: { connected: false, error: null },
        remote: { connected: false, error: null }
    },
    
    /**
     * Initialize database connection management
     */
    init: function() {
        this.bindEvents();
        this.loadConnectionStatus();
        this.loadDatabaseConfig();
    },
    
    /**
     * Bind event listeners for database connection controls
     */
    bindEvents: function() {
        $('#databaseModeToggle').on('change', (e) => {
            this.handleModeToggle(e.target.checked);
        });
        
        $('#testConnectionBtn').on('click', () => {
            this.testCurrentConnection();
        });
        
        $('#showDatabaseConfigBtn').on('click', () => {
            this.showDatabaseConfig();
        });
        
        $('#testRemoteConnectionBtn').on('click', () => {
            this.testRemoteConnection();
        });
        
        $('#saveRemoteConfigBtn').on('click', () => {
            this.saveRemoteConfiguration();
        });
        
        $('#cancelRemoteConfigBtn').on('click', () => {
            this.cancelRemoteConfig();
        });
        
        // Auto-refresh connection status every 30 seconds
        setInterval(() => {
            this.loadConnectionStatus();
        }, 30000);
    },
    
    /**
     * Load current database connection status
     */
    loadConnectionStatus: function() {
        $.get('/api/database/status')
            .done((response) => {
                if (response.success) {
                    this.updateConnectionStatus(response.status);
                } else {
                    console.error('Failed to load database status:', response.error);
                }
            })
            .fail(() => {
                console.error('Failed to load database status');
                this.updateConnectionStatus({
                    current_mode: 'local',
                    local_status: { connected: false, error: 'Connection failed' },
                    remote_status: { connected: false, error: 'Connection failed' }
                });
            });
    },
    
    /**
     * Load database configuration
     */
    loadDatabaseConfig: function() {
        $.get('/api/database/config')
            .done((response) => {
                if (response.success) {
                    this.populateConfigForm(response.config);
                    this.currentMode = response.config.connection_mode;
                    this.updateModeToggle();
                }
            })
            .fail((error) => {
                console.error('Failed to load database config:', error);
            });
    },
    
    /**
     * Update connection status display
     */
    updateConnectionStatus: function(status) {
        this.connectionStatus = {
            local: status.local_status,
            remote: status.remote_status
        };
        this.currentMode = status.current_mode;
        
        // Update status badge
        const badge = $('#connectionStatusBadge');
        const currentStatus = status[`${this.currentMode}_status`];
        
        if (currentStatus.connected) {
            badge.html('<i class="fas fa-circle text-success"></i><span class="ms-1">Connected</span>');
            $('#connectionInfoText').text(`Connected to ${this.currentMode} database successfully.`);
            $('#databaseConnectionInfo').removeClass('alert-danger alert-warning').addClass('alert-success');
        } else {
            badge.html('<i class="fas fa-circle text-danger"></i><span class="ms-1">Disconnected</span>');
            $('#connectionInfoText').text(`Failed to connect to ${this.currentMode} database: ${currentStatus.error || 'Unknown error'}`);
            $('#databaseConnectionInfo').removeClass('alert-success alert-warning').addClass('alert-danger');
            
            // Check for auto-fallback
            this.checkAutoFallback(status);
        }
        
        this.updateModeToggle();
    },
    
    /**
     * Check for auto-fallback conditions
     */
    checkAutoFallback: function(status) {
        // If current mode failed but other mode is available, suggest fallback
        const otherMode = this.currentMode === 'local' ? 'remote' : 'local';
        const otherStatus = status[`${otherMode}_status`];
        
        if (!status[`${this.currentMode}_status`].connected && otherStatus.connected) {
            const fallbackMessage = `Primary ${this.currentMode} database is unavailable. ${otherMode.charAt(0).toUpperCase() + otherMode.slice(1)} database is available. Would you like to switch automatically?`;
            
            if (confirm(fallbackMessage)) {
                this.switchDatabaseMode(otherMode);
            }
        } else if (!status.local_status.connected && !status.remote_status.connected) {
            // Both databases failed - show critical warning
            $('#connectionInfoText').text('CRITICAL: Both local and remote databases are unavailable!');
            $('#databaseConnectionInfo').removeClass('alert-success alert-warning').addClass('alert-danger');
            
            // Show critical warning without redirect
            showAlert('CRITICAL: Both local and remote databases are unavailable! Check your database connections.', 'danger');
        }
    },
    
    /**
     * Update mode toggle display
     */
    updateModeToggle: function() {
        const toggle = $('#databaseModeToggle');
        const label = $('#databaseModeLabel');
        
        if (this.currentMode === 'remote') {
            toggle.prop('checked', true);
            label.text('Remote Database');
        } else {
            toggle.prop('checked', false);
            label.text('Local Database');
        }
    },
    
    /**
     * Handle mode toggle change
     */
    handleModeToggle: function(isRemote) {
        const newMode = isRemote ? 'remote' : 'local';
        
        if (newMode !== this.currentMode) {
            this.switchDatabaseMode(newMode);
        }
    },
    
    /**
     * Switch database connection mode
     */
    switchDatabaseMode: function(newMode) {
        showLoading(true);
        
        $.post('/api/database/switch-mode', { mode: newMode })
            .done((response) => {
                if (response.success) {
                    this.currentMode = newMode;
                    this.updateConnectionStatus(response.status);
                    showAlert(`Successfully switched to ${newMode} database`, 'success');
                } else {
                    showAlert(`Failed to switch to ${newMode} database: ${response.message}`, 'danger');
                    this.updateModeToggle(); // Revert toggle
                }
            })
            .fail(() => {
                showAlert(`Error switching to ${newMode} database`, 'danger');
                this.updateModeToggle(); // Revert toggle
            })
            .always(() => {
                showLoading(false);
            });
    },
    
    /**
     * Test current database connection
     */
    testCurrentConnection: function() {
        showLoading(true);
        
        $.post('/api/database/test-connection', { mode: this.currentMode })
            .done((response) => {
                if (response.success) {
                    showAlert(`${this.currentMode.charAt(0).toUpperCase() + this.currentMode.slice(1)} database connection successful!`, 'success');
                } else {
                    showAlert(`${this.currentMode.charAt(0).toUpperCase() + this.currentMode.slice(1)} database connection failed: ${response.message}`, 'danger');
                }
                this.updateConnectionStatus(response.status);
            })
            .fail(() => {
                showAlert(`Failed to test ${this.currentMode} database connection`, 'danger');
            })
            .always(() => {
                showLoading(false);
            });
    },
    
    /**
     * Show database configuration form
     */
    showDatabaseConfig: function() {
        const isRemote = $('#databaseModeToggle').prop('checked');
        
        if (isRemote) {
            $('#remoteDatabaseConfig').slideDown();
        } else {
            showAlert('Local database configuration is managed through config.json file', 'info');
        }
    },
    
    /**
     * Test remote database connection with form data
     */
    testRemoteConnection: function() {
        if (!this.validateRemoteForm()) {
            return;
        }
        
        const config = this.getRemoteFormData();
        showLoading(true);
        
        // Temporarily update remote config for testing
        $.post('/api/database/test-connection', { 
            mode: 'remote',
            config: config
        })
            .done((response) => {
                if (response.success) {
                    showAlert('Remote database connection test successful!', 'success');
                    $('#testRemoteConnectionBtn').removeClass('btn-outline-success').addClass('btn-success');
                } else {
                    showAlert(`Remote database connection test failed: ${response.message}`, 'danger');
                    $('#testRemoteConnectionBtn').removeClass('btn-success').addClass('btn-outline-success');
                }
            })
            .fail(() => {
                showAlert('Failed to test remote database connection', 'danger');
            })
            .always(() => {
                showLoading(false);
            });
    },
    
    /**
     * Save remote database configuration
     */
    saveRemoteConfiguration: function() {
        if (!this.validateRemoteForm()) {
            return;
        }
        
        const formData = this.getRemoteFormData();
        const config = {
            mode: 'remote',
            connection_mode: 'remote',
            database_config: {
                remote_database: formData
            }
        };
        
        showLoading(true);
        
        $.post('/api/database/update-config', config)
            .done((response) => {
                if (response.success) {
                    showAlert('Remote database configuration saved successfully!', 'success');
                    this.currentMode = 'remote';
                    this.updateConnectionStatus(response.status);
                    $('#remoteDatabaseConfig').slideUp();
                } else {
                    showAlert(`Failed to save configuration: ${response.error}`, 'danger');
                }
            })
            .fail(() => {
                showAlert('Failed to save remote database configuration', 'danger');
            })
            .always(() => {
                showLoading(false);
            });
    },
    
    /**
     * Cancel remote configuration
     */
    cancelRemoteConfig: function() {
        $('#remoteDatabaseConfig').slideUp();
        // Reset form validation
        $('#remoteDatabaseForm').removeClass('was-validated');
        $('#testRemoteConnectionBtn').removeClass('btn-success').addClass('btn-outline-success');
    },
    
    /**
     * Validate remote database form
     */
    validateRemoteForm: function() {
        const form = document.getElementById('remoteDatabaseForm');
        
        // Check IP address format
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipInput = $('#remoteServerIp');
        
        if (!ipRegex.test(ipInput.val())) {
            ipInput.addClass('is-invalid');
            return false;
        } else {
            ipInput.removeClass('is-invalid').addClass('is-valid');
        }
        
        // Validate port range
        const port = parseInt($('#remoteServerPort').val());
        const portInput = $('#remoteServerPort');
        
        if (port < 1 || port > 65535) {
            portInput.addClass('is-invalid');
            return false;
        } else {
            portInput.removeClass('is-invalid').addClass('is-valid');
        }
        
        // Check required fields
        const requiredFields = ['#remoteUsername', '#remotePassword'];
        let allValid = true;
        
        requiredFields.forEach(selector => {
            const field = $(selector);
            if (!field.val().trim()) {
                field.addClass('is-invalid');
                allValid = false;
            } else {
                field.removeClass('is-invalid').addClass('is-valid');
            }
        });
        
        form.classList.add('was-validated');
        return allValid;
    },
    
    /**
     * Get remote form data
     */
    getRemoteFormData: function() {
        return {
            server: $('#remoteServerIp').val().trim(),
            port: parseInt($('#remoteServerPort').val()),
            username: $('#remoteUsername').val().trim(),
            password: $('#remotePassword').val(),
            authentication: $('#remoteAuthMethod').val(),
            database: 'VenusHR14',
            driver: 'ODBC Driver 17 for SQL Server'
        };
    },
    
    /**
     * Populate configuration form with current settings
     */
    populateConfigForm: function(config) {
        if (config.remote_database) {
            $('#remoteServerIp').val(config.remote_database.server || '********');
            $('#remoteServerPort').val(config.remote_database.port || 1888);
            $('#remoteUsername').val(config.remote_database.username || 'sa');
            $('#remotePassword').val(''); // Don't populate password for security
            $('#remoteAuthMethod').val(config.remote_database.authentication || 'sql_server');
        }
    }
};

// Initialize database connection management when document is ready
$(document).ready(function() {
    console.log('Document ready, initializing database connection management...');
    databaseConnectionManager.init();
});

/**
 * Display optimized staging data in grouped employee format
 */
function displayOptimizedStagingData(employeesData, response) {
    console.log('Displaying optimized staging data:', employeesData.length, 'employees');
    
    // Destroy existing DataTable if it exists
    if (stagingDataTable) {
        console.log('Destroying existing staging DataTable...');
        stagingDataTable.destroy();
        stagingDataTable = null;
    }
    
    const tableBody = $('#stagingTableBody');
    tableBody.empty();
    
    if (!employeesData || employeesData.length === 0) {
        console.log('No optimized staging data to display');
        tableBody.html(`
            <tr>
                <td colspan="19" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No staging data available. Use "Move to Staging" to add records.
                </td>
            </tr>
        `);
        
        // Update record count
        $('#stagingRecordCount').text('0 of 0 records transferred');
        return;
    }
    
    // Update record count with optimized statistics
    const totalAttendanceRecords = response.returned_records || 0;
    const totalEmployees = response.total_employees || 0;
    const successfulTransfers = response.successfully_transferred || 0;
    
    $('#stagingRecordCount').text(`${successfulTransfers} of ${totalAttendanceRecords} records transferred across ${totalEmployees} employees`);
    
    // Add data rows with employee grouping
    let globalRowIndex = 0;
    employeesData.forEach(function(employee, employeeIndex) {
        // Add employee header row
        const employeeHeaderRow = `
            <tr class="employee-group-header" style="background-color: #f8f9fa; font-weight: bold;">
                <td colspan="3" style="position: sticky; left: 0; background: #f8f9fa; z-index: 5; border-right: 2px solid #dee2e6;">
                    <i class="fas fa-user me-2"></i>${employee.employee_name} (${employee.employee_id})
                </td>
                <td colspan="9" style="background: #f8f9fa; font-size: 0.85rem; color: #6c757d;">
                    Task: ${employee.task_code} | Station: ${employee.station_code} | Machine: ${employee.machine_code} | Expense: ${employee.expense_code}
                </td>
                <td colspan="6" style="background: #f8f9fa; font-size: 0.85rem; color: #6c757d;">
                    Raw Charge Job: ${employee.raw_charge_job || 'N/A'} | Records: ${employee.attendance_records.length}
                </td>
            </tr>
        `;
        tableBody.append(employeeHeaderRow);
        
        // Add attendance records for this employee
        employee.attendance_records.forEach(function(record, recordIndex) {
            const rowId = `staging_row_${globalRowIndex}`;
            const statusClass = `staging-record-status-${record.status}`;
            const transferStatusClass = `transfer-status-${record.transfer_status}`;
            
            // Create transfer status indicator
            const transferIcon = record.transfer_status === 'success' ? 
                '<i class="fas fa-check-circle text-success me-1"></i>' : 
                '<i class="fas fa-clock text-warning me-1"></i>';
            
            const row = `
                <tr id="${rowId}" data-row-index="${globalRowIndex}" data-staging-id="${record.id}" class="staging-row-selectable employee-attendance-row">
                    <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                        <input type="checkbox" class="form-check-input staging-row-checkbox" data-row-id="${globalRowIndex}" data-staging-id="${record.id}">
                    </td>
                    <td style="position: sticky; left: 50px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                        <span class="staging-row-number">${globalRowIndex + 1}</span>
                    </td>
                    <td style="position: sticky; left: 100px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: left; font-size: 0.85rem; color: #6c757d;">-</td>
                    <td style="position: sticky; left: 220px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center; font-size: 0.85rem; color: #17a2b8; font-weight: 500;">${employee.employee_id_ptrj || 'N/A'}</td>
                    <td style="position: sticky; left: 340px; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: left; font-weight: 500; font-size: 0.85rem; color: #6c757d;">-</td>
                    <td style="text-align: center; font-size: 0.85rem;">${record.date || ''}</td>
                    <td style="text-align: center; font-size: 0.85rem;">${record.day_of_week || ''}</td>
                    <td style="text-align: center; font-size: 0.85rem;">${record.shift || ''}</td>
                    <td style="text-align: center; font-size: 0.85rem;">${record.check_in || ''}</td>
                    <td style="text-align: center; font-size: 0.85rem;">${record.check_out || ''}</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; font-weight: bold;">${record.regular_hours || '0.00'}</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #fff3cd; font-weight: bold;">${record.overtime_hours || '0.00'}</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; font-weight: bold;">${record.total_hours || '0.00'}</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; color: #6c757d;">-</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; color: #6c757d;">-</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; color: #6c757d;">-</td>
                    <td style="text-align: center; font-size: 0.85rem; background-color: #e8f5e8; color: #6c757d;">-</td>
                    <td style="text-align: center; font-size: 0.85rem;">
                        ${transferIcon}
                        <span class="${transferStatusClass}">${record.transfer_status}</span>
                        <br>
                        <small class="${statusClass}">${record.status}</small>
                    </td>
                    <td style="text-align: center;">
                        <button class="btn btn-staging-edit me-1" data-staging-id="${record.id}" title="Save Changes">
                            <i class="fas fa-save"></i>
                        </button>
                        <button class="btn btn-staging-delete" data-staging-id="${record.id}" title="Delete Record">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            tableBody.append(row);
            globalRowIndex++;
        });
    });
    
    // Bind staging events
    bindStagingEvents();
    
    // Apply grid styling (no DataTables for consistency with monthly grid)
    $('#stagingTable').addClass('attendance-grid');
    $('#stagingTable').removeClass('dataTable'); // Remove any DataTable classes
    
    console.log('Optimized staging data displayed - employees grouped with attendance records nested');
}