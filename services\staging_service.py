"""
Staging Service for Attendance Report System.
Handles all staging-related business logic and operations.
Implements Single Responsibility and Dependency Inversion Principles.
"""

import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from interfaces.service_interfaces import IStagingService


class StagingService(IStagingService):
    """
    Service for managing staging operations.
    Implements business logic for staging data management.
    """
    
    def __init__(self, database_manager, config_manager, logging_manager):
        """
        Initialize staging service.
        
        Args:
            database_manager: Database manager instance
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.database_manager = database_manager
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)
        
        # Get staging configuration
        self.staging_config = config_manager.get_staging_config()
        self.max_records = self.staging_config.get('max_records', 10000)
        self.auto_cleanup_days = self.staging_config.get('auto_cleanup_days', 30)
    
    def initialize_staging_database(self) -> bool:
        """Initialize staging database and perform startup cleanup."""
        try:
            # Initialize database structure
            success = self.database_manager.initialize_staging_database()
            if not success:
                return False
            
            # Perform startup cleanup
            self.logger.info("Checking for duplicate records in staging database...")
            cleanup_result = self.cleanup_staging_duplicates()
            
            if cleanup_result['duplicates_found'] > 0:
                self.logger.info(f"Startup cleanup: removed {cleanup_result['records_removed']} duplicate records")
            else:
                self.logger.info("No duplicate records found during startup check")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize staging database: {str(e)}")
            return False
    
    def get_staging_data(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get staging data with pagination and filters.
        
        Args:
            filters: Dictionary containing filter parameters
            
        Returns:
            Dictionary with staging data and metadata
        """
        try:
            # Set default values
            limit = filters.get('limit', 1000)
            offset = filters.get('offset', 0)
            optimize_structure = filters.get('optimize', False)
            
            # Get filtered data
            staging_data = self.database_manager.get_staging_data(filters)
            
            # Get total count for pagination
            count_filters = {k: v for k, v in filters.items() if k not in ['limit', 'offset', 'optimize']}
            total_count = len(self.database_manager.get_staging_data(count_filters))
            
            # Enhance data with charge job information if requested
            if filters.get('enhance_with_charge_jobs', False):
                staging_data = self._enhance_with_charge_jobs(staging_data)
            
            # Format data based on optimization request
            if optimize_structure:
                formatted_data = self._optimize_data_structure(staging_data)
            else:
                formatted_data = staging_data
            
            return {
                'success': True,
                'data': formatted_data,
                'pagination': {
                    'total_count': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                },
                'filters_applied': filters,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get staging data: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'pagination': {'total_count': 0, 'limit': limit, 'offset': offset, 'has_more': False}
            }
    
    def add_staging_records(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Add multiple records to staging with validation and deduplication.
        
        Args:
            records: List of attendance records to add
            
        Returns:
            Dictionary with operation results
        """
        try:
            if not records:
                return {'success': False, 'error': 'No records provided', 'added_count': 0}
            
            # Check if we're approaching the maximum record limit
            current_stats = self.database_manager.get_staging_statistics()
            current_count = current_stats.get('total_attendance_records', 0)
            
            if current_count + len(records) > self.max_records:
                self.logger.warning(f"Adding {len(records)} records would exceed maximum limit of {self.max_records}")
                return {
                    'success': False,
                    'error': f'Would exceed maximum staging limit of {self.max_records} records',
                    'added_count': 0,
                    'current_count': current_count
                }
            
            added_count = 0
            failed_count = 0
            errors = []
            
            for record in records:
                try:
                    # Validate record
                    is_valid, validation_errors = self._validate_staging_record(record)
                    if not is_valid:
                        failed_count += 1
                        errors.extend(validation_errors)
                        continue
                    
                    # Add unique ID if not present
                    if 'id' not in record:
                        record['id'] = str(uuid.uuid4())
                    
                    # Set default status
                    if 'status' not in record:
                        record['status'] = 'staged'
                    
                    # Add record to staging
                    record_id = self.database_manager.add_staging_record(record)
                    if record_id:
                        added_count += 1
                    else:
                        failed_count += 1
                        errors.append(f"Failed to add record for employee {record.get('employee_name', 'unknown')}")
                        
                except Exception as e:
                    failed_count += 1
                    errors.append(f"Error adding record: {str(e)}")
            
            # Log operation
            self._log_staging_operation(
                'ADD_RECORDS',
                f"Added {added_count} records, {failed_count} failed",
                data_volume=len(records),
                result_status='success' if added_count > 0 else 'partial_failure'
            )
            
            return {
                'success': added_count > 0,
                'added_count': added_count,
                'failed_count': failed_count,
                'errors': errors[:10],  # Limit error messages
                'total_errors': len(errors)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to add staging records: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'added_count': 0,
                'failed_count': len(records) if records else 0
            }
    
    def update_staging_record(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update staging record with validation."""
        try:
            # Validate update data
            is_valid, validation_errors = self._validate_staging_record(data, is_update=True)
            if not is_valid:
                self.logger.warning(f"Validation failed for staging record update: {validation_errors}")
                return False
            
            # Perform update
            success = self.database_manager.update_staging_record(record_id, data)
            
            if success:
                self._log_staging_operation(
                    'UPDATE_RECORD',
                    f"Updated record {record_id}",
                    affected_record_ids=[record_id],
                    result_status='success'
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update staging record {record_id}: {str(e)}")
            return False
    
    def delete_staging_record(self, record_id: str) -> bool:
        """Delete staging record."""
        try:
            success = self.database_manager.delete_staging_record(record_id)
            
            if success:
                self._log_staging_operation(
                    'DELETE_RECORD',
                    f"Deleted record {record_id}",
                    affected_record_ids=[record_id],
                    result_status='success'
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete staging record {record_id}: {str(e)}")
            return False
    
    def move_to_staging(self, attendance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Move attendance records to staging with enhanced processing.
        
        Args:
            attendance_data: List of attendance records to move to staging
            
        Returns:
            Dictionary with operation results
        """
        try:
            if not attendance_data:
                return {'success': False, 'error': 'No attendance data provided', 'moved_count': 0}
            
            # Process and enhance records before staging
            processed_records = []
            
            for record in attendance_data:
                # Convert to staging format
                staging_record = self._convert_to_staging_format(record)
                processed_records.append(staging_record)
            
            # Add records to staging
            result = self.add_staging_records(processed_records)
            
            # Update result with move-specific information
            result['moved_count'] = result.get('added_count', 0)
            result['operation'] = 'move_to_staging'
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to move records to staging: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'moved_count': 0
            }
    
    def get_staging_statistics(self) -> Dict[str, Any]:
        """Get comprehensive staging statistics."""
        try:
            base_stats = self.database_manager.get_staging_statistics()
            
            # Add service-level statistics
            base_stats.update({
                'configuration': {
                    'max_records': self.max_records,
                    'auto_cleanup_days': self.auto_cleanup_days
                },
                'health_status': self.database_manager.check_staging_health(),
                'last_updated': datetime.now().isoformat()
            })
            
            return base_stats
            
        except Exception as e:
            self.logger.error(f"Failed to get staging statistics: {str(e)}")
            return {'error': str(e)}
    
    def cleanup_staging_duplicates(self) -> Dict[str, Any]:
        """Clean up duplicate staging records."""
        try:
            result = self.database_manager.cleanup_staging_duplicates()
            
            # Log cleanup operation
            if result.get('cleanup_successful', False):
                self._log_staging_operation(
                    'CLEANUP_DUPLICATES',
                    f"Removed {result['records_removed']} duplicate records",
                    data_volume=result['records_removed'],
                    result_status='success'
                )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup staging duplicates: {str(e)}")
            return {
                'duplicates_found': 0,
                'records_removed': 0,
                'cleanup_successful': False,
                'error': str(e)
            }
    
    def get_staging_health_status(self) -> Dict[str, Any]:
        """Get comprehensive staging health status."""
        try:
            health_status = self.database_manager.check_staging_health()
            stats = self.get_staging_statistics()
            
            return {
                'healthy': health_status,
                'statistics': stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get staging health status: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _validate_staging_record(self, record: Dict[str, Any], is_update: bool = False) -> tuple[bool, List[str]]:
        """
        Validate staging record data.

        Args:
            record: Record data to validate
            is_update: Whether this is an update operation

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []

        # Required fields for new records
        if not is_update:
            required_fields = ['employee_id', 'employee_name', 'date']
            for field in required_fields:
                if not record.get(field):
                    errors.append(f"Missing required field: {field}")

        # Validate date format if present
        if 'date' in record and record['date']:
            try:
                datetime.strptime(record['date'], '%Y-%m-%d')
            except ValueError:
                errors.append("Invalid date format. Expected YYYY-MM-DD")

        # Validate numeric fields
        numeric_fields = ['regular_hours', 'overtime_hours', 'total_hours']
        for field in numeric_fields:
            if field in record and record[field] is not None:
                try:
                    float(record[field])
                except (ValueError, TypeError):
                    errors.append(f"Invalid numeric value for {field}")

        # Validate boolean fields
        boolean_fields = ['is_alfa', 'is_on_leave']
        for field in boolean_fields:
            if field in record and record[field] is not None:
                if not isinstance(record[field], (bool, int)) or record[field] not in [0, 1, True, False]:
                    errors.append(f"Invalid boolean value for {field}")

        return len(errors) == 0, errors

    def _convert_to_staging_format(self, attendance_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert attendance record to staging format.

        Args:
            attendance_record: Original attendance record

        Returns:
            Record formatted for staging
        """
        staging_record = {
            'id': str(uuid.uuid4()),
            'employee_id': attendance_record.get('EmployeeID', ''),
            'employee_name': attendance_record.get('EmployeeName', ''),
            'ptrj_employee_id': attendance_record.get('PTRJEmployeeID', 'N/A'),
            'date': attendance_record.get('TADate', ''),
            'day_of_week': attendance_record.get('DayOfWeek', ''),
            'shift': attendance_record.get('Shift', ''),
            'check_in': attendance_record.get('CheckIn', ''),
            'check_out': attendance_record.get('CheckOut', ''),
            'regular_hours': float(attendance_record.get('RegularHours', 0)),
            'overtime_hours': float(attendance_record.get('OvertimeHours', 0)),
            'total_hours': float(attendance_record.get('TotalHours', 0)),
            'task_code': attendance_record.get('TaskCode', ''),
            'station_code': attendance_record.get('StationCode', ''),
            'machine_code': attendance_record.get('MachineCode', ''),
            'expense_code': attendance_record.get('ExpenseCode', ''),
            'raw_charge_job': attendance_record.get('RawChargeJob', ''),
            'leave_type_code': attendance_record.get('LeaveTypeCode', ''),
            'leave_type_description': attendance_record.get('LeaveTypeDescription', ''),
            'leave_ref_number': attendance_record.get('LeaveRefNumber', ''),
            'is_alfa': bool(attendance_record.get('IsAlfa', False)),
            'is_on_leave': bool(attendance_record.get('IsOnLeave', False)),
            'status': 'staged',
            'source_record_id': attendance_record.get('RecordID', ''),
            'notes': attendance_record.get('Notes', '')
        }

        return staging_record

    def _enhance_with_charge_jobs(self, staging_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance staging data with charge job information.

        Args:
            staging_data: List of staging records

        Returns:
            Enhanced staging data
        """
        try:
            # This would typically fetch charge job data from external service
            # For now, return data as-is
            # TODO: Implement charge job enhancement
            return staging_data

        except Exception as e:
            self.logger.warning(f"Failed to enhance staging data with charge jobs: {str(e)}")
            return staging_data

    def _optimize_data_structure(self, staging_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Optimize data structure by grouping by employee.

        Args:
            staging_data: List of staging records

        Returns:
            Optimized data structure grouped by employee
        """
        try:
            employees_data = {}

            for record in staging_data:
                emp_id = record.get('employee_id', '')
                emp_name = record.get('employee_name', '')
                employee_key = f"{emp_id}_{emp_name}"

                # Create employee entry if not exists
                if employee_key not in employees_data:
                    employees_data[employee_key] = {
                        'identitas_karyawan': {
                            'employee_id_venus': emp_id,
                            'employee_id_ptrj': record.get('ptrj_employee_id', 'N/A'),
                            'employee_name': emp_name,
                            'task_code': record.get('task_code', ''),
                            'station_code': record.get('station_code', ''),
                            'machine_code': record.get('machine_code', ''),
                            'expense_code': record.get('expense_code', ''),
                            'raw_charge_job': record.get('raw_charge_job', '')
                        },
                        'data_presensi': []
                    }

                # Add attendance record
                attendance_record = {
                    'id': record.get('id'),
                    'date': record.get('date'),
                    'day_of_week': record.get('day_of_week'),
                    'shift': record.get('shift'),
                    'check_in': record.get('check_in'),
                    'check_out': record.get('check_out'),
                    'regular_hours': round(float(record.get('regular_hours', 0)), 2),
                    'overtime_hours': round(float(record.get('overtime_hours', 0)), 2),
                    'total_hours': round(float(record.get('total_hours', 0)), 2),
                    'leave_type_code': record.get('leave_type_code'),
                    'leave_type_description': record.get('leave_type_description'),
                    'leave_ref_number': record.get('leave_ref_number'),
                    'is_alfa': record.get('is_alfa', False),
                    'is_on_leave': record.get('is_on_leave', False),
                    'status': record.get('status', 'staged'),
                    'created_at': record.get('created_at'),
                    'updated_at': record.get('updated_at')
                }

                employees_data[employee_key]['data_presensi'].append(attendance_record)

            return employees_data

        except Exception as e:
            self.logger.error(f"Failed to optimize data structure: {str(e)}")
            return {'error': str(e)}

    def _log_staging_operation(self, operation_type: str, details: str,
                              affected_record_ids: Optional[List[str]] = None,
                              data_volume: int = 0, result_status: str = 'success',
                              error_details: Optional[str] = None) -> None:
        """
        Log staging operation for audit purposes.

        Args:
            operation_type: Type of operation
            details: Operation details
            affected_record_ids: List of affected record IDs
            data_volume: Number of records affected
            result_status: Operation result status
            error_details: Error details if operation failed
        """
        try:
            # Use logging manager for operation logging
            self.logger.info(f"STAGING_OPERATION: {operation_type} - {details}")

            if result_status != 'success' and error_details:
                self.logger.error(f"STAGING_ERROR: {operation_type} - {error_details}")

            # TODO: Implement database logging to staging_operations_log table
            # This would store detailed operation logs in the database

        except Exception as e:
            self.logger.error(f"Failed to log staging operation: {str(e)}")
