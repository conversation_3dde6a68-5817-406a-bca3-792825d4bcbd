#!/usr/bin/env python3
"""
Test script untuk verifikasi integrasi PTRJ Employee ID dalam sistem
"""

import sys
import os
import json
import requests
import pyodbc
from datetime import datetime

# Add modules to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'modules'))

from modules.attendance_reporter import AttendanceReporter

def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Failed to load config.json: {str(e)}")
        return None

def test_ptrj_mill_database_connection():
    """Test koneksi ke database PTRJ Mill"""
    print("=" * 60)
    print("TESTING PTRJ MILL DATABASE CONNECTION")
    print("=" * 60)
    
    try:
        # Load configuration
        config = load_config()
        if not config:
            return False
            
        ptrj_config = config.get('database_config', {}).get('ptrj_mill_database', {})
        
        if not ptrj_config.get('enabled', False):
            print("❌ PTRJ Mill database is not enabled in configuration")
            return False
        
        # Build connection string
        server = ptrj_config.get('server', '10.0.0.7')
        port = ptrj_config.get('port', 1888)
        database = ptrj_config.get('database', 'db_ptrj_mill')
        username = ptrj_config.get('username', 'sa')
        password = ptrj_config.get('password', '')
        driver = ptrj_config.get('driver', 'ODBC Driver 17 for SQL Server')
        
        connection_string = (
            f"DRIVER={{{driver}}};"
            f"SERVER={server},{port};"
            f"DATABASE={database};"
            f"UID={username};"
            f"PWD={password};"
            f"TrustServerCertificate=yes;"
        )
        
        print("🧪 Testing database connection...")
        print(f"   🔗 Server: {server}:{port}")
        print(f"   📊 Database: {database}")
        print(f"   👤 Username: {username}")
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Test query HR_EMPLOYEE table
        cursor.execute("""
            SELECT [EmpCode], [EmpName], [Status]
            FROM [db_ptrj_mill].[dbo].[HR_EMPLOYEE]
            WHERE [Status] = '1'
            ORDER BY [EmpName]
        """)
        
        results = cursor.fetchall()
        print(f"✅ Successfully connected to PTRJ Mill database")
        print(f"   📊 Found {len(results)} active employees")
        
        # If no active employees, check what statuses are available
        if len(results) == 0:
            print("\n🔍 No active employees found. Checking all available statuses...")
            cursor.execute("""
                SELECT [Status], COUNT(*) as employee_count
                FROM [db_ptrj_mill].[dbo].[HR_EMPLOYEE]
                GROUP BY [Status]
                ORDER BY employee_count DESC
            """)
            
            status_results = cursor.fetchall()
            if status_results:
                print("📋 Available employee statuses:")
                for status, count in status_results:
                    print(f"   - {status}: {count} employees")
                
                # Get sample data from the most common status
                most_common_status = status_results[0][0]
                print(f"\n📋 Sample employees with status '{most_common_status}':")
                cursor.execute("""
                    SELECT TOP 5 [EmpCode], [EmpName], [Status]
                    FROM [db_ptrj_mill].[dbo].[HR_EMPLOYEE]
                    WHERE [Status] = ?
                    ORDER BY [EmpName]
                """, most_common_status)
                
                sample_results = cursor.fetchall()
                for i, row in enumerate(sample_results):
                    print(f"   {i+1}. {row[0]} - {row[1]} ({row[2]})")
            else:
                print("⚠️ No employee data found in HR_EMPLOYEE table")
        else:
            # Show sample data
            print("\n📋 Sample PTRJ Employee Data:")
            for i, row in enumerate(results[:5]):
                print(f"   {i+1}. {row[0]} - {row[1]} ({row[2]})")
        
        if len(results) > 5:
            print(f"   ... and {len(results) - 5} more employees")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PTRJ Mill database connection failed: {str(e)}")
        return False

def test_ptrj_employee_mapping():
    """Test PTRJ Employee mapping functionality"""
    print("\n" + "=" * 60)
    print("TESTING PTRJ EMPLOYEE MAPPING")
    print("=" * 60)
    
    try:
        # Create AttendanceReporter instance
        reporter = AttendanceReporter()
        
        # Test PTRJ mapping
        print("🔄 Getting PTRJ employee mapping...")
        ptrj_mapping = reporter.get_ptrj_employee_mapping()
        
        if not ptrj_mapping:
            print("❌ No PTRJ mapping data retrieved")
            return False
            
        print(f"✅ PTRJ mapping retrieved successfully")
        print(f"   📊 Total mapping entries: {len(ptrj_mapping)}")
        
        # Test matching function with sample names
        test_names = [
            "ANDISON",
            "Andison", 
            "andison",
            "ANDI SON",
            "Andi Son",
            "NonExistentEmployee"
        ]
        
        print("\n🧪 Testing name matching with sample data:")
        for name in test_names:
            matched_id = reporter._match_ptrj_employee_id(name, ptrj_mapping)
            print(f"   '{name}' → {matched_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ PTRJ employee mapping test failed: {str(e)}")
        return False

def test_staging_database_schema():
    """Test staging database schema untuk PTRJ Employee ID"""
    print("\n" + "=" * 60)
    print("TESTING STAGING DATABASE SCHEMA")
    print("=" * 60)
    
    try:
        import sqlite3
        
        # Connect to staging database
        staging_db_path = os.path.join('data', 'staging_attendance.db')
        
        if not os.path.exists(staging_db_path):
            print("❌ Staging database not found")
            return False
            
        conn = sqlite3.connect(staging_db_path)
        cursor = conn.cursor()
        
        # Check table schema
        cursor.execute("PRAGMA table_info(staging_attendance)")
        columns = cursor.fetchall()
        
        # Check if ptrj_employee_id column exists
        column_names = [col[1] for col in columns]
        has_ptrj_column = 'ptrj_employee_id' in column_names
        
        print(f"✅ Staging database found")
        print(f"   📁 Path: {staging_db_path}")
        print(f"   📊 Total columns: {len(columns)}")
        print(f"   🆔 ptrj_employee_id column present: {has_ptrj_column}")
        
        if has_ptrj_column:
            # Get column details
            ptrj_column = [col for col in columns if col[1] == 'ptrj_employee_id'][0]
            print(f"   📝 Column details: {ptrj_column[1]} {ptrj_column[2]} (DEFAULT: {ptrj_column[4]})")
        else:
            print("❌ ptrj_employee_id column not found in staging database")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Staging database schema test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 PTRJ EMPLOYEE ID INTEGRATION TEST")
    print("=" * 60)
    print("Testing PTRJ Employee ID implementation...")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test results
    results = {}
    
    # Test 1: Staging Database Schema
    print("\n🔧 Step 1: Testing staging database schema...")
    results['staging_schema'] = test_staging_database_schema()
    
    # Test 2: PTRJ Mill Database Connection
    print("\n🔗 Step 2: Testing PTRJ Mill database connection...")
    results['ptrj_connection'] = test_ptrj_mill_database_connection()
    
    # Test 3: PTRJ Employee Mapping
    print("\n👥 Step 3: Testing PTRJ employee mapping...")
    results['ptrj_mapping'] = test_ptrj_employee_mapping()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.ljust(20)}: {status}")
    
    print(f"\n📊 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PTRJ Employee ID integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 