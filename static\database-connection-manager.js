/**
 * Database Connection Manager Frontend
 * Handles database connection status, testing, and mode switching
 */

class DatabaseConnectionManager {
    constructor() {
        this.connectionStatus = {
            local: { connected: false, error: null },
            remote: { connected: false, error: null }
        };
        this.currentMode = 'local';
        this.initialize();
    }

    initialize() {
        console.log('Database Connection Manager initialized');
        this.loadConnectionStatus();
        this.bindEvents();
    }

    bindEvents() {
        // Bind events for database management UI if they exist
        $(document).on('click', '.test-database-connection', (e) => {
            const mode = $(e.target).data('mode') || 'all';
            this.testConnection(mode);
        });

        $(document).on('click', '.switch-database-mode', (e) => {
            const mode = $(e.target).data('mode');
            if (mode) {
                this.switchMode(mode);
            }
        });
    }

    async loadConnectionStatus() {
        try {
            const response = await fetch('/api/database/status');
            const result = await response.json();
            
            if (result.success) {
                this.updateConnectionStatus(result.status);
            } else {
                console.error('Failed to load database status:', result.error);
            }
        } catch (error) {
            console.error('Error loading database status:', error);
        }
    }

    updateConnectionStatus(status) {
        this.connectionStatus.local = status.local_status || { connected: false, error: 'Unknown' };
        this.connectionStatus.remote = status.remote_status || { connected: false, error: 'Unknown' };
        this.currentMode = status.current_mode || 'local';
        
        // Update UI elements if they exist
        this.updateConnectionIndicators();
    }

    updateConnectionIndicators() {
        // Update connection status indicators in the UI
        $('.db-status-local').removeClass('connected disconnected').addClass(
            this.connectionStatus.local.connected ? 'connected' : 'disconnected'
        );
        $('.db-status-remote').removeClass('connected disconnected').addClass(
            this.connectionStatus.remote.connected ? 'connected' : 'disconnected'
        );
        $('.current-db-mode').text(this.currentMode);
    }

    async testConnection(mode = 'all') {
        try {
            console.log(`Testing ${mode} database connection...`);
            
            const response = await fetch('/api/database/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ mode: mode })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('Database test results:', result);
                this.updateConnectionStatus(result.status);
                
                if (typeof showAlert !== 'undefined') {
                    const message = mode === 'all' 
                        ? `Connection test complete. Overall: ${result.overall_success ? 'Success' : 'Failed'}`
                        : `${mode} connection: ${result.results[mode]?.success ? 'Success' : 'Failed'}`;
                    showAlert(message, result.overall_success ? 'success' : 'warning');
                }
            } else {
                console.error('Database test failed:', result.error);
                if (typeof showAlert !== 'undefined') {
                    showAlert(`Database test failed: ${result.error}`, 'danger');
                }
            }
        } catch (error) {
            console.error('Error testing database connection:', error);
            if (typeof showAlert !== 'undefined') {
                showAlert(`Error testing database connection: ${error.message}`, 'danger');
            }
        }
    }

    async switchMode(newMode) {
        try {
            console.log(`Switching to ${newMode} database mode...`);
            
            const response = await fetch('/api/database/switch-mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ mode: newMode })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('Database mode switched successfully:', result);
                this.currentMode = result.current_mode;
                this.updateConnectionIndicators();
                
                if (typeof showAlert !== 'undefined') {
                    showAlert(`Successfully switched to ${newMode} database mode`, 'success');
                }
            } else {
                console.error('Failed to switch database mode:', result.message);
                if (typeof showAlert !== 'undefined') {
                    showAlert(`Failed to switch database mode: ${result.message}`, 'danger');
                }
            }
        } catch (error) {
            console.error('Error switching database mode:', error);
            if (typeof showAlert !== 'undefined') {
                showAlert(`Error switching database mode: ${error.message}`, 'danger');
            }
        }
    }

    getConnectionStatus() {
        return {
            local: this.connectionStatus.local,
            remote: this.connectionStatus.remote,
            currentMode: this.currentMode
        };
    }

    isConnected() {
        return this.connectionStatus[this.currentMode]?.connected || false;
    }
}

// Initialize database connection manager when DOM is ready
$(document).ready(function() {
    if (typeof window.dbConnectionManager === 'undefined') {
        window.dbConnectionManager = new DatabaseConnectionManager();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseConnectionManager;
} 