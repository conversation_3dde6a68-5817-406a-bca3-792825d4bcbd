"""
Attendance Reporter module for VenusHR14 database.
Generates attendance reports based on data from the HR_T_TAMachine_Summary table.
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd

# Add the analyze_venus directory to the Python path
analyze_venus_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../analyze_venus/src'))
if os.path.exists(analyze_venus_path):
    sys.path.insert(0, analyze_venus_path)
else:
    # Try alternative path
    alt_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../analyze_venus/src'))
    if os.path.exists(alt_path):
        sys.path.insert(0, alt_path)
    else:
        # Try direct path
        direct_path = r'd:\Gawean Rebinmas\Venus Auto Fill\analyze_venus\src'
        if os.path.exists(direct_path):
            sys.path.insert(0, direct_path)

from db_connection import DatabaseConnection
from export_manager import ExportManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='../attendance_report.log'
)
logger = logging.getLogger(__name__)

class AttendanceReporter:
    """
    Generates attendance reports based on data from HR_T_TAMachine_Summary.
    """

    def __init__(self):
        """
        Initialize the attendance reporter.
        """
        self.db_connection = DatabaseConnection()
        self.export_manager = ExportManager(export_dir=os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports')))

        # Create exports directory if it doesn't exist
        os.makedirs(os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports')), exist_ok=True)

    def get_attendance_data(self, start_date: str, end_date: str, bus_code: str = None) -> List[Dict[str, Any]]:
        """
        Fetch enhanced attendance data from the database for a given date range.
        Includes employee names, proper work hours calculation with business rules,
        shift information, and overtime data.

        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            bus_code: Optional business code filter

        Returns:
            List of dictionaries with enhanced attendance data
        """
        query = """
        SELECT
            t.BusCode,
            t.EmployeeID,
            COALESCE(emp.EmployeeName, t.UserDeviceName) as EmployeeName,
            t.TADate,
            t.Shift,
            t.TACheckIn,
            t.TACheckOut,
            -- Calculate total minutes worked
            CASE
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL
                THEN DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut)
                ELSE 0
            END as TotalMinutesWorked,
            -- Calculate regular work hours based on business rules
            CASE
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL THEN
                    CASE
                        -- Saturday: Maximum 5 hours (300 minutes)
                        WHEN DATENAME(WEEKDAY, t.TADate) = 'Saturday' THEN
                            CASE
                                WHEN (DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) - ISNULL(ot.TotalOvertimeMinutes, 0)) > 300
                                THEN 5.0
                                ELSE (DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) - ISNULL(ot.TotalOvertimeMinutes, 0)) / 60.0
                            END
                        -- Weekdays: Maximum 7 hours (420 minutes)
                        ELSE
                            CASE
                                WHEN (DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) - ISNULL(ot.TotalOvertimeMinutes, 0)) > 420
                                THEN 7.0
                                ELSE (DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) - ISNULL(ot.TotalOvertimeMinutes, 0)) / 60.0
                            END
                    END
                ELSE 0
            END as RegularHours,
            -- Overtime hours from HR_T_Overtime table
            ISNULL(ot.OTHourDuration, 0) as OvertimeHours,
            -- Day of week for reference
            DATENAME(WEEKDAY, t.TADate) as DayOfWeek
        FROM
            HR_T_TAMachine_Summary t
        LEFT JOIN
            HR_M_EmployeePI emp ON t.EmployeeID = emp.EmployeeID
        LEFT JOIN (
            SELECT
                EmployeeID,
                OTDate,
                SUM(ISNULL(OTTimeDuration, 0)) as TotalOvertimeMinutes,
                SUM(ISNULL(OTHourDuration, 0)) as OTHourDuration
            FROM HR_T_Overtime
            GROUP BY EmployeeID, OTDate
        ) ot ON t.EmployeeID = ot.EmployeeID AND t.TADate = ot.OTDate
        WHERE
            t.TADate BETWEEN ? AND ?
        """

        params = [start_date, end_date]

        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)

        query += " ORDER BY t.TADate DESC, t.EmployeeID"

        try:
            self.db_connection.connect()
            results = self.db_connection.execute_query(query, tuple(params))
            logger.info(f"Retrieved {len(results)} enhanced attendance records")
            return results
        except Exception as e:
            logger.error(f"Error retrieving attendance data: {str(e)}")
            return []
        finally:
            self.db_connection.disconnect()

    def calculate_work_hours(self, check_in: str, check_out: str) -> float:
        """
        Calculate work hours from check-in and check-out times.

        Args:
            check_in: Check-in time (HH:MM)
            check_out: Check-out time (HH:MM)

        Returns:
            Work hours as decimal number
        """
        if not check_in or not check_out:
            return 0

        try:
            # Parse times
            in_time = datetime.strptime(check_in, "%H:%M")
            out_time = datetime.strptime(check_out, "%H:%M")

            # Calculate difference in hours
            delta = out_time - in_time
            hours = delta.total_seconds() / 3600

            return round(hours, 2)
        except Exception as e:
            logger.error(f"Error calculating work hours: {str(e)}")
            return 0

    def generate_monthly_report(self, year: int, month: int, bus_code: str = None) -> str:
        """
        Generate a monthly attendance report.

        Args:
            year: Year for the report
            month: Month for the report (1-12)
            bus_code: Optional business code filter

        Returns:
            Path to the exported Excel file
        """
        # Calculate start and end dates for the month
        start_date = f"{year}-{month:02d}-01"

        # Calculate the last day of the month
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        # Get attendance data
        data = self.get_attendance_data(start_date, end_date, bus_code)

        if not data:
            logger.warning(f"No attendance data found for {year}-{month:02d}")
            return ""

        # Convert to DataFrame for easier manipulation
        df = pd.DataFrame(data)

        # Format dates and times
        if 'TADate' in df.columns:
            df['TADate'] = pd.to_datetime(df['TADate']).dt.date

        # Generate Excel report
        report_name = f"Attendance_Report_{year}_{month:02d}"
        if bus_code:
            report_name += f"_{bus_code}"

        excel_path = self.export_manager.export_to_excel(data, report_name)
        logger.info(f"Generated monthly report: {excel_path}")

        return excel_path

    def generate_daily_report(self, date: str, bus_code: str = None) -> str:
        """
        Generate a daily attendance report.

        Args:
            date: Date for the report in 'YYYY-MM-DD' format
            bus_code: Optional business code filter

        Returns:
            Path to the exported Excel file
        """
        # Get attendance data
        data = self.get_attendance_data(date, date, bus_code)

        if not data:
            logger.warning(f"No attendance data found for {date}")
            return ""

        # Generate Excel report
        report_name = f"Attendance_Report_{date}"
        if bus_code:
            report_name += f"_{bus_code}"

        excel_path = self.export_manager.export_to_excel(data, report_name)
        logger.info(f"Generated daily report: {excel_path}")

        return excel_path

    def generate_date_range_report(self, start_date: str, end_date: str, bus_code: str = None) -> str:
        """
        Generate an attendance report for a date range.

        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            bus_code: Optional business code filter

        Returns:
            Path to the exported Excel file
        """
        # Get attendance data
        data = self.get_attendance_data(start_date, end_date, bus_code)

        if not data:
            logger.warning(f"No attendance data found for {start_date} to {end_date}")
            return ""

        # Generate Excel report
        report_name = f"Attendance_Report_{start_date}_to_{end_date}"
        if bus_code:
            report_name += f"_{bus_code}"

        excel_path = self.export_manager.export_to_excel(data, report_name)
        logger.info(f"Generated date range report: {excel_path}")

        return excel_path

    def get_employees_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """
        Get list of employees for filtering purposes.

        Args:
            bus_code: Optional business code filter

        Returns:
            List of dictionaries with employee information
        """
        # Create a fresh database connection for this operation
        from db_connection import DatabaseConnection
        db_conn = DatabaseConnection()
        
        query = """
        SELECT DISTINCT
            emp.EmployeeID,
            emp.EmployeeName,
            emp.BusCode
        FROM HR_M_EmployeePI emp
        WHERE emp.EmployeeID IS NOT NULL
        """

        params = []

        if bus_code:
            query += " AND emp.BusCode = ?"
            params.append(bus_code)

        query += " ORDER BY emp.EmployeeName"

        try:
            db_conn.connect()
            results = db_conn.execute_query(query, tuple(params) if params else None)
            logger.info(f"Retrieved {len(results)} employees")
            return results
        except Exception as e:
            logger.error(f"Error retrieving employees list: {str(e)}")
            return []
        finally:
            try:
                db_conn.disconnect()
            except Exception as disconnect_error:
                logger.error(f"Error disconnecting database: {disconnect_error}")

    def get_shifts_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """
        Get list of available shifts for filtering purposes.

        Args:
            bus_code: Optional business code filter

        Returns:
            List of dictionaries with shift information
        """
        # Create a fresh database connection for this operation
        from db_connection import DatabaseConnection
        db_conn = DatabaseConnection()
        
        query = """
        SELECT DISTINCT
            t.Shift
        FROM HR_T_TAMachine_Summary t
        WHERE t.Shift IS NOT NULL
        """

        params = []

        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)

        query += " ORDER BY t.Shift"

        try:
            db_conn.connect()
            results = db_conn.execute_query(query, tuple(params) if params else None)
            logger.info(f"Retrieved {len(results)} shifts")
            return results
        except Exception as e:
            logger.error(f"Error retrieving shifts list: {str(e)}")
            return []
        finally:
            try:
                db_conn.disconnect()
            except Exception as disconnect_error:
                logger.error(f"Error disconnecting database: {disconnect_error}")

    def get_available_months(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """
        Get list of available months with data for monthly grouping.

        Args:
            bus_code: Optional business code filter

        Returns:
            List of dictionaries with month information and record counts
        """
        # Create a fresh database connection for this operation
        from db_connection import DatabaseConnection
        db_conn = DatabaseConnection()
        
        query = """
        SELECT
            YEAR(t.TADate) as Year,
            MONTH(t.TADate) as Month,
            DATENAME(MONTH, t.TADate) as MonthName,
            COUNT(*) as RecordCount,
            COUNT(DISTINCT t.EmployeeID) as EmployeeCount,
            MIN(t.TADate) as FirstDate,
            MAX(t.TADate) as LastDate
        FROM HR_T_TAMachine_Summary t
        WHERE t.TADate IS NOT NULL
        """

        params = []

        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)

        query += """
        GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
        ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
        """

        try:
            db_conn.connect()
            logger.info(f"Executing months query with params: {params}")
            logger.info(f"Query: {query}")
            results = db_conn.execute_query(query, tuple(params) if params else None)
            logger.info(f"Retrieved {len(results)} available months")

            # Log first few results for debugging
            if results:
                logger.info(f"Sample results: {results[:2]}")
            else:
                logger.warning("No months found - checking if table has data")
                # Quick check if table has any data
                test_query = "SELECT COUNT(*) as count FROM HR_T_TAMachine_Summary"
                test_result = db_conn.execute_query(test_query)
                total_count = test_result[0]['count'] if test_result else 0
                logger.info(f"Total records in HR_T_TAMachine_Summary: {total_count}")

                if total_count > 0:
                    # Check for NULL dates
                    null_check = "SELECT COUNT(*) as null_count FROM HR_T_TAMachine_Summary WHERE TADate IS NULL"
                    null_result = db_conn.execute_query(null_check)
                    null_count = null_result[0]['null_count'] if null_result else 0
                    logger.info(f"Records with NULL TADate: {null_count}")

            return results
        except Exception as e:
            logger.error(f"Error retrieving available months: {str(e)}")
            logger.error(f"Query was: {query}")
            logger.error(f"Params were: {params}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return []
        finally:
            try:
                db_conn.disconnect()
            except Exception as disconnect_error:
                logger.error(f"Error disconnecting database: {disconnect_error}")

    def get_monthly_summary(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """
        Get summary statistics for a specific month.

        Args:
            year: Year for the summary
            month: Month for the summary (1-12)
            bus_code: Optional business code filter

        Returns:
            Dictionary with monthly summary statistics
        """
        # Calculate start and end dates for the month
        start_date = f"{year}-{month:02d}-01"

        # Calculate the last day of the month
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        from datetime import datetime, timedelta
        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        # Get attendance data for the month
        data = self.get_attendance_data(start_date, end_date, bus_code)

        if not data:
            return {
                'year': year,
                'month': month,
                'month_name': datetime(year, month, 1).strftime('%B'),
                'total_employees': 0,
                'total_records': 0,
                'total_regular_hours': 0,
                'total_overtime_hours': 0,
                'avg_regular_hours': 0,
                'avg_overtime_hours': 0,
                'working_days': 0,
                'date_range': f"{start_date} to {end_date}"
            }

        # Calculate summary statistics
        total_employees = len(set(record.get('EmployeeID') for record in data))
        total_records = len(data)
        total_regular_hours = sum(float(record.get('RegularHours', 0)) for record in data)
        total_overtime_hours = sum(float(record.get('OvertimeHours', 0)) for record in data)

        # Calculate working days (unique dates)
        working_days = len(set(record.get('TADate') for record in data if record.get('TADate')))

        # Calculate averages
        avg_regular_hours = total_regular_hours / total_employees if total_employees > 0 else 0
        avg_overtime_hours = total_overtime_hours / total_employees if total_employees > 0 else 0

        return {
            'year': year,
            'month': month,
            'month_name': datetime(year, month, 1).strftime('%B'),
            'total_employees': total_employees,
            'total_records': total_records,
            'total_regular_hours': round(total_regular_hours, 2),
            'total_overtime_hours': round(total_overtime_hours, 2),
            'avg_regular_hours': round(avg_regular_hours, 2),
            'avg_overtime_hours': round(avg_overtime_hours, 2),
            'working_days': working_days,
            'date_range': f"{start_date} to {end_date}"
        }

    def get_monthly_attendance_grid(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """
        Get monthly attendance data in grid format with working hours calculation.
        Shows actual working hours per day with business rules applied.
        Now includes station information for each employee.

        Args:
            year: Year for the report
            month: Month for the report (1-12)
            bus_code: Optional business code filter

        Returns:
            Dictionary with grid data and overtime summary, including station categorization
        """
        from datetime import datetime, timedelta
        import calendar

        # Calculate start and end dates for the month
        start_date = f"{year}-{month:02d}-01"
        days_in_month = calendar.monthrange(year, month)[1]
        end_date = f"{year}-{month:02d}-{days_in_month:02d}"

        # Get all employees
        employees = self.get_employees_list(bus_code)

        # Get attendance data for the month
        attendance_data = self.get_attendance_data(start_date, end_date, bus_code)

        # Create a dictionary for quick lookup: {employee_id: {date: record}}
        attendance_lookup = {}
        for record in attendance_data:
            emp_id = record.get('EmployeeID')
            date_str = record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None

            if emp_id and date_str:
                if emp_id not in attendance_lookup:
                    attendance_lookup[emp_id] = {}
                attendance_lookup[emp_id][date_str] = record

        # Build the grid data
        grid_data = []
        overtime_summary = []
        
        # Group employees by station for organized display
        stations_grid = {}
        unassigned_employees = []

        for i, employee in enumerate(employees, 1):
            emp_id = employee.get('EmployeeID')
            emp_name = employee.get('EmployeeName')
            
            # Get station information for this employee
            station_name = self.get_employee_station(emp_name)

            # Create row data
            row_data = {
                'No': i,
                'EmployeeID': emp_id,
                'EmployeeName': emp_name,
                'Station': station_name,
                'days': {}
            }

            # Track totals for overtime summary
            total_regular = 0
            total_overtime = 0

            # Add working hours for each day of the month
            for day in range(1, days_in_month + 1):
                date_str = f"{year}-{month:02d}-{day:02d}"
                date_obj = datetime(year, month, day)
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday

                # Get attendance record for this day
                if emp_id in attendance_lookup and date_str in attendance_lookup[emp_id]:
                    record = attendance_lookup[emp_id][date_str]
                    hours_data = self.calculate_working_hours_from_record(record, weekday)
                    row_data['days'][str(day)] = hours_data['display']
                    
                    # Store status information for visual indicators (add to display data)
                    row_data['days'][f'{day}_status'] = {
                        'status': hours_data['status'],
                        'check_in_only': hours_data['check_in_only'],
                        'check_out_only': hours_data['check_out_only'],
                        'complete_record': hours_data['complete_record'],
                        'regular': hours_data['regular'],
                        'overtime': hours_data['overtime']
                    }
                    
                    total_regular += hours_data['regular']
                    total_overtime += hours_data['overtime']
                else:
                    # No attendance record
                    if weekday == 6:  # Sunday
                        row_data['days'][str(day)] = 'OFF'
                        row_data['days'][f'{day}_status'] = {
                            'status': 'sunday_off',
                            'check_in_only': False,
                            'check_out_only': False,
                            'complete_record': False,
                            'regular': 0,
                            'overtime': 0
                        }
                    else:
                        row_data['days'][str(day)] = '-'
                        row_data['days'][f'{day}_status'] = {
                            'status': 'absent',
                            'check_in_only': False,
                            'check_out_only': False,
                            'complete_record': False,
                            'regular': 0,
                            'overtime': 0
                        }

            # Group by station
            if station_name == 'Unknown':
                unassigned_employees.append(row_data)
            else:
                if station_name not in stations_grid:
                    stations_grid[station_name] = []
                stations_grid[station_name].append(row_data)

            grid_data.append(row_data)

            # Add to overtime summary if there's overtime
            if total_overtime > 0:
                overtime_summary.append({
                    'No': i,
                    'EmployeeID': emp_id,
                    'EmployeeName': emp_name,
                    'TotalRegular': round(total_regular, 2),
                    'TotalOvertime': round(total_overtime, 2),
                    'TotalHours': round(total_regular + total_overtime, 2)
                })

        # Add unassigned employees to stations grid
        if unassigned_employees:
            stations_grid['Unassigned'] = unassigned_employees

        # Calculate working days (exclude Sundays)
        total_working_days = sum(1 for day in range(1, days_in_month + 1)
                               if datetime(year, month, day).weekday() != 6)

        return {
            'year': year,
            'month': month,
            'month_name': datetime(year, month, 1).strftime('%B'),
            'days_in_month': days_in_month,
            'total_employees': len(employees),
            'total_working_days': total_working_days,
            'grid_data': grid_data,
            'stations_grid': stations_grid,  # Include station grouping
            'total_stations': len(stations_grid),
            'overtime_summary': overtime_summary,
            'date_range': f"{start_date} to {end_date}"
        }

    def calculate_working_hours_from_record(self, record: Dict[str, Any], weekday: int) -> Dict[str, Any]:
        """
        Calculate working hours from attendance record with business rules.
        Returns format: "(jam kerja normal) | (jam overtime)" or "-" if no overtime.

        Args:
            record: Attendance record with check-in/out times
            weekday: Day of week (0=Monday, 6=Sunday)

        Returns:
            dict: Contains display value, regular hours, overtime hours, and status indicators
        """
        check_in = record.get('TACheckIn')
        check_out = record.get('TACheckOut')
        
        # Debug logging
        logger.info(f"Processing record: EmployeeID={record.get('EmployeeID')}, Date={record.get('TADate')}")
        logger.info(f"Check-in: {check_in} (type: {type(check_in)})")
        logger.info(f"Check-out: {check_out} (type: {type(check_out)})")

        # Get overtime hours directly from the record (from HR_T_Overtime table)
        overtime_hours = record.get('OvertimeHours', 0) or 0

        # Handle Sunday case - check for overtime first
        if weekday == 6:  # Sunday
            if overtime_hours > 0:
                # Sunday with overtime
                overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
                display = f"(0) | ({overtime_display})"
                return {
                    'display': display,
                    'regular': 0,
                    'overtime': overtime_hours,
                    'status': 'sunday_with_overtime',
                    'check_in_only': False,
                    'check_out_only': False,
                    'complete_record': bool(check_in and check_out)
                }
            else:
                # Sunday without overtime
                return {
                    'display': 'OFF',
                    'regular': 0,
                    'overtime': 0,
                    'status': 'sunday_off',
                    'check_in_only': False,
                    'check_out_only': False,
                    'complete_record': bool(check_in and check_out)
                }

        # ENHANCED LOGIC: Handle incomplete records with default hours
        has_check_in = bool(check_in)
        has_check_out = bool(check_out)
        
        # Determine default regular hours based on weekday
        if weekday == 5:  # Saturday
            default_regular_hours = 5.0
            max_regular_hours = 5.0
        else:  # Monday to Friday
            default_regular_hours = 7.0
            max_regular_hours = 7.0

        # Handle incomplete records (missing check-in OR check-out)
        if not has_check_in and has_check_out:
            # Only check-out exists - assign default hours with purple indicator
            logger.info(f"Incomplete record: Only check-out exists, assigning {default_regular_hours} hours")
            regular_display = f"{default_regular_hours:.1f}".rstrip('0').rstrip('.')
            
            if overtime_hours > 0:
                overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
            else:
                overtime_display = "-"
                
            display = f"({regular_display}) | ({overtime_display})"
            
            return {
                'display': display,
                'regular': default_regular_hours,
                'overtime': overtime_hours,
                'status': 'check_out_only',
                'check_in_only': False,
                'check_out_only': True,
                'complete_record': False
            }
            
        elif has_check_in and not has_check_out:
            # Only check-in exists - assign default hours with blue indicator
            logger.info(f"Incomplete record: Only check-in exists, assigning {default_regular_hours} hours")
            regular_display = f"{default_regular_hours:.1f}".rstrip('0').rstrip('.')
            
            if overtime_hours > 0:
                overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
            else:
                overtime_display = "-"
                
            display = f"({regular_display}) | ({overtime_display})"
            
            return {
                'display': display,
                'regular': default_regular_hours,
                'overtime': overtime_hours,
                'status': 'check_in_only',
                'check_in_only': True,
                'check_out_only': False,
                'complete_record': False
            }
            
        elif not has_check_in and not has_check_out:
            # Neither check-in nor check-out exists
            logger.info("Missing both check-in and check-out times")
            # Check if there's overtime on a day without attendance
            if overtime_hours > 0:
                overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
                display = f"(0) | ({overtime_display})"
                return {
                    'display': display,
                    'regular': 0,
                    'overtime': overtime_hours,
                    'status': 'overtime_only',
                    'check_in_only': False,
                    'check_out_only': False,
                    'complete_record': False
                }
            else:
                return {
                    'display': '-',
                    'regular': 0,
                    'overtime': 0,
                    'status': 'absent',
                    'check_in_only': False,
                    'check_out_only': False,
                    'complete_record': False
                }

        # Complete records - both check-in and check-out exist
        # Calculate total hours worked from check-in/out
        try:
            # Handle different time formats and types
            check_in_dt = None
            check_out_dt = None

            # Convert check_in
            if isinstance(check_in, str):
                # Try different string formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%H:%M:%S', '%H:%M']:
                    try:
                        check_in_dt = datetime.strptime(check_in, fmt)
                        break
                    except ValueError:
                        continue
            elif hasattr(check_in, 'hour'):  # datetime.time or datetime.datetime
                if hasattr(check_in, 'date'):  # datetime.datetime
                    check_in_dt = check_in
                else:  # datetime.time
                    today = datetime.now().date()
                    check_in_dt = datetime.combine(today, check_in)

            # Convert check_out
            if isinstance(check_out, str):
                # Try different string formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%H:%M:%S', '%H:%M']:
                    try:
                        check_out_dt = datetime.strptime(check_out, fmt)
                        break
                    except ValueError:
                        continue
            elif hasattr(check_out, 'hour'):  # datetime.time or datetime.datetime
                if hasattr(check_out, 'date'):  # datetime.datetime
                    check_out_dt = check_out
                else:  # datetime.time
                    today = datetime.now().date()
                    check_out_dt = datetime.combine(today, check_out)

            if not check_in_dt or not check_out_dt:
                logger.error(f"Could not parse times: check_in={check_in}, check_out={check_out}")
                # Fallback to default hours for parsing failures
                regular_display = f"{default_regular_hours:.1f}".rstrip('0').rstrip('.')
                if overtime_hours > 0:
                    overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
                    display = f"({regular_display}) | ({overtime_display})"
                else:
                    overtime_display = "-"
                    display = f"({regular_display}) | ({overtime_display})"
                    
                return {
                    'display': display,
                    'regular': default_regular_hours,
                    'overtime': overtime_hours,
                    'status': 'parse_error',
                    'check_in_only': False,
                    'check_out_only': False,
                    'complete_record': True
                }

            # If only time part was parsed, use same date for both
            if check_in_dt.date() == datetime.min.date():
                today = datetime.now().date()
                check_in_dt = datetime.combine(today, check_in_dt.time())
                check_out_dt = datetime.combine(today, check_out_dt.time())

            # Handle overnight shifts
            if check_out_dt < check_in_dt:
                check_out_dt += timedelta(days=1)

            # Calculate total hours from check-in/out
            total_hours_from_attendance = (check_out_dt - check_in_dt).total_seconds() / 3600

            logger.info(f"Calculated hours from attendance: {total_hours_from_attendance}")

        except Exception as e:
            logger.error(f"Error calculating hours: {str(e)}")
            logger.error(f"Check-in: {check_in}, Check-out: {check_out}")
            # Fallback to default hours for calculation errors
            regular_display = f"{default_regular_hours:.1f}".rstrip('0').rstrip('.')
            if overtime_hours > 0:
                overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
                display = f"({regular_display}) | ({overtime_display})"
            else:
                overtime_display = "-"
                display = f"({regular_display}) | ({overtime_display})"
                
            return {
                'display': display,
                'regular': default_regular_hours,
                'overtime': overtime_hours,
                'status': 'calculation_error',
                'check_in_only': False,
                'check_out_only': False,
                'complete_record': True
            }

        # Apply business rules: cap regular hours at max_regular_hours
        regular_hours = min(total_hours_from_attendance, max_regular_hours)

        # Determine status for complete records
        status = 'complete'
        if regular_hours > max_regular_hours:
            status = 'dark_green'  # More than 7 hours with complete record
        elif regular_hours > 0 and overtime_hours == 0:
            status = 'light_green'  # Regular hours present, no overtime
        elif regular_hours > 0 and overtime_hours > 0:
            status = 'with_overtime'
        else:
            status = 'complete'

        # Format display value: "(jam kerja normal) | (jam overtime)"
        # If no overtime, show "(-)" for the overtime part
        regular_display = f"{regular_hours:.1f}".rstrip('0').rstrip('.')
        
        if overtime_hours > 0:
            overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
        else:
            overtime_display = "-"
            
        display = f"({regular_display}) | ({overtime_display})"

        logger.info(f"Final display: {display}, regular: {regular_hours}, overtime: {overtime_hours}, status: {status}")

        return {
            'display': display,
            'regular': regular_hours,
            'overtime': overtime_hours,
            'status': status,
            'check_in_only': False,
            'check_out_only': False,
            'complete_record': True
        }

    def load_employee_stations(self) -> Dict[str, Any]:
        """
        Load employee station data from JSON file.

        Returns:
            dict: Station data with employee assignments
        """
        try:
            import json
            stations_file = os.path.abspath(os.path.join(os.path.dirname(__file__), '../data/employee_stations.json'))

            if os.path.exists(stations_file):
                with open(stations_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"Employee stations file not found: {stations_file}")
                return {"stations": []}

        except Exception as e:
            logger.error(f"Error loading employee stations: {str(e)}")
            return {"stations": []}

    def get_employee_station(self, employee_name: str) -> str:
        """
        Get station name for a given employee.

        Args:
            employee_name: Name of the employee

        Returns:
            str: Station name or 'Unknown' if not found
        """
        stations_data = self.load_employee_stations()

        for station in stations_data.get('stations', []):
            for employee in station.get('employees', []):
                if employee.get('name', '').upper() == employee_name.upper():
                    return station.get('station_name', 'Unknown')

        return 'Unknown'

    def get_monthly_attendance_grid_by_station(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """
        Get monthly attendance data grouped by stations.

        Args:
            year: Year for the report
            month: Month for the report (1-12)
            bus_code: Optional business code filter

        Returns:
            dict: Grid data grouped by stations
        """
        try:
            # Get regular grid data
            grid_data = self.get_monthly_attendance_grid(year, month, bus_code)

            if not grid_data.get('success', True):
                return grid_data

            # Load station data
            stations_data = self.load_employee_stations()

            # Group employees by station
            stations_grid = {}
            unassigned_employees = []

            for employee in grid_data.get('grid_data', []):
                emp_name = employee.get('EmployeeName', '')
                station_name = self.get_employee_station(emp_name)

                if station_name == 'Unknown':
                    unassigned_employees.append(employee)
                else:
                    if station_name not in stations_grid:
                        stations_grid[station_name] = []
                    stations_grid[station_name].append(employee)

            # Add unassigned employees to a separate group
            if unassigned_employees:
                stations_grid['Unassigned'] = unassigned_employees

            # Prepare response
            result = {
                'year': grid_data.get('year'),
                'month': grid_data.get('month'),
                'month_name': grid_data.get('month_name'),
                'days_in_month': grid_data.get('days_in_month'),
                'total_employees': grid_data.get('total_employees'),
                'total_working_days': grid_data.get('total_working_days'),
                'date_range': grid_data.get('date_range'),
                'stations_grid': stations_grid,
                'overtime_summary': grid_data.get('overtime_summary', []),
                'total_stations': len(stations_grid)
            }

            return result

        except Exception as e:
            logger.error(f"Error in get_monthly_attendance_grid_by_station: {str(e)}")
            import traceback
            traceback.print_exc()
            raise