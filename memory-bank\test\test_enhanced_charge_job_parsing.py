#!/usr/bin/env python3
"""
Enhanced test script to verify the improved charge job parsing logic.
Tests edge cases including task codes with embedded slashes and single separators.
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
FLASK_APP_URL = "http://localhost:5173"
CHARGE_JOB_API_URL = "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"

def test_enhanced_parsing_logic():
    """Test the enhanced charge job parsing logic with various edge cases."""
    print("=" * 80)
    print("TESTING ENHANCED CHARGE JOB PARSING LOGIC")
    print("=" * 80)
    
    # Comprehensive test cases including edge cases
    test_cases = [
        {
            'name': '4-component format (standard)',
            'input': '(OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)',
            'expected_format': '4_component',
            'expected_components': 4
        },
        {
            'name': '3-component format (no machine code)',
            'input': 'TSK001 / STN-001 / EXP001',
            'expected_format': '3_component',
            'expected_components': 3
        },
        {
            'name': '2-component format (task + expense only)',
            'input': 'C/Roll Wages / L (LABOUR)',
            'expected_format': '2_component',
            'expected_components': 2
        },
        {
            'name': '2-component with task code containing slash',
            'input': 'C/Roll Wages / LABOUR COST',
            'expected_format': '2_component', 
            'expected_components': 2
        },
        {
            'name': 'Task code with embedded slash (A/B pattern)',
            'input': 'A/B Maintenance / Station Equipment / Machine Cost / Labour',
            'expected_format': '4_component',
            'expected_components': 4
        },
        {
            'name': 'Task code with multiple embedded slashes',
            'input': 'C/D/E Complex Task / STN-001 / MCH-001 / EXP-001',
            'expected_format': '4_component',
            'expected_components': 4
        },
        {
            'name': 'Single component (task code only)',
            'input': 'STANDALONE_TASK_CODE',
            'expected_format': '1_component',
            'expected_components': 1
        },
        {
            'name': 'Empty string',
            'input': '',
            'expected_format': 'invalid',
            'expected_components': 0
        },
        {
            'name': 'Mixed separators (spaces and no spaces)',
            'input': 'TaskCode/NoSpace / With Space / Another / Final',
            'expected_format': '4_component',
            'expected_components': 4
        },
        {
            'name': 'Fallback parsing test - no space separators',
            'input': 'TaskCode/Station/Machine/Expense',
            'expected_format': '3_component',
            'expected_components': 3
        },
        {
            'name': 'Complex task code with slash in parentheses',
            'input': '(C/Roll Operations) Complex Task / STN-FRC / Machine Cost / Labour',
            'expected_format': '4_component',
            'expected_components': 4
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"Input: '{test_case['input']}'")
        
        if test_case['input']:
            # Simulate the parsing logic
            result = simulate_enhanced_parsing(test_case['input'])
            
            print(f"Result Format: {result['format_type']}")
            print(f"Components Found: {result['components_count']}")
            print(f"Task Code: '{result['task_code']}'")
            print(f"Station Code: '{result['station_code']}'")
            print(f"Machine Code: '{result['machine_code']}'")
            print(f"Expense Code: '{result['expense_code']}'")
            
            if result.get('parse_error'):
                print(f"Parse Error: {result['parse_error']}")
            
            # Check if result matches expectations
            format_match = result['format_type'] == test_case['expected_format']
            component_match = result['components_count'] == test_case['expected_components']
            
            if format_match and component_match:
                print("✅ PASS")
                passed += 1
            else:
                print("❌ FAIL")
                if not format_match:
                    print(f"   Expected format: {test_case['expected_format']}, got: {result['format_type']}")
                if not component_match:
                    print(f"   Expected components: {test_case['expected_components']}, got: {result['components_count']}")
                failed += 1
        else:
            print("Empty input - testing invalid case")
            print("✅ PASS")
            passed += 1
        
        print("-" * 40)
    
    print(f"\nTest Summary: {passed} passed, {failed} failed")
    return failed == 0

def simulate_enhanced_parsing(charge_job_string):
    """
    Simulate the enhanced parsing logic locally for testing.
    This mirrors the logic in the web_app.py file.
    """
    if not charge_job_string or not isinstance(charge_job_string, str):
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'invalid',
            'components_count': 0,
            'parse_error': 'Empty or invalid charge job string'
        }
    
    try:
        primary_separator = " / "
        fallback_separator = "/"
        
        cleaned_string = charge_job_string.strip()
        
        # First attempt: Use primary separator
        parts = [part.strip() for part in cleaned_string.split(primary_separator)]
        parts = [part for part in parts if part]
        
        # If primary separator doesn't work well, try fallback strategy
        if len(parts) <= 1 and fallback_separator in cleaned_string:
            parts = simulate_fallback_strategy(cleaned_string, primary_separator, fallback_separator)
        
        # Determine format and extract components
        components_count = len(parts)
        
        if len(parts) == 4:
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': parts[2],
                'expense_code': parts[3],
                'format_type': '4_component',
                'components_count': components_count,
                'parse_error': None
            }
        elif len(parts) == 3:
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': '',
                'expense_code': parts[2],
                'format_type': '3_component',
                'components_count': components_count,
                'parse_error': None
            }
        elif len(parts) == 2:
            return {
                'task_code': parts[0],
                'station_code': '',
                'machine_code': '',
                'expense_code': parts[1],
                'format_type': '2_component',
                'components_count': components_count,
                'parse_error': None
            }
        elif len(parts) == 1:
            return {
                'task_code': parts[0],
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'format_type': '1_component',
                'components_count': components_count,
                'parse_error': 'Only task code found, no separators'
            }
        else:
            return {
                'task_code': parts[0] if len(parts) > 0 else '',
                'station_code': parts[1] if len(parts) > 1 else '',
                'machine_code': parts[2] if len(parts) > 2 else '',
                'expense_code': parts[3] if len(parts) > 3 else '',
                'format_type': 'excessive_components',
                'components_count': components_count,
                'parse_error': f'Found {len(parts)} components, expected 2-4. Using first 4 components.'
            }
            
    except Exception as e:
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'error',
            'components_count': 0,
            'parse_error': str(e)
        }

def simulate_fallback_strategy(charge_job_string, primary_separator, fallback_separator):
    """Simulate the fallback parsing strategy."""
    try:
        if primary_separator in charge_job_string:
            parts = [part.strip() for part in charge_job_string.split(primary_separator)]
            return [part for part in parts if part]
        
        tentative_parts = charge_job_string.split(fallback_separator)
        
        if len(tentative_parts) > 2:
            first_part = tentative_parts[0].strip()
            second_part = tentative_parts[1].strip()
            
            if (len(first_part) <= 10 and len(second_part) <= 20 and 
                '(' not in first_part and ')' not in first_part and
                '(' not in second_part and ')' not in second_part):
                
                combined_task_code = f"{first_part}/{second_part}"
                remaining_parts = [part.strip() for part in tentative_parts[2:] if part.strip()]
                
                return [combined_task_code] + remaining_parts
        
        return [part.strip() for part in tentative_parts if part.strip()]
        
    except Exception:
        return [charge_job_string.strip()]

def test_flask_endpoint_with_enhanced_parsing():
    """Test the Flask endpoint with the enhanced parsing."""
    print("\n" + "=" * 80)
    print("TESTING FLASK ENDPOINT WITH ENHANCED PARSING")
    print("=" * 80)
    
    try:
        print(f"Testing Flask endpoint: {FLASK_APP_URL}/api/employee-charge-jobs")
        response = requests.get(f"{FLASK_APP_URL}/api/employee-charge-jobs", timeout=30)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Flask endpoint successful!")
            
            print(f"Success: {result.get('success')}")
            print(f"Total Records: {result.get('total_records')}")
            
            if 'format_statistics' in result:
                print("\nEnhanced Format Statistics:")
                for format_type, count in result['format_statistics'].items():
                    print(f"  {format_type}: {count} records")
            
            print(f"Parse Errors: {result.get('parse_errors', 0)}")
            
            # Check for new format types
            format_stats = result.get('format_statistics', {})
            total_new_formats = format_stats.get('2_component', 0) + format_stats.get('1_component', 0)
            
            if total_new_formats > 0:
                print(f"\n✅ Enhanced parsing detected {total_new_formats} records with new formats!")
            
            return True
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Starting enhanced charge job parsing tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test 1: Enhanced parsing logic
    parsing_success = test_enhanced_parsing_logic()
    
    # Test 2: Flask endpoint with enhanced parsing
    flask_success = test_flask_endpoint_with_enhanced_parsing()
    
    print("\n" + "=" * 80)
    print("ENHANCED TEST SUMMARY")
    print("=" * 80)
    print(f"Enhanced parsing logic: {'✅ PASS' if parsing_success else '❌ FAIL'}")
    print(f"Flask endpoint test: {'✅ PASS' if flask_success else '❌ FAIL'}")
    
    if parsing_success and flask_success:
        print("\n🎉 ALL ENHANCED TESTS PASSED!")
        print("The enhanced charge job parsing logic is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME ENHANCED TESTS FAILED")
        print("Please check the error messages above for troubleshooting.")
        sys.exit(1) 