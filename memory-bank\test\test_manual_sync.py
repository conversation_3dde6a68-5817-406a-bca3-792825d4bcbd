#!/usr/bin/env python3
"""
Test script to verify new Google Apps Script deployment
Replace YOUR_NEW_URL with your new deployment URL
"""

import requests
import json

# Replace this with your new Google Apps Script URL after redeployment
NEW_GOOGLE_APPS_SCRIPT_URL = "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec"

def test_new_deployment():
    print("=" * 60)
    print("TESTING NEW GOOGLE APPS SCRIPT DEPLOYMENT")
    print("=" * 60)
    
    if "YOUR_NEW_URL_HERE" in NEW_GOOGLE_APPS_SCRIPT_URL:
        print("❌ Please update the NEW_GOOGLE_APPS_SCRIPT_URL variable with your new deployment URL!")
        return
    
    # Test data
    test_data = [
        {
            "employeeId": "TEST001",
            "employeeName": "Test Employee",
            "date": "2025-01-15",
            "dayOfWeek": "Wednesday",
            "shift": "Morning",
            "checkIn": "08:00:00",
            "checkOut": "17:00:00",
            "regularHours": 8,
            "overtimeHours": 1,
            "totalHours": 9
        }
    ]
    
    print(f"Testing URL: {NEW_GOOGLE_APPS_SCRIPT_URL}")
    
    # Test 1: Direct call to Google Apps Script
    print("\n1. Testing direct sync_attendance call...")
    try:
        params = {
            'action': 'sync_attendance',
            'data': json.dumps(test_data)
        }
        
        response = requests.get(NEW_GOOGLE_APPS_SCRIPT_URL, params=params, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('status') == 'success':
                    print("✅ Direct Google Apps Script call successful!")
                    print(f"   Records synced: {result.get('data', {}).get('records_synced', 'N/A')}")
                else:
                    print(f"❌ Google Apps Script returned error: {result.get('message', 'Unknown error')}")
            except:
                print("❌ Response is not valid JSON")
        else:
            print("❌ Direct Google Apps Script call failed")
            
    except Exception as e:
        print(f"❌ Direct call error: {e}")
    
    # Test 2: Get data test
    print("\n2. Testing get_data call...")
    try:
        params = {'action': 'get_data'}
        
        response = requests.get(NEW_GOOGLE_APPS_SCRIPT_URL, params=params, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('status') == 'success':
                    print(f"✅ Get data successful! Found {result.get('total_records', 0)} records")
                else:
                    print(f"❌ Get data returned error: {result.get('message', 'Unknown error')}")
            except:
                print("❌ Response is not valid JSON")
        else:
            print("❌ Get data call failed")
            
    except Exception as e:
        print(f"❌ Get data error: {e}")
    
    print("\n" + "=" * 60)
    print("If both tests passed, update the frontend URLs and try the web app!")
    print("=" * 60)

if __name__ == "__main__":
    test_new_deployment() 