"""
Database Controller for Attendance Report System.
Handles all database management API endpoints.
Implements Interface Segregation Principle.
"""

from flask import Blueprint, request
from typing import Dict, Any

from api.api_utils import (
    create_success_response, create_error_response,
    validate_json_request, log_api_request, get_user_context,
    handle_database_error
)


class DatabaseController:
    """
    Controller for database management endpoints.
    Implements Interface Segregation - only handles database management.
    """
    
    def __init__(self, database_manager, logging_manager):
        """
        Initialize database controller.
        
        Args:
            database_manager: Database manager instance
            logging_manager: Logging manager instance
        """
        self.database_manager = database_manager
        self.logger = logging_manager.get_logger(__name__)
        
        # Create Flask blueprint
        self.blueprint = Blueprint('database', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """Register all database management routes."""
        
        @self.blueprint.route('/connection-status', methods=['GET'])
        @log_api_request(self.logger)
        def get_connection_status():
            """Get current database connection status."""
            try:
                status = self.database_manager.get_connection_status()
                
                return create_success_response(
                    data=status,
                    message="Database connection status retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting connection status: {str(e)}")
                return create_error_response(f"Failed to get connection status: {str(e)}", 500)
        
        @self.blueprint.route('/test-connection', methods=['POST'])
        @log_api_request(self.logger)
        @validate_json_request(['mode'])
        def test_database_connection():
            """Test database connection for specified mode."""
            try:
                data = request.get_json()
                mode = data.get('mode', 'local')
                
                if mode not in ['local', 'remote']:
                    return create_error_response("Mode must be 'local' or 'remote'", 400)
                
                # Test connection
                success, message = self.database_manager.connection_manager.test_connection(mode)
                
                return create_success_response(
                    data={
                        'mode': mode,
                        'success': success,
                        'message': message
                    },
                    message=f"Connection test for {mode} database completed"
                )
                
            except Exception as e:
                self.logger.error(f"Error testing database connection: {str(e)}")
                return handle_database_error(e, "testing database connection")
        
        @self.blueprint.route('/switch-mode', methods=['POST'])
        @log_api_request(self.logger)
        @validate_json_request(['new_mode'])
        def switch_connection_mode():
            """Switch database connection mode."""
            try:
                data = request.get_json()
                new_mode = data.get('new_mode')
                
                if new_mode not in ['local', 'remote']:
                    return create_error_response("new_mode must be 'local' or 'remote'", 400)
                
                # Log the switch attempt
                user_context = get_user_context()
                self.logger.info(f"Database mode switch requested: {new_mode} by {user_context['ip_address']}")
                
                # Switch connection mode
                success, message = self.database_manager.switch_connection_mode(new_mode)
                
                if success:
                    return create_success_response(
                        data={
                            'new_mode': new_mode,
                            'message': message
                        },
                        message=f"Successfully switched to {new_mode} database mode"
                    )
                else:
                    return create_error_response(message, 400)
                
            except Exception as e:
                self.logger.error(f"Error switching connection mode: {str(e)}")
                return handle_database_error(e, "switching connection mode")
        
        @self.blueprint.route('/config', methods=['GET'])
        @log_api_request(self.logger)
        def get_database_config():
            """Get current database configuration (safe version)."""
            try:
                # Get safe configuration (without sensitive data)
                safe_config = self.database_manager.config_manager.get_safe_config()
                db_config = safe_config.get('database_config', {})
                
                return create_success_response(
                    data=db_config,
                    message="Database configuration retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting database config: {str(e)}")
                return create_error_response(f"Failed to get database config: {str(e)}", 500)
        
        @self.blueprint.route('/config', methods=['PUT'])
        @log_api_request(self.logger)
        @validate_json_request()
        def update_database_config():
            """Update database configuration."""
            try:
                data = request.get_json()
                
                # Log the configuration update attempt
                user_context = get_user_context()
                self.logger.warning(f"Database config update requested by {user_context['ip_address']}")
                
                # Update configuration
                success, message = self.database_manager.update_database_config(data)
                
                if success:
                    return create_success_response(
                        message=message
                    )
                else:
                    return create_error_response(message, 400)
                
            except Exception as e:
                self.logger.error(f"Error updating database config: {str(e)}")
                return create_error_response(f"Failed to update database config: {str(e)}", 500)
        
        @self.blueprint.route('/validate-config', methods=['POST'])
        @log_api_request(self.logger)
        def validate_database_config():
            """Validate database configuration."""
            try:
                # Validate current configuration
                validation_result = self.database_manager.config_manager.validate_config()
                
                return create_success_response(
                    data=validation_result,
                    message="Database configuration validation completed"
                )
                
            except Exception as e:
                self.logger.error(f"Error validating database config: {str(e)}")
                return create_error_response(f"Failed to validate database config: {str(e)}", 500)
        
        @self.blueprint.route('/statistics', methods=['GET'])
        @log_api_request(self.logger)
        def get_database_statistics():
            """Get database statistics and performance metrics."""
            try:
                # Get connection status
                connection_status = self.database_manager.get_connection_status()
                
                # Get staging statistics
                staging_stats = self.staging_service.get_staging_statistics()
                
                # Combine statistics
                statistics = {
                    'connection_info': connection_status,
                    'staging_info': staging_stats,
                    'performance_metrics': {
                        'connection_pool_active': 'not_implemented',  # TODO: Implement
                        'query_cache_hits': 'not_implemented',  # TODO: Implement
                        'average_response_time': 'not_implemented'  # TODO: Implement
                    }
                }
                
                return create_success_response(
                    data=statistics,
                    message="Database statistics retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting database statistics: {str(e)}")
                return create_error_response(f"Failed to get database statistics: {str(e)}", 500)
        
        @self.blueprint.route('/test-query', methods=['POST'])
        @log_api_request(self.logger)
        @validate_json_request(['query'])
        def test_database_query():
            """Test a database query (for debugging purposes)."""
            try:
                data = request.get_json()
                query = data.get('query', '').strip()
                
                # Security check - only allow SELECT queries
                if not query.upper().startswith('SELECT'):
                    return create_error_response("Only SELECT queries are allowed", 400)
                
                # Log the query test attempt
                user_context = get_user_context()
                self.logger.warning(f"Database query test requested by {user_context['ip_address']}: {query[:100]}...")
                
                # Execute query with timeout
                result = self.database_manager.execute_query(query)
                
                return create_success_response(
                    data={
                        'query': query,
                        'result_count': len(result),
                        'results': result[:10] if len(result) > 10 else result,  # Limit results for safety
                        'truncated': len(result) > 10
                    },
                    message=f"Query executed successfully, returned {len(result)} rows"
                )
                
            except Exception as e:
                self.logger.error(f"Error executing test query: {str(e)}")
                return handle_database_error(e, "executing test query")
        
        @self.blueprint.route('/connection-history', methods=['GET'])
        @log_api_request(self.logger)
        def get_connection_history():
            """Get database connection history and attempts."""
            try:
                connection_status = self.database_manager.get_connection_status()
                
                # Extract connection history
                history = {
                    'current_mode': connection_status['current_mode'],
                    'fallback_enabled': connection_status['fallback_enabled'],
                    'local_connection': {
                        'status': connection_status['local_status']['connected'],
                        'last_attempt': connection_status['local_status']['last_attempt'],
                        'error': connection_status['local_status']['error']
                    },
                    'remote_connection': {
                        'status': connection_status['remote_status']['connected'],
                        'last_attempt': connection_status['remote_status']['last_attempt'],
                        'error': connection_status['remote_status']['error']
                    }
                }
                
                return create_success_response(
                    data=history,
                    message="Connection history retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting connection history: {str(e)}")
                return create_error_response(f"Failed to get connection history: {str(e)}", 500)
