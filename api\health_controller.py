"""
Health Controller for Attendance Report System.
Handles all health check and monitoring API endpoints.
Implements Interface Segregation Principle.
"""

from flask import Blueprint
from typing import Dict, Any

from api.api_utils import (
    create_success_response, create_error_response,
    log_api_request, get_user_context
)


class HealthController:
    """
    Controller for health check and monitoring endpoints.
    Implements Interface Segregation - only handles health monitoring.
    """
    
    def __init__(self, database_manager, staging_service, logging_manager):
        """
        Initialize health controller.
        
        Args:
            database_manager: Database manager instance
            staging_service: Staging service instance
            logging_manager: Logging manager instance
        """
        self.database_manager = database_manager
        self.staging_service = staging_service
        self.logger = logging_manager.get_logger(__name__)
        
        # Create Flask blueprint
        self.blueprint = Blueprint('health', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """Register all health-related routes."""
        
        @self.blueprint.route('/health', methods=['GET'])
        @log_api_request(self.logger)
        def health_check():
            """Comprehensive system health check."""
            try:
                # Perform comprehensive health check
                health_status = self.database_manager.perform_health_check()
                
                # Add additional system information
                health_status.update({
                    'system_info': {
                        'version': '1.0.0-refactored',
                        'architecture': 'modular',
                        'principles': 'SOLID'
                    }
                })
                
                # Determine overall status code
                status_code = 200 if health_status.get('overall_health', False) else 503
                
                return create_success_response(
                    data=health_status,
                    message="Health check completed",
                    status_code=status_code
                )
                
            except Exception as e:
                self.logger.error(f"Health check failed: {str(e)}")
                return create_error_response(f"Health check failed: {str(e)}", 500)
        
        @self.blueprint.route('/health/database', methods=['GET'])
        @log_api_request(self.logger)
        def database_health():
            """Database-specific health check."""
            try:
                connection_status = self.database_manager.get_connection_status()
                
                # Test current connection
                test_result = self.database_manager.connection_manager.test_connection(
                    connection_status['current_mode']
                )
                
                health_data = {
                    'connection_status': connection_status,
                    'current_test': {
                        'success': test_result[0],
                        'message': test_result[1]
                    }
                }
                
                status_code = 200 if test_result[0] else 503
                
                return create_success_response(
                    data=health_data,
                    message="Database health check completed",
                    status_code=status_code
                )
                
            except Exception as e:
                self.logger.error(f"Database health check failed: {str(e)}")
                return create_error_response(f"Database health check failed: {str(e)}", 500)
        
        @self.blueprint.route('/health/staging', methods=['GET'])
        @log_api_request(self.logger)
        def staging_health():
            """Staging database health check."""
            try:
                health_status = self.staging_service.get_staging_health_status()
                
                status_code = 200 if health_status.get('healthy', False) else 503
                
                return create_success_response(
                    data=health_status,
                    message="Staging health check completed",
                    status_code=status_code
                )
                
            except Exception as e:
                self.logger.error(f"Staging health check failed: {str(e)}")
                return create_error_response(f"Staging health check failed: {str(e)}", 500)
        
        @self.blueprint.route('/status', methods=['GET'])
        @log_api_request(self.logger)
        def system_status():
            """Get comprehensive system status."""
            try:
                # Get all status information
                db_status = self.database_manager.get_connection_status()
                staging_stats = self.staging_service.get_staging_statistics()
                health_check = self.database_manager.perform_health_check()
                
                system_status = {
                    'overall_status': 'healthy' if health_check.get('overall_health', False) else 'degraded',
                    'database_status': db_status,
                    'staging_statistics': staging_stats,
                    'health_check': health_check,
                    'uptime_info': {
                        'startup_time': 'unknown',  # TODO: Track actual startup time
                        'current_time': health_check.get('last_check')
                    }
                }
                
                return create_success_response(
                    data=system_status,
                    message="System status retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting system status: {str(e)}")
                return create_error_response(f"Failed to get system status: {str(e)}", 500)
        
        @self.blueprint.route('/ping', methods=['GET'])
        @log_api_request(self.logger)
        def ping():
            """Simple ping endpoint for basic connectivity testing."""
            try:
                return create_success_response(
                    data={'pong': True, 'server_time': health_check.get('last_check', 'unknown')},
                    message="Pong! Server is responding"
                )
                
            except Exception as e:
                self.logger.error(f"Ping failed: {str(e)}")
                return create_error_response(f"Ping failed: {str(e)}", 500)
        
        @self.blueprint.route('/version', methods=['GET'])
        @log_api_request(self.logger)
        def get_version():
            """Get application version and build information."""
            try:
                version_info = {
                    'version': '1.0.0-refactored',
                    'build_type': 'modular',
                    'architecture': 'SOLID principles',
                    'features': [
                        'Dependency Inversion',
                        'Single Responsibility',
                        'Interface Segregation',
                        'Open/Closed Principle',
                        'Performance Optimized',
                        'Modular Design'
                    ],
                    'performance_improvements': [
                        'Lazy loading',
                        'Connection pooling',
                        'Caching support',
                        'Optimized queries',
                        'Fast startup mode'
                    ]
                }
                
                return create_success_response(
                    data=version_info,
                    message="Version information retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting version info: {str(e)}")
                return create_error_response(f"Failed to get version info: {str(e)}", 500)
