"""
Application Factory for Attendance Report System.
Creates and configures the Flask application with dependency injection.
"""

import os
import sys
import logging
from flask import Flask
from flask_cors import CORS

# Import core components
from core.config_manager import ConfigurationManager
from core.database_manager import DatabaseManager
from core.logging_manager import LoggingManager

# Import API controllers
from api.attendance_controller import AttendanceController
from api.staging_controller import StagingController
from api.database_controller import DatabaseController
from api.health_controller import HealthController
from api.employee_controller import EmployeeController

# Import services
from services.attendance_service import AttendanceService
from services.staging_service import StagingService
from services.employee_service import EmployeeService

def create_app(config_name='default'):
    """
    Application factory function that creates and configures the Flask app.
    
    Args:
        config_name (str): Configuration environment name
        
    Returns:
        Flask: Configured Flask application instance
    """
    
    # Create Flask application
    app = Flask(__name__)
    CORS(app)
    
    # Configure Flask app
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['JSON_SORT_KEYS'] = False
    
    try:
        # Initialize core components (Dependency Inversion Principle)
        config_manager = ConfigurationManager()
        logging_manager = LoggingManager(config_manager)
        database_manager = DatabaseManager(config_manager, logging_manager)
        
        # Initialize services (Single Responsibility Principle)
        attendance_service = AttendanceService(database_manager, config_manager, logging_manager)
        staging_service = StagingService(database_manager, config_manager, logging_manager)
        employee_service = EmployeeService(database_manager, config_manager, logging_manager)
        
        # Initialize controllers (Interface Segregation Principle)
        attendance_controller = AttendanceController(attendance_service, logging_manager)
        staging_controller = StagingController(staging_service, logging_manager)
        database_controller = DatabaseController(database_manager, logging_manager)
        health_controller = HealthController(database_manager, staging_service, logging_manager)
        employee_controller = EmployeeController(employee_service, logging_manager)
        
        # Register blueprints (Open/Closed Principle)
        app.register_blueprint(attendance_controller.blueprint, url_prefix='/api')
        app.register_blueprint(staging_controller.blueprint, url_prefix='/api/staging')
        app.register_blueprint(database_controller.blueprint, url_prefix='/api/database')
        app.register_blueprint(health_controller.blueprint, url_prefix='/api')
        app.register_blueprint(employee_controller.blueprint, url_prefix='/api')
        
        # Store components in app context for access in routes
        app.config['COMPONENTS'] = {
            'config_manager': config_manager,
            'database_manager': database_manager,
            'logging_manager': logging_manager,
            'attendance_service': attendance_service,
            'staging_service': staging_service,
            'employee_service': employee_service
        }
        
        # Register main routes
        register_main_routes(app)
        
        # Initialize database connections and perform startup tasks
        initialize_application(app)

        logging_manager.get_logger(__name__).info("✅ Application factory completed successfully")

        return app

    except Exception as e:
        # Fallback logging if logging_manager failed to initialize
        logging.basicConfig(level=logging.ERROR)
        logger = logging.getLogger(__name__)
        logger.error(f"❌ Application factory failed: {str(e)}")
        raise


def register_main_routes(app):
    """Register main application routes."""
    from flask import render_template, send_file
    
    @app.route('/')
    def index():
        """Main dashboard page."""
        return render_template('index.html')
    
    @app.route('/test')
    def test_page():
        """Simple test page without DataTables."""
        return render_template('simple_test.html')
    
    @app.route('/test-grid')
    def test_grid_page():
        """Test page for grid functionality."""
        return render_template('test_grid.html')
    
    @app.route('/test-charge-jobs')
    def test_charge_jobs_page():
        """Test page for employee charge jobs integration."""
        return send_file('test_frontend_integration.html')
    
    @app.route('/test-staging')
    def test_staging_page():
        """Test page for staging transfer debugging."""
        return send_file('test_staging_transfer.html')
    
    @app.route('/fallback')
    def database_fallback_page():
        """Fallback page when both databases are unavailable."""
        return render_template('database_fallback.html')


def initialize_application(app):
    """
    Initialize application components and perform startup tasks.
    
    Args:
        app (Flask): Flask application instance
    """
    components = app.config['COMPONENTS']
    logger = components['logging_manager'].get_logger(__name__)
    
    try:
        logger.info("=" * 60)
        logger.info("INITIALIZING ATTENDANCE REPORT APPLICATION")
        logger.info("=" * 60)
        
        # Initialize database connections
        database_manager = components['database_manager']
        database_manager.initialize_connections()
        
        # Initialize staging database
        staging_service = components['staging_service']
        staging_service.initialize_staging_database()
        
        # Perform initial health checks
        health_status = database_manager.perform_health_check()
        if health_status['overall_health']:
            logger.info("✅ Application initialization completed successfully")
        else:
            logger.warning("⚠️ Application initialized with some components unavailable")
            
        logger.info("🌐 Web interface ready")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ Application initialization failed: {str(e)}")
        # Don't raise - allow app to start in degraded mode
        logger.info("📝 Application starting in degraded mode - some features may be unavailable")


def parse_command_line_args():
    """Parse command-line arguments for database mode selection."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='VenusHR14 Attendance Report Web Application',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python web_app.py --local        # Use local database directly (fast boot)
  python web_app.py --remote       # Use remote database directly (fast boot)
  python web_app.py -db local      # Use local database directly (fast boot)
  python web_app.py -db remote     # Use remote database directly (fast boot)
  python web_app.py                # Default: scan and auto-select database
        """
    )
    
    # Database mode selection group
    db_group = parser.add_mutually_exclusive_group()
    db_group.add_argument(
        '--local', 
        action='store_true',
        help='Use local database directly (skip scanning)'
    )
    db_group.add_argument(
        '--remote', 
        action='store_true',
        help='Use remote database directly (skip scanning)'
    )
    
    # Alternative syntax with -db flag that accepts a value
    db_group.add_argument(
        '-db', '--database',
        choices=['local', 'remote'],
        help='Database mode: local or remote (skip scanning)'
    )
    
    args = parser.parse_args()
    
    # Determine the selected mode
    if args.local:
        return 'local'
    elif args.remote:
        return 'remote'
    elif args.database:
        return args.database
    else:
        return None
