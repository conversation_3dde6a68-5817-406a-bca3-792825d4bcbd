#!/usr/bin/env python3
"""
Test script to verify the attendance selection functionality works correctly.
This script tests the new staging selection mode and transfer functionality.
"""

import requests
import json
import sys
from datetime import datetime, timedelta

def test_attendance_selection():
    """Test the attendance selection and transfer functionality."""
    base_url = "http://localhost:5000"
    
    print("Testing Attendance Selection and Transfer Functionality")
    print("=" * 60)
    
    # Test 1: Check if the main page loads
    print("1. Testing main page load...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("   ✅ Main page loads successfully")
            
            # Check if new elements are present
            content = response.text
            if 'toggleStagingSelectionMode' in content:
                print("   ✅ Staging selection toggle button found")
            else:
                print("   ❌ Staging selection toggle button NOT found")
                
            if 'stagingTransferControls' in content:
                print("   ✅ Transfer controls section found")
            else:
                print("   ❌ Transfer controls section NOT found")
                
            if 'selectAllAttendance' in content:
                print("   ✅ Select all attendance checkbox found")
            else:
                print("   ❌ Select all attendance checkbox NOT found")
        else:
            print(f"   ❌ Main page failed to load: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error loading main page: {e}")
    
    # Test 2: Check if attendance API works
    print("\n2. Testing attendance API...")
    try:
        # Get current date range (last 7 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        params = {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'bus_code': 'PTRJ'
        }
        
        response = requests.get(f"{base_url}/api/attendance", params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Attendance API works: {data.get('total_records', 0)} records found")
            else:
                print(f"   ❌ Attendance API error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ Attendance API failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing attendance API: {e}")
    
    # Test 3: Check if selective copy API works
    print("\n3. Testing selective copy API...")
    try:
        test_data = {
            'employee_ids': ['TEST001'],
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'bus_code': 'PTRJ'
        }
        
        response = requests.post(
            f"{base_url}/api/staging/selective-copy",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Selective copy API works")
                print(f"      - New records: {data.get('new_records', 0)}")
                print(f"      - Updated records: {data.get('updated_records', 0)}")
                print(f"      - Duplicates prevented: {data.get('skipped_duplicates', 0)}")
            else:
                print(f"   ⚠️ Selective copy API responded but no data: {data.get('error', 'No matching records')}")
        else:
            print(f"   ❌ Selective copy API failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing selective copy API: {e}")
    
    # Test 4: Check duplicate prevention APIs
    print("\n4. Testing duplicate prevention APIs...")
    try:
        # Check duplicates
        response = requests.get(f"{base_url}/api/staging/check-duplicates")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Check duplicates API works")
                print(f"      - Total records: {data.get('total_records', 0)}")
                print(f"      - Duplicate sets: {data.get('duplicate_sets', 0)}")
                print(f"      - Has duplicates: {data.get('has_duplicates', False)}")
            else:
                print(f"   ❌ Check duplicates API error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ Check duplicates API failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing check duplicates API: {e}")
    
    # Test 5: Check staging data API
    print("\n5. Testing staging data API...")
    try:
        response = requests.get(f"{base_url}/api/staging/data")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Staging data API works")
                print(f"      - Total records: {data.get('total_records', 0)}")
                print(f"      - Returned records: {data.get('returned_records', 0)}")
                print(f"      - Total employees: {data.get('total_employees', 0)}")
            else:
                print(f"   ❌ Staging data API error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ Staging data API failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing staging data API: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nTo test the frontend functionality:")
    print("1. Open http://localhost:5000 in your browser")
    print("2. Generate an attendance report")
    print("3. Click 'Selection Mode' button")
    print("4. Select some records using checkboxes")
    print("5. Configure date range options")
    print("6. Click 'Transfer to Staging'")
    print("7. Check the Staging tab to verify records were transferred")

if __name__ == "__main__":
    test_attendance_selection()
