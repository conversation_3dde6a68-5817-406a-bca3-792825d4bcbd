/*
Monthly Attendance Reports Query
Purpose: Generate monthly attendance summaries and statistics
Database: VenusHR14
Tables: HR_T_TAMachine_Summary, HR_M_EmployeePI, HR_T_Overtime
Parameters: @year, @month, @bus_code (optional)
*/

-- Get available months with data
WITH AvailableMonths AS (
    SELECT DISTINCT
        YEAR(ta.TADate) as Year,
        MONTH(ta.TADate) as Month,
        DATENAME(MONTH, ta.TADate) as MonthName,
        COUNT(*) as RecordCount,
        COUNT(DISTINCT ta.EmployeeID) as EmployeeCount,
        MIN(ta.TADate) as FirstDate,
        MAX(ta.TADate) as LastDate
    FROM [VenusHR14].[dbo].[HR_T_TAMachine_Summary] ta
    WHERE (@bus_code IS NULL OR ta.BusCode = @bus_code)
    GROUP BY YEAR(ta.TADate), MONTH(ta.TADate), DATENAME(MONTH, ta.TADate)
),

-- Monthly summary data
MonthlySummary AS (
    SELECT 
        YEAR(ta.TADate) as Year,
        MONTH(ta.TADate) as Month,
        COUNT(DISTINCT ta.EmployeeID) as TotalEmployees,
        COUNT(*) as TotalRecords,
        COUNT(DISTINCT ta.TADate) as WorkingDays,
        
        -- Calculate total hours
        SUM(CASE 
            WHEN ta.TACheckIn IS NOT NULL AND ta.TACheckOut IS NOT NULL
            THEN CASE 
                WHEN DATEPART(WEEKDAY, ta.TADate) = 7 -- Saturday
                THEN CASE 
                    WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 5
                    THEN 5.0
                    ELSE DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0
                END
                ELSE CASE 
                    WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 8
                    THEN 8.0
                    ELSE DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0
                END
            END
            ELSE 0
        END) as TotalRegularHours,
        
        -- Calculate overtime hours from attendance
        SUM(CASE 
            WHEN ta.TACheckIn IS NOT NULL AND ta.TACheckOut IS NOT NULL
            THEN CASE 
                WHEN DATEPART(WEEKDAY, ta.TADate) = 7 -- Saturday
                THEN CASE 
                    WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 5
                    THEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 - 5.0
                    ELSE 0
                END
                ELSE CASE 
                    WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 8
                    THEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 - 8.0
                    ELSE 0
                END
            END
            ELSE 0
        END) as TotalOvertimeHours
        
    FROM [VenusHR14].[dbo].[HR_T_TAMachine_Summary] ta
    WHERE (@year IS NULL OR YEAR(ta.TADate) = @year)
        AND (@month IS NULL OR MONTH(ta.TADate) = @month)
        AND (@bus_code IS NULL OR ta.BusCode = @bus_code)
    GROUP BY YEAR(ta.TADate), MONTH(ta.TADate)
)

-- Return available months if no specific month requested
SELECT 
    am.Year,
    am.Month,
    am.MonthName,
    am.RecordCount,
    am.EmployeeCount,
    am.FirstDate,
    am.LastDate,
    COALESCE(ms.TotalEmployees, 0) as TotalEmployees,
    COALESCE(ms.TotalRecords, 0) as TotalRecords,
    COALESCE(ms.WorkingDays, 0) as WorkingDays,
    COALESCE(ms.TotalRegularHours, 0) as TotalRegularHours,
    COALESCE(ms.TotalOvertimeHours, 0) as TotalOvertimeHours
FROM AvailableMonths am
LEFT JOIN MonthlySummary ms ON am.Year = ms.Year AND am.Month = ms.Month
WHERE (@year IS NULL OR am.Year = @year)
    AND (@month IS NULL OR am.Month = @month)
ORDER BY am.Year DESC, am.Month DESC 