"""
Health Monitor for Attendance Report System.
Provides system health monitoring and diagnostics.
Implements Single Responsibility Principle.
"""

import os
import time
import psutil
from typing import Dict, Any, Optional
from datetime import datetime


class HealthMonitor:
    """
    System health monitoring utility.
    Provides comprehensive health checks and system diagnostics.
    """
    
    def __init__(self, logging_manager):
        """
        Initialize health monitor.
        
        Args:
            logging_manager: Logging manager instance
        """
        self.logger = logging_manager.get_logger(__name__)
        self.startup_time = datetime.now()
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health information.
        
        Returns:
            Dictionary with system health data
        """
        try:
            return {
                'system_info': self._get_system_info(),
                'performance_metrics': self._get_performance_metrics(),
                'disk_usage': self._get_disk_usage(),
                'memory_usage': self._get_memory_usage(),
                'uptime': self._get_uptime(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get system health: {str(e)}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get basic system information."""
        try:
            return {
                'platform': os.name,
                'python_version': f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}",
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
        except Exception as e:
            self.logger.warning(f"Failed to get system info: {str(e)}")
            return {'error': str(e)}
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        try:
            return {
                'cpu_usage_percent': psutil.cpu_percent(interval=0.1),
                'memory_usage_percent': psutil.virtual_memory().percent,
                'disk_io': {
                    'read_bytes': psutil.disk_io_counters().read_bytes if psutil.disk_io_counters() else 0,
                    'write_bytes': psutil.disk_io_counters().write_bytes if psutil.disk_io_counters() else 0
                },
                'network_io': {
                    'bytes_sent': psutil.net_io_counters().bytes_sent if psutil.net_io_counters() else 0,
                    'bytes_recv': psutil.net_io_counters().bytes_recv if psutil.net_io_counters() else 0
                }
            }
        except Exception as e:
            self.logger.warning(f"Failed to get performance metrics: {str(e)}")
            return {'error': str(e)}
    
    def _get_disk_usage(self) -> Dict[str, Any]:
        """Get disk usage information."""
        try:
            disk_usage = psutil.disk_usage('.')
            return {
                'total_gb': round(disk_usage.total / (1024**3), 2),
                'used_gb': round(disk_usage.used / (1024**3), 2),
                'free_gb': round(disk_usage.free / (1024**3), 2),
                'usage_percent': round((disk_usage.used / disk_usage.total) * 100, 1)
            }
        except Exception as e:
            self.logger.warning(f"Failed to get disk usage: {str(e)}")
            return {'error': str(e)}
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage information."""
        try:
            memory = psutil.virtual_memory()
            return {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'usage_percent': memory.percent
            }
        except Exception as e:
            self.logger.warning(f"Failed to get memory usage: {str(e)}")
            return {'error': str(e)}
    
    def _get_uptime(self) -> Dict[str, Any]:
        """Get application uptime information."""
        try:
            uptime_seconds = (datetime.now() - self.startup_time).total_seconds()
            uptime_hours = uptime_seconds / 3600
            
            return {
                'startup_time': self.startup_time.isoformat(),
                'current_time': datetime.now().isoformat(),
                'uptime_seconds': round(uptime_seconds, 2),
                'uptime_hours': round(uptime_hours, 2),
                'uptime_formatted': self._format_uptime(uptime_seconds)
            }
        except Exception as e:
            self.logger.warning(f"Failed to get uptime: {str(e)}")
            return {'error': str(e)}
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """Format uptime in human-readable format."""
        try:
            days = int(uptime_seconds // 86400)
            hours = int((uptime_seconds % 86400) // 3600)
            minutes = int((uptime_seconds % 3600) // 60)
            seconds = int(uptime_seconds % 60)
            
            if days > 0:
                return f"{days}d {hours}h {minutes}m {seconds}s"
            elif hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
                
        except Exception:
            return f"{uptime_seconds:.1f}s"
    
    def check_file_permissions(self, file_path: str) -> Dict[str, Any]:
        """
        Check file permissions and accessibility.
        
        Args:
            file_path: Path to file to check
            
        Returns:
            Dictionary with permission information
        """
        try:
            if not os.path.exists(file_path):
                return {
                    'exists': False,
                    'readable': False,
                    'writable': False,
                    'error': 'File does not exist'
                }
            
            return {
                'exists': True,
                'readable': os.access(file_path, os.R_OK),
                'writable': os.access(file_path, os.W_OK),
                'size_bytes': os.path.getsize(file_path),
                'modified_time': datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
            }
            
        except Exception as e:
            return {
                'exists': False,
                'readable': False,
                'writable': False,
                'error': str(e)
            }
    
    def check_directory_health(self, directory_path: str) -> Dict[str, Any]:
        """
        Check directory health and contents.
        
        Args:
            directory_path: Path to directory to check
            
        Returns:
            Dictionary with directory health information
        """
        try:
            if not os.path.exists(directory_path):
                return {
                    'exists': False,
                    'accessible': False,
                    'error': 'Directory does not exist'
                }
            
            if not os.path.isdir(directory_path):
                return {
                    'exists': True,
                    'accessible': False,
                    'error': 'Path is not a directory'
                }
            
            # Count files and subdirectories
            file_count = 0
            dir_count = 0
            total_size = 0
            
            for item in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item)
                if os.path.isfile(item_path):
                    file_count += 1
                    total_size += os.path.getsize(item_path)
                elif os.path.isdir(item_path):
                    dir_count += 1
            
            return {
                'exists': True,
                'accessible': True,
                'file_count': file_count,
                'directory_count': dir_count,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024**2), 2),
                'readable': os.access(directory_path, os.R_OK),
                'writable': os.access(directory_path, os.W_OK)
            }
            
        except Exception as e:
            return {
                'exists': False,
                'accessible': False,
                'error': str(e)
            }
    
    def get_application_health(self) -> Dict[str, Any]:
        """
        Get application-specific health information.
        
        Returns:
            Dictionary with application health data
        """
        try:
            # Check critical directories
            critical_dirs = ['modules', 'templates', 'static', 'data', 'core', 'services', 'api']
            directory_health = {}
            
            for dir_name in critical_dirs:
                directory_health[dir_name] = self.check_directory_health(dir_name)
            
            # Check critical files
            critical_files = ['config.json', 'app_factory.py']
            file_health = {}
            
            for file_name in critical_files:
                file_health[file_name] = self.check_file_permissions(file_name)
            
            # Overall application health
            all_dirs_healthy = all(
                dir_info.get('accessible', False) 
                for dir_info in directory_health.values()
            )
            
            all_files_healthy = all(
                file_info.get('readable', False) 
                for file_info in file_health.values()
            )
            
            return {
                'overall_healthy': all_dirs_healthy and all_files_healthy,
                'directory_health': directory_health,
                'file_health': file_health,
                'uptime': self._get_uptime(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get application health: {str(e)}")
            return {
                'overall_healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
