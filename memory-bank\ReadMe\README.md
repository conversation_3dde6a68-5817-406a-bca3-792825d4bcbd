# Enhanced Attendance Report System

A comprehensive tool for generating attendance reports from the VenusHR14 database with advanced business logic, overtime tracking, and modern web interface.

## Features

### Core Functionality
- **Enhanced Work Hours Calculation**: Implements business rules for regular work hours (7 hours weekdays, 5 hours Saturday)
- **Overtime Tracking**: Separate calculation and display of overtime hours from HR_T_Overtime table
- **Employee Integration**: Proper employee names from HR_M_EmployeePI table
- **Multiple Report Types**: Daily, monthly, and date range reports
- **Multiple Interfaces**: Command-line, GUI, and Web interfaces

### Web Interface Features (NEW!)
- **Monthly View**: Tampilkan semua data dikelompokkan per bulan - klik bulan untuk melihat laporan
- **Interactive Dashboard**: Modern, responsive web interface
- **Real-time Filtering**: Filter by date range, employee, shift, and business code
- **Summary Statistics**: Overview of total employees, hours, and averages
- **Data Export**: Export filtered results to Excel format
- **Search and Sort**: Advanced table functionality with DataTables
- **Dual Interface**: Tab untuk Monthly Reports dan Custom Date Range

### Business Logic
- **Weekday Rules**: Maximum 7 regular hours (Monday-Friday)
- **Saturday Rules**: Maximum 5 regular hours
- **Overtime Separation**: Overtime hours calculated separately from regular hours
- **Proper Data Joins**: Combines data from HR_T_TAMachine_Summary, HR_M_EmployeePI, and HR_T_Overtime tables

## Installation

### Prerequisites
- Python 3.7 or higher
- Access to VenusHR14 SQL Server database
- Required Python packages (automatically installed)

### Setup Instructions

1. **Install Dependencies**:
   ```
   install_dependencies.bat
   ```

2. **For Web Interface** (additional requirements):
   ```
   pip install -r requirements_web.txt
   ```

## Usage

### Web Interface (Recommended)

1. **Start the Web Server**:
   ```
   run_web.bat
   ```

2. **Access the Application**:
   - Open your browser and go to: `http://localhost:5000`
   - The web interface provides an intuitive dashboard for generating reports

3. **Using the Web Interface**:

   **Monthly Reports Tab (Recommended)**:
   - Masukkan Business Code (opsional, contoh: PTRJ)
   - Klik "Load Available Months" untuk melihat bulan-bulan yang tersedia
   - **Klik pada bulan yang ingin dilihat** (contoh: klik "May 2025")
   - Sistem akan menampilkan laporan lengkap untuk bulan tersebut
   - Klik "Export" untuk mengunduh laporan Excel bulan tersebut

   **Custom Date Range Tab**:
   - Set date range using the date pickers
   - Optionally filter by business code, employee, or shift
   - Click "Generate Report" to view data
   - Use "Export to Excel" to download reports
   - View summary statistics in the dashboard cards

### Command Line Interface

Run the CLI version using:
```
run_cli.bat
```

Or directly with Python:
```
python src/main.py --help
```

#### Examples:

Generate a daily report:
```
python src/main.py --daily 2025-02-05 --bus-code PTRJ
```

Generate a monthly report:
```
python src/main.py --monthly --year 2025 --month 2 --bus-code PTRJ
```

Generate a date range report:
```
python src/main.py --range --start-date 2025-02-01 --end-date 2025-02-28 --bus-code PTRJ
```

### GUI Interface

Run the GUI version using:
```
run_gui.bat
```

The GUI provides an easy-to-use interface with:
- Date picker for selecting report periods
- Business code selection
- Report type selection (daily, monthly, range)
- Export functionality

## Enhanced Report Format

The generated reports now include enhanced columns with proper business logic:

### Web Interface Columns:
- **Employee ID**: Employee identifier (e.g., PTRJ.241000144)
- **Employee Name**: Full employee name from HR_M_EmployeePI table
- **Date**: Attendance date
- **Day**: Day of the week (Monday, Tuesday, etc.)
- **Shift**: Work shift assignment
- **Check In**: Check-in time (HH:MM format)
- **Check Out**: Check-out time (HH:MM format)
- **Regular Hours**: Calculated regular work hours with business rules applied
  - Weekdays (Mon-Fri): Maximum 7 hours
  - Saturday: Maximum 5 hours
  - Excludes overtime hours
- **Overtime Hours**: Separate overtime hours from HR_T_Overtime table
- **Total Hours**: Sum of regular and overtime hours

### Excel Export Columns:
- **BusCode**: Business code (e.g., PTRJ)
- **EmployeeID**: Employee identifier
- **EmployeeName**: Employee name (from HR_M_EmployeePI or fallback to UserDeviceName)
- **TADate**: Attendance date
- **Shift**: Work shift
- **TACheckIn**: Check-in time
- **TACheckOut**: Check-out time
- **TotalMinutesWorked**: Total minutes between check-in and check-out
- **RegularHours**: Regular work hours (with business rules applied)
- **OvertimeHours**: Overtime hours (from HR_T_Overtime table)
- **DayOfWeek**: Day of the week

## Troubleshooting

If you encounter issues:

1. Check that the database connection is properly configured in the analyze_venus module
2. Verify that all required dependencies are installed
3. Check the log file at `attendance_report.log` for error messages
4. Ensure the VenusHR14 database is accessible

## Data Sources

The enhanced system now integrates data from multiple tables in the VenusHR14 database:

### Primary Tables:

#### HR_T_TAMachine_Summary (Main attendance data)
- **BusCode**: Business code (e.g., PTRJ)
- **UserDeviceID**: User ID on the device
- **UserDeviceName**: User name on the device (fallback for employee name)
- **EmployeeID**: Employee identifier (e.g., PTRJ.241000144)
- **TADate**: Attendance date
- **TACheckIn**: Check-in time (e.g., 07:29)
- **TACheckOut**: Check-out time (e.g., 17:37)
- **Shift**: Work shift assignment
- **IsCrossDay**: Flag indicating if attendance crosses midnight

#### HR_M_EmployeePI (Employee information)
- **EmployeeID**: Employee identifier (primary key)
- **EmployeeName**: Full employee name
- **BusCode**: Business code

#### HR_T_Overtime (Overtime data)
- **EmployeeID**: Employee identifier
- **OTDate**: Overtime date
- **OTStart**: Overtime start time
- **OTFinish**: Overtime finish time
- **OTBreak**: Break time in minutes
- **OTTimeDuration**: Total overtime duration in minutes

### Business Logic Implementation:

1. **Regular Hours Calculation**:
   - Total work time = TACheckOut - TACheckIn
   - Regular hours = Total work time - Overtime hours
   - Apply maximum limits: 7 hours (weekdays), 5 hours (Saturday)

2. **Overtime Hours**:
   - Extracted separately from HR_T_Overtime table
   - Converted from minutes to hours
   - Aggregated by employee and date

3. **Employee Names**:
   - Primary source: HR_M_EmployeePI.EmployeeName
   - Fallback: HR_T_TAMachine_Summary.UserDeviceName

## Web Interface Features

### Dashboard Components:
- **Filter Section**: Date range, business code, employee, and shift filters
- **Summary Cards**: Real-time statistics (total employees, hours, averages)
- **Data Table**: Interactive table with sorting, searching, and pagination
- **Export Function**: Download filtered results as Excel files

### API Endpoints:
- `GET /api/attendance` - Retrieve attendance data with filters
- `GET /api/employees` - Get employee list for filtering
- `GET /api/shifts` - Get available shifts
- `GET /api/export` - Export data to Excel
- `GET /api/summary` - Get summary statistics

### Technical Stack:
- **Backend**: Flask (Python)
- **Frontend**: Bootstrap 5, jQuery, DataTables
- **Database**: SQL Server (VenusHR14)
- **Export**: openpyxl (Excel generation)