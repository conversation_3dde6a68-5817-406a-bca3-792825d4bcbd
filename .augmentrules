# Project Intelligence: VenusHR14 Attendance System

## Coding Patterns

### Database Access Patterns
- **Always use parameterized queries** to prevent SQL injection
- **Read-only operations only** - no INSERT/UPDATE/DELETE on VenusHR14 database
- **LEFT JOIN pattern** for overtime data: `LEFT JOIN HR_T_Overtime o ON t.EmployeeID = o.EmployeeID AND t.AttendDate = o.OTDate`
- **Business code hardcoded** to 'PTRJ' throughout the system
- **Connection timeout** set to 30 seconds for all database operations

### Business Logic Patterns
- **Working hours format**: "(regular_hours) | (overtime_hours)" or "(regular_hours) | (-)"
- **Weekend rules**: Saturday max 5h regular, Sunday shows overtime only or "OFF"
- **Weekday rules**: Maximum 7 regular hours, excess goes to overtime
- **Color coding**: Green for meeting thresholds, red for below (based on regular hours only)

### Data Format Handling Patterns
- **Backend data formats**: Handle both object format `{normal_hours: 7, overtime_hours: 2, status: 'complete'}` and legacy string format `"(7) | (2)"`
- **Frontend compatibility**: Always check data type before processing (typeof hours === 'object' vs 'string')
- **Display conversion**: Convert object format to string format for UI display
- **Backward compatibility**: Maintain support for both formats to prevent breaking changes

### API Response Patterns
```json
{
    "success": true/false,
    "data": {...},
    "timestamp": "ISO format",
    "metadata": {"total_records": N, "query_time": "Xs"}
}
```

### Error Handling Patterns
- **Comprehensive try-catch blocks** for all database operations
- **Detailed logging** with timestamps and context
- **User-friendly error messages** in Indonesian
- **Graceful degradation** when services are unavailable

## User Preferences

### Interface Design
- **Indonesian localization** preferred for all user-facing text
- **Bootstrap 5** for consistent UI components
- **Sticky headers** for grid tables during vertical scroll
- **Color-coded cells** for immediate visual feedback
- **Loading indicators** with descriptive text and progress bars

### Data Display
- **Monthly grid format** as primary view
- **Station-grouped display** with collapsible sections
- **Split totals**: "DAYS Total", "REG Hours", "OT Hours"
- **Indonesian day abbreviations**: Min, Sen, Sel, Rab, Kam, Jum, Sab

### Export Preferences
- **Excel format** with formatting preservation using xlsxwriter
- **JSON format** with structured metadata for programmatic access
- **Multiple export options**: regular grid and station-grouped
- **Color preservation** in Excel exports

## Technical Constraints

### Database Constraints
- **Windows Authentication only** for SQL Server connection
- **ODBC Driver 17** required for SQL Server connectivity
- **Connection pooling** limited to prevent database overload
- **Query timeout** maximum 30 seconds

### Staging Database Patterns
- **SQLite database** stored in `data/staging_attendance.db` for local staging
- **Automatic schema migration** on startup to add missing columns
- **Required columns**: `id`, `employee_id`, `employee_name`, `date`, `station_code`, `machine_code`, `expense_code`, `total_hours`
- **UUID primary keys** for staging records to prevent conflicts
- **Status tracking** with 'staged' status for successful transfers
- **Operations logging** in `staging_operations_log` table for audit trail

### Performance Considerations
- **Large datasets** (>1000 employees) require optimization
- **Browser memory limits** for Excel export file sizes
- **IE11 compatibility** required (limited CSS Grid support)
- **Mobile responsiveness** planned for future implementation

### Security Requirements
- **Input validation** for all user inputs
- **CORS policy** restrictions for cross-origin requests
- **No direct database credentials** in frontend code
- **Audit trail** for all data access operations

## Known Issues & Workarounds

### Recently Resolved Critical Issues
- **Checkbox display in sync mode**: Fixed data format compatibility issue (2024-12-31)
- **Data format mismatch**: Backend object vs frontend string format resolved
- **Staging database transfer**: Fixed schema issues and implemented complete workflow (2024-12-31)
- **Database migration**: Created automatic column addition system for schema updates

### Resolved Issues
- **Overtime integration**: Fixed with proper LEFT JOIN implementation
- **Business rule calculations**: Resolved with day-of-week logic
- **Export formatting**: Fixed with xlsxwriter library
- **Sticky headers**: Resolved with CSS position:sticky
- **Loading feedback**: Enhanced with better indicators
- **Data format compatibility**: Fixed backend object vs frontend string format mismatch (2024-12-31)
- **Staging database schema**: Implemented automatic migration system for missing columns (2024-12-31)

### Performance Workarounds
- **Large dataset handling**: Implement pagination for >1000 employees
- **Export optimization**: Stream large Excel files instead of memory generation
- **Query optimization**: Use indexed columns for date range filtering

## Development Workflow

### Testing Approach
- **Database testing**: Use `test_months_debug.py` for standalone testing
- **API testing**: Use `/api/debug-months` for detailed diagnostics
- **Frontend testing**: Browser console for JavaScript debugging
- **Integration testing**: End-to-end workflow validation

### Debugging Tools
- **`/api/debug-months`**: Detailed API diagnostics
- **`test_months_debug.py`**: Standalone database connection testing
- **`/api/debug`**: General system information
- **Browser DevTools**: Frontend debugging and network inspection

### Code Organization
- **Backend**: `web_app.py` (Flask app), `modules/attendance_reporter.py` (business logic)
- **Frontend**: `static/app.js` (main logic), `templates/index.html` (UI structure)
- **Configuration**: `config.json` for database settings
- **Documentation**: `memory-bank/` for project context and progress

## Integration Patterns

### Google Sheets Sync
- **Apps Script URL** configuration for external sync
- **Data formatting** for Google Sheets compatibility
- **Error handling** for sync failures
- **Authentication flow** for secure access

### Export Integration
- **Multiple formats**: Excel (xlsxwriter) and JSON
- **Formatting preservation** in Excel exports
- **Metadata inclusion** in JSON exports
- **Station grouping** support in both formats

## Future Considerations

### Planned Enhancements
- **Mobile-first responsive design** for better mobile experience
- **Advanced analytics dashboard** for trend analysis
- **Automated report scheduling** for regular reports
- **Multi-business-code support** for expanded usage

### Architecture Evolution
- **Microservices consideration** for scalability
- **Caching layer** for improved performance
- **API versioning** for backward compatibility
- **Database optimization** for large-scale deployment

---
*Last Updated: 2024-12-31*
*Project Phase: Critical Issue Resolution & System Stabilization*
