"""
Logging Manager for Attendance Report System.
Handles centralized logging configuration and management.
Implements Single Responsibility Principle.
"""

import os
import logging
import logging.handlers
from typing import Dict, Optional
from datetime import datetime


class LoggingManager:
    """
    Centralized logging management with configurable handlers and formatters.
    Follows Single Responsibility Principle - only handles logging configuration.
    """
    
    def __init__(self, config_manager):
        """
        Initialize logging manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self._loggers: Dict[str, logging.Logger] = {}
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration based on config manager settings."""
        try:
            logging_config = self.config_manager.get_logging_config()
            
            # Get configuration values with defaults
            log_level = getattr(logging, logging_config.get('level', 'INFO').upper())
            log_format = logging_config.get('format', 
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            
            # Configure root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(log_level)
            
            # Clear existing handlers
            root_logger.handlers.clear()
            
            # Create formatter
            formatter = logging.Formatter(log_format)
            
            # Setup console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            
            # Setup file handler if enabled
            if logging_config.get('file_enabled', True):
                self._setup_file_handler(root_logger, formatter, logging_config)
            
            # Log successful initialization
            logger = self.get_logger(__name__)
            logger.info("✅ Logging manager initialized successfully")
            
        except Exception as e:
            # Fallback to basic logging if setup fails
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Logging manager setup failed, using basic configuration: {str(e)}")
    
    def _setup_file_handler(self, root_logger: logging.Logger, formatter: logging.Formatter, 
                           logging_config: Dict) -> None:
        """
        Setup rotating file handler for logging.
        
        Args:
            root_logger: Root logger instance
            formatter: Log formatter
            logging_config: Logging configuration dictionary
        """
        try:
            log_file_path = logging_config.get('file_path', 'logs/attendance_app.log')
            max_file_size = logging_config.get('max_file_size', 10485760)  # 10MB
            backup_count = logging_config.get('backup_count', 5)
            
            # Create logs directory if it doesn't exist
            log_dir = os.path.dirname(log_file_path)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file_path,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            
            file_handler.setLevel(logging.DEBUG)  # File gets all messages
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"⚠️ Failed to setup file logging: {str(e)}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get or create a logger with the specified name.
        
        Args:
            name (str): Logger name (typically __name__ of the calling module)
            
        Returns:
            logging.Logger: Configured logger instance
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def log_operation(self, operation_type: str, details: str, level: str = 'INFO', 
                     logger_name: Optional[str] = None) -> None:
        """
        Log an operation with standardized format.
        
        Args:
            operation_type (str): Type of operation (e.g., 'DATABASE', 'API', 'STAGING')
            details (str): Operation details
            level (str): Log level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
            logger_name (str, optional): Logger name, defaults to 'operations'
        """
        logger = self.get_logger(logger_name or 'operations')
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        timestamp = datetime.now().isoformat()
        message = f"[{operation_type}] {details} (timestamp: {timestamp})"
        
        logger.log(log_level, message)
    
    def log_performance(self, operation: str, duration: float, details: Optional[str] = None) -> None:
        """
        Log performance metrics.
        
        Args:
            operation (str): Operation name
            duration (float): Duration in seconds
            details (str, optional): Additional details
        """
        logger = self.get_logger('performance')
        
        message = f"PERFORMANCE: {operation} completed in {duration:.3f}s"
        if details:
            message += f" - {details}"
        
        # Log as WARNING if operation took too long (configurable threshold)
        performance_config = self.config_manager.get_performance_config()
        slow_threshold = performance_config.get('slow_operation_threshold', 5.0)  # 5 seconds
        
        if duration > slow_threshold:
            logger.warning(f"⚠️ SLOW OPERATION: {message}")
        else:
            logger.info(f"⚡ {message}")
    
    def log_error_with_context(self, error: Exception, context: Dict, logger_name: Optional[str] = None) -> None:
        """
        Log error with additional context information.
        
        Args:
            error (Exception): Exception that occurred
            context (dict): Additional context information
            logger_name (str, optional): Logger name, defaults to 'errors'
        """
        logger = self.get_logger(logger_name or 'errors')
        
        error_message = f"❌ ERROR: {str(error)}"
        
        if context:
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            error_message += f" | Context: {context_str}"
        
        logger.error(error_message, exc_info=True)
    
    def log_database_operation(self, operation: str, table: str, affected_rows: int = 0, 
                              duration: Optional[float] = None, success: bool = True) -> None:
        """
        Log database operations with standardized format.
        
        Args:
            operation (str): Database operation (SELECT, INSERT, UPDATE, DELETE)
            table (str): Table name
            affected_rows (int): Number of affected rows
            duration (float, optional): Operation duration in seconds
            success (bool): Whether operation was successful
        """
        logger = self.get_logger('database')
        
        status = "✅" if success else "❌"
        message = f"{status} DB {operation} on {table}: {affected_rows} rows"
        
        if duration is not None:
            message += f" in {duration:.3f}s"
        
        if success:
            logger.info(message)
        else:
            logger.error(message)
    
    def log_api_request(self, method: str, endpoint: str, status_code: int, 
                       duration: Optional[float] = None, user_ip: str = 'unknown') -> None:
        """
        Log API requests with standardized format.
        
        Args:
            method (str): HTTP method
            endpoint (str): API endpoint
            status_code (int): HTTP status code
            duration (float, optional): Request duration in seconds
            user_ip (str): User IP address
        """
        logger = self.get_logger('api')
        
        status_emoji = "✅" if 200 <= status_code < 400 else "❌"
        message = f"{status_emoji} {method} {endpoint} -> {status_code}"
        
        if duration is not None:
            message += f" ({duration:.3f}s)"
        
        message += f" from {user_ip}"
        
        if 200 <= status_code < 400:
            logger.info(message)
        elif 400 <= status_code < 500:
            logger.warning(message)
        else:
            logger.error(message)
    
    def get_log_statistics(self) -> Dict[str, int]:
        """
        Get logging statistics (if available).
        
        Returns:
            dict: Statistics about logged messages
        """
        # This is a basic implementation - could be enhanced with actual statistics
        return {
            'active_loggers': len(self._loggers),
            'handlers_count': len(logging.getLogger().handlers),
            'current_level': logging.getLogger().level
        }
    
    def set_log_level(self, level: str, logger_name: Optional[str] = None) -> bool:
        """
        Set log level for a specific logger or root logger.
        
        Args:
            level (str): Log level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
            logger_name (str, optional): Logger name, None for root logger
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            log_level = getattr(logging, level.upper())
            
            if logger_name:
                logger = self.get_logger(logger_name)
                logger.setLevel(log_level)
            else:
                logging.getLogger().setLevel(log_level)
            
            return True
            
        except AttributeError:
            self.get_logger(__name__).error(f"Invalid log level: {level}")
            return False
    
    def flush_logs(self) -> None:
        """Flush all log handlers."""
        for handler in logging.getLogger().handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
