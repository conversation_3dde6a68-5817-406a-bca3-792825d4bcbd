"""
API Utilities for Attendance Report System.
Contains common functions for API request/response handling.
Implements Single Responsibility Principle.
"""

import json
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from flask import request, jsonify, Response
from functools import wraps


def validate_required_params(required_params: list):
    """
    Decorator to validate required parameters in request.
    
    Args:
        required_params (list): List of required parameter names
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check query parameters
            missing_params = []
            for param in required_params:
                if not request.args.get(param) and not request.json.get(param, None) if request.json else True:
                    missing_params.append(param)
            
            if missing_params:
                return create_error_response(
                    f"Missing required parameters: {', '.join(missing_params)}",
                    400
                )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def validate_json_request(required_fields: Optional[list] = None):
    """
    Decorator to validate JSON request body.
    
    Args:
        required_fields (list, optional): List of required fields in JSON
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not request.is_json:
                return create_error_response("Request must be JSON", 400)
            
            try:
                data = request.get_json()
                if data is None:
                    return create_error_response("Invalid JSON data", 400)
                
                # Check required fields if specified
                if required_fields:
                    missing_fields = []
                    for field in required_fields:
                        if field not in data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        return create_error_response(
                            f"Missing required fields: {', '.join(missing_fields)}",
                            400
                        )
                
                return func(*args, **kwargs)
                
            except Exception as e:
                return create_error_response(f"JSON parsing error: {str(e)}", 400)
        
        return wrapper
    return decorator


def log_api_request(logger):
    """
    Decorator to log API requests.
    
    Args:
        logger: Logger instance
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            
            # Get request information
            method = request.method
            endpoint = request.endpoint or 'unknown'
            user_ip = request.environ.get('REMOTE_ADDR', 'unknown')
            user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')
            
            try:
                # Execute the function
                result = func(*args, **kwargs)
                
                # Calculate duration
                duration = (datetime.now() - start_time).total_seconds()
                
                # Determine status code
                if isinstance(result, tuple):
                    status_code = result[1] if len(result) > 1 else 200
                elif isinstance(result, Response):
                    status_code = result.status_code
                else:
                    status_code = 200
                
                # Log the request
                logger.info(f"API_REQUEST: {method} {endpoint} -> {status_code} ({duration:.3f}s) from {user_ip}")
                
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"API_ERROR: {method} {endpoint} -> 500 ({duration:.3f}s) from {user_ip}: {str(e)}")
                raise
        
        return wrapper
    return decorator


def create_success_response(data: Any = None, message: str = "Success", 
                           status_code: int = 200, **kwargs) -> Tuple[Response, int]:
    """
    Create standardized success response.
    
    Args:
        data: Response data
        message: Success message
        status_code: HTTP status code
        **kwargs: Additional response fields
        
    Returns:
        Tuple of (response, status_code)
    """
    response_data = {
        'success': True,
        'message': message,
        'timestamp': datetime.now().isoformat(),
        **kwargs
    }
    
    if data is not None:
        response_data['data'] = data
    
    return jsonify(response_data), status_code


def create_error_response(error_message: str, status_code: int = 500, 
                         error_code: Optional[str] = None, **kwargs) -> Tuple[Response, int]:
    """
    Create standardized error response.
    
    Args:
        error_message: Error message
        status_code: HTTP status code
        error_code: Optional error code
        **kwargs: Additional response fields
        
    Returns:
        Tuple of (response, status_code)
    """
    response_data = {
        'success': False,
        'error': error_message,
        'timestamp': datetime.now().isoformat(),
        **kwargs
    }
    
    if error_code:
        response_data['error_code'] = error_code
    
    return jsonify(response_data), status_code


def create_paginated_response(data: list, total_count: int, page: int = 1, 
                             per_page: int = 100, **kwargs) -> Tuple[Response, int]:
    """
    Create paginated response.
    
    Args:
        data: Response data
        total_count: Total number of records
        page: Current page number
        per_page: Records per page
        **kwargs: Additional response fields
        
    Returns:
        Tuple of (response, status_code)
    """
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_prev = page > 1
    
    response_data = {
        'success': True,
        'data': data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev
        },
        'timestamp': datetime.now().isoformat(),
        **kwargs
    }
    
    return jsonify(response_data), 200


def parse_date_range(start_date_param: str, end_date_param: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Parse and validate date range parameters.
    
    Args:
        start_date_param: Start date parameter value
        end_date_param: End date parameter value
        
    Returns:
        Tuple of (start_date, end_date, error_message)
    """
    try:
        if not start_date_param or not end_date_param:
            return None, None, "Both start_date and end_date are required"
        
        # Validate date format
        start_date = datetime.strptime(start_date_param, '%Y-%m-%d').strftime('%Y-%m-%d')
        end_date = datetime.strptime(end_date_param, '%Y-%m-%d').strftime('%Y-%m-%d')
        
        # Validate date range
        if start_date > end_date:
            return None, None, "start_date must be before or equal to end_date"
        
        return start_date, end_date, None
        
    except ValueError as e:
        return None, None, f"Invalid date format. Expected YYYY-MM-DD: {str(e)}"


def parse_pagination_params(default_per_page: int = 100, max_per_page: int = 1000) -> Tuple[int, int]:
    """
    Parse pagination parameters from request.
    
    Args:
        default_per_page: Default records per page
        max_per_page: Maximum allowed records per page
        
    Returns:
        Tuple of (page, per_page)
    """
    try:
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(max_per_page, max(1, int(request.args.get('per_page', default_per_page))))
        return page, per_page
    except ValueError:
        return 1, default_per_page


def get_user_context() -> Dict[str, str]:
    """
    Get user context information from request.
    
    Returns:
        Dictionary with user context
    """
    return {
        'ip_address': request.environ.get('REMOTE_ADDR', 'unknown'),
        'user_agent': request.environ.get('HTTP_USER_AGENT', 'unknown'),
        'method': request.method,
        'endpoint': request.endpoint or 'unknown',
        'timestamp': datetime.now().isoformat()
    }


def validate_filters(allowed_filters: list) -> Dict[str, Any]:
    """
    Validate and extract filters from request parameters.
    
    Args:
        allowed_filters: List of allowed filter parameter names
        
    Returns:
        Dictionary of validated filters
    """
    filters = {}
    
    for filter_name in allowed_filters:
        value = request.args.get(filter_name)
        if value is not None and value.strip():
            filters[filter_name] = value.strip()
    
    return filters


def handle_database_error(error: Exception, operation: str = "database operation") -> Tuple[Response, int]:
    """
    Handle database errors with appropriate response.
    
    Args:
        error: Database error exception
        operation: Description of the operation that failed
        
    Returns:
        Tuple of (error_response, status_code)
    """
    error_message = str(error)
    
    # Check for specific database error types
    if "connection" in error_message.lower():
        return create_error_response(
            f"Database connection error during {operation}",
            503,
            "DATABASE_CONNECTION_ERROR"
        )
    elif "timeout" in error_message.lower():
        return create_error_response(
            f"Database timeout during {operation}",
            504,
            "DATABASE_TIMEOUT"
        )
    else:
        return create_error_response(
            f"Database error during {operation}: {error_message}",
            500,
            "DATABASE_ERROR"
        )


def format_response_data(data: Any, format_type: str = 'json') -> Any:
    """
    Format response data based on requested format.
    
    Args:
        data: Data to format
        format_type: Requested format ('json', 'csv', 'excel')
        
    Returns:
        Formatted data
    """
    if format_type.lower() == 'json':
        return data
    elif format_type.lower() == 'csv':
        from utils.data_processor import format_data_for_csv
        return format_data_for_csv(data)
    elif format_type.lower() == 'excel':
        from utils.data_processor import format_data_for_excel
        return format_data_for_excel(data)
    else:
        raise ValueError(f"Unsupported format type: {format_type}")


def safe_int_conversion(value: Any, default: int = 0) -> int:
    """
    Safely convert value to integer.
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Integer value
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """
    Safely convert value to float.
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Float value
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default
