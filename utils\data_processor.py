"""
Data Processing Utilities for Attendance Report System.
Contains functions for parsing, validating, and transforming data.
Implements Single Responsibility Principle.
"""

import re
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta


def parse_charge_job_data(charge_job_string: str) -> Dict[str, str]:
    """
    Parse charge job data string according to the specified format with enhanced edge case handling.
    
    Expected format: "TaskCode/StationCode/MachineCode/ExpenseCode"
    
    Args:
        charge_job_string (str): Raw charge job string to parse
        
    Returns:
        dict: Dictionary with parsed components
    """
    if not charge_job_string or not isinstance(charge_job_string, str):
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': ''
        }
    
    # Clean the input string
    cleaned_string = charge_job_string.strip()
    
    # Try primary parsing strategy with forward slash
    if '/' in cleaned_string:
        result = parse_with_fallback_strategy(cleaned_string, '/', '\\')
        if result:
            return result
    
    # Try alternative parsing strategy with backslash
    if '\\' in cleaned_string:
        result = parse_with_fallback_strategy(cleaned_string, '\\', '/')
        if result:
            return result
    
    # Try parsing with other common separators
    for separator in ['-', '_', '|', ';', ':']:
        if separator in cleaned_string:
            result = parse_with_fallback_strategy(cleaned_string, separator, '/')
            if result:
                return result
    
    # If no separators found, return the entire string as expense code
    return {
        'task_code': '',
        'station_code': '',
        'machine_code': '',
        'expense_code': cleaned_string
    }


def parse_with_fallback_strategy(charge_job_string: str, primary_separator: str, fallback_separator: str) -> Optional[Dict[str, str]]:
    """
    Advanced parsing strategy to handle edge cases like task codes with embedded slashes.
    
    Args:
        charge_job_string (str): String to parse
        primary_separator (str): Primary separator to use
        fallback_separator (str): Fallback separator if primary fails
        
    Returns:
        dict or None: Parsed components or None if parsing fails
    """
    try:
        # Split by primary separator
        components = charge_job_string.split(primary_separator)
        
        # Handle different component counts
        if len(components) == 4:
            # Perfect case: exactly 4 components
            return {
                'task_code': components[0].strip(),
                'station_code': components[1].strip(),
                'machine_code': components[2].strip(),
                'expense_code': components[3].strip()
            }
        
        elif len(components) == 3:
            # 3 components: assume missing one field (usually station_code)
            return {
                'task_code': components[0].strip(),
                'station_code': '',
                'machine_code': components[1].strip(),
                'expense_code': components[2].strip()
            }
        
        elif len(components) == 2:
            # 2 components: assume task_code and expense_code
            return {
                'task_code': components[0].strip(),
                'station_code': '',
                'machine_code': '',
                'expense_code': components[1].strip()
            }
        
        elif len(components) > 4:
            # More than 4 components: try to intelligently merge
            # This handles cases like "TASK/CODE/STATION/MACHINE/EXPENSE"
            # where TASK/CODE should be treated as one task code
            
            # Strategy 1: Take first component as task, last as expense, middle as station/machine
            if len(components) == 5:
                return {
                    'task_code': f"{components[0]}/{components[1]}".strip(),
                    'station_code': components[2].strip(),
                    'machine_code': components[3].strip(),
                    'expense_code': components[4].strip()
                }
            
            # Strategy 2: Take first as task, last as expense, combine middle
            else:
                middle_components = components[1:-1]
                return {
                    'task_code': components[0].strip(),
                    'station_code': middle_components[0].strip() if middle_components else '',
                    'machine_code': middle_components[1].strip() if len(middle_components) > 1 else '',
                    'expense_code': components[-1].strip()
                }
        
        else:
            # Single component or empty: return as expense code
            return {
                'task_code': '',
                'station_code': '',
                'machine_code': '',
                'expense_code': charge_job_string.strip()
            }
    
    except Exception:
        # If all strategies fail, return None to try next separator
        return None


def validate_attendance_record(record: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate attendance record data.
    
    Args:
        record (dict): Attendance record to validate
        
    Returns:
        tuple: (is_valid, list_of_errors)
    """
    errors = []
    
    # Required fields
    required_fields = ['EmployeeID', 'EmployeeName', 'TADate']
    for field in required_fields:
        if not record.get(field):
            errors.append(f"Missing required field: {field}")
    
    # Validate date format
    if 'TADate' in record and record['TADate']:
        try:
            datetime.strptime(record['TADate'], '%Y-%m-%d')
        except ValueError:
            errors.append("Invalid date format in TADate. Expected YYYY-MM-DD")
    
    # Validate time formats
    time_fields = ['CheckIn', 'CheckOut']
    for field in time_fields:
        if field in record and record[field]:
            if not validate_time_format(record[field]):
                errors.append(f"Invalid time format in {field}")
    
    # Validate numeric fields
    numeric_fields = ['RegularHours', 'OvertimeHours', 'TotalHours']
    for field in numeric_fields:
        if field in record and record[field] is not None:
            try:
                value = float(record[field])
                if value < 0:
                    errors.append(f"Negative value not allowed for {field}")
                if value > 24:  # Reasonable upper limit for hours
                    errors.append(f"Value too large for {field} (max 24 hours)")
            except (ValueError, TypeError):
                errors.append(f"Invalid numeric value for {field}")
    
    # Validate boolean fields
    boolean_fields = ['IsAlfa', 'IsOnLeave']
    for field in boolean_fields:
        if field in record and record[field] is not None:
            if not isinstance(record[field], (bool, int)) or record[field] not in [0, 1, True, False]:
                errors.append(f"Invalid boolean value for {field}")
    
    # Business logic validations
    if record.get('CheckIn') and record.get('CheckOut'):
        if not validate_check_times(record['CheckIn'], record['CheckOut']):
            errors.append("Check-out time must be after check-in time")
    
    return len(errors) == 0, errors


def validate_time_format(time_string: str) -> bool:
    """
    Validate time format (HH:MM or HH:MM:SS).
    
    Args:
        time_string (str): Time string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not time_string:
        return True  # Empty time is valid
    
    # Try different time formats
    time_formats = ['%H:%M', '%H:%M:%S', '%I:%M %p', '%I:%M:%S %p']
    
    for fmt in time_formats:
        try:
            datetime.strptime(time_string.strip(), fmt)
            return True
        except ValueError:
            continue
    
    return False


def validate_check_times(check_in: str, check_out: str) -> bool:
    """
    Validate that check-out time is after check-in time.
    
    Args:
        check_in (str): Check-in time string
        check_out (str): Check-out time string
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        # Parse times (assuming same day for simplicity)
        check_in_time = datetime.strptime(check_in.strip(), '%H:%M')
        check_out_time = datetime.strptime(check_out.strip(), '%H:%M')
        
        # Handle overnight shifts (check-out next day)
        if check_out_time < check_in_time:
            check_out_time += timedelta(days=1)
        
        return check_out_time > check_in_time
        
    except ValueError:
        # If parsing fails, assume valid (let other validations handle format errors)
        return True


def calculate_working_hours(check_in: str, check_out: str, break_minutes: int = 0) -> Dict[str, float]:
    """
    Calculate working hours from check-in and check-out times.
    
    Args:
        check_in (str): Check-in time string
        check_out (str): Check-out time string
        break_minutes (int): Break time in minutes to subtract
        
    Returns:
        dict: Dictionary with calculated hours
    """
    try:
        # Parse times
        check_in_time = datetime.strptime(check_in.strip(), '%H:%M')
        check_out_time = datetime.strptime(check_out.strip(), '%H:%M')
        
        # Handle overnight shifts
        if check_out_time < check_in_time:
            check_out_time += timedelta(days=1)
        
        # Calculate total time
        total_time = check_out_time - check_in_time
        total_minutes = total_time.total_seconds() / 60
        
        # Subtract break time
        working_minutes = max(0, total_minutes - break_minutes)
        working_hours = working_minutes / 60
        
        # Calculate regular and overtime hours (assuming 8-hour regular day)
        regular_hours = min(working_hours, 8.0)
        overtime_hours = max(0, working_hours - 8.0)
        
        return {
            'total_hours': round(working_hours, 2),
            'regular_hours': round(regular_hours, 2),
            'overtime_hours': round(overtime_hours, 2)
        }
        
    except ValueError as e:
        return {
            'total_hours': 0.0,
            'regular_hours': 0.0,
            'overtime_hours': 0.0,
            'error': str(e)
        }


def format_data_for_export(data: List[Dict[str, Any]], format_type: str) -> Any:
    """
    Format data for export in specified format.
    
    Args:
        data (list): Data to format
        format_type (str): Export format ('json', 'csv', 'excel')
        
    Returns:
        Formatted data
    """
    if format_type.lower() == 'json':
        return data
    
    elif format_type.lower() == 'csv':
        return format_data_for_csv(data)
    
    elif format_type.lower() == 'excel':
        return format_data_for_excel(data)
    
    else:
        raise ValueError(f"Unsupported export format: {format_type}")


def format_data_for_csv(data: List[Dict[str, Any]]) -> str:
    """
    Format data as CSV string.
    
    Args:
        data (list): Data to format
        
    Returns:
        str: CSV formatted string
    """
    if not data:
        return ""
    
    import csv
    import io
    
    output = io.StringIO()
    
    # Get all unique keys from all records
    all_keys = set()
    for record in data:
        all_keys.update(record.keys())
    
    fieldnames = sorted(all_keys)
    
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    
    for record in data:
        # Convert all values to strings and handle None values
        clean_record = {}
        for key in fieldnames:
            value = record.get(key, '')
            clean_record[key] = str(value) if value is not None else ''
        writer.writerow(clean_record)
    
    return output.getvalue()


def format_data_for_excel(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Format data for Excel export.
    
    Args:
        data (list): Data to format
        
    Returns:
        dict: Excel-ready data structure
    """
    if not data:
        return {'headers': [], 'rows': []}
    
    # Get all unique keys from all records
    all_keys = set()
    for record in data:
        all_keys.update(record.keys())
    
    headers = sorted(all_keys)
    rows = []
    
    for record in data:
        row = []
        for header in headers:
            value = record.get(header, '')
            # Convert to appropriate type for Excel
            if isinstance(value, (int, float)):
                row.append(value)
            elif isinstance(value, bool):
                row.append('Yes' if value else 'No')
            else:
                row.append(str(value) if value is not None else '')
        rows.append(row)
    
    return {
        'headers': headers,
        'rows': rows,
        'total_records': len(rows)
    }


def clean_string_data(value: Any) -> str:
    """
    Clean and normalize string data.
    
    Args:
        value: Value to clean
        
    Returns:
        str: Cleaned string
    """
    if value is None:
        return ''
    
    # Convert to string
    str_value = str(value).strip()
    
    # Remove extra whitespace
    str_value = re.sub(r'\s+', ' ', str_value)
    
    # Remove non-printable characters
    str_value = re.sub(r'[^\x20-\x7E]', '', str_value)
    
    return str_value


def normalize_employee_name(name: str) -> str:
    """
    Normalize employee name for consistent matching.
    
    Args:
        name (str): Employee name to normalize
        
    Returns:
        str: Normalized name
    """
    if not name:
        return ''
    
    # Clean the name
    normalized = clean_string_data(name)
    
    # Convert to uppercase for consistent comparison
    normalized = normalized.upper()
    
    # Remove common prefixes/suffixes
    prefixes = ['MR.', 'MRS.', 'MS.', 'DR.']
    for prefix in prefixes:
        if normalized.startswith(prefix + ' '):
            normalized = normalized[len(prefix):].strip()
    
    return normalized
