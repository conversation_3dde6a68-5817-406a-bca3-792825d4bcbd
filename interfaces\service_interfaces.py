"""
Service interfaces for Attendance Report System.
Defines abstract base classes for business logic services.
Implements Dependency Inversion Principle.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple


class IAttendanceService(ABC):
    """Abstract interface for attendance business logic."""
    
    @abstractmethod
    def get_attendance_data(self, start_date: str, end_date: str, 
                           bus_code: Optional[str] = None, 
                           employee_id: Optional[str] = None,
                           shift: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get attendance data with filters."""
        pass
    
    @abstractmethod
    def get_monthly_attendance_grid(self, year: int, month: int, 
                                   bus_code: Optional[str] = None) -> Dict[str, Any]:
        """Get monthly attendance in grid format."""
        pass
    
    @abstractmethod
    def get_attendance_summary(self, start_date: str, end_date: str, 
                              bus_code: Optional[str] = None) -> Dict[str, Any]:
        """Get attendance summary statistics."""
        pass
    
    @abstractmethod
    def export_attendance_report(self, start_date: str, end_date: str, 
                                format_type: str, bus_code: Optional[str] = None) -> Any:
        """Export attendance report in specified format."""
        pass
    
    @abstractmethod
    def get_available_months(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available months with data."""
        pass


class IStagingService(ABC):
    """Abstract interface for staging operations."""
    
    @abstractmethod
    def initialize_staging_database(self) -> bool:
        """Initialize staging database."""
        pass
    
    @abstractmethod
    def get_staging_data(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Get staging data with pagination and filters."""
        pass
    
    @abstractmethod
    def add_staging_records(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add multiple records to staging."""
        pass
    
    @abstractmethod
    def update_staging_record(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update staging record."""
        pass
    
    @abstractmethod
    def delete_staging_record(self, record_id: str) -> bool:
        """Delete staging record."""
        pass
    
    @abstractmethod
    def move_to_staging(self, attendance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Move attendance records to staging."""
        pass
    
    @abstractmethod
    def get_staging_statistics(self) -> Dict[str, Any]:
        """Get staging statistics."""
        pass
    
    @abstractmethod
    def cleanup_staging_duplicates(self) -> Dict[str, Any]:
        """Clean up duplicate staging records."""
        pass
    
    @abstractmethod
    def get_staging_health_status(self) -> Dict[str, Any]:
        """Get staging database health status."""
        pass


class IEmployeeService(ABC):
    """Abstract interface for employee operations."""
    
    @abstractmethod
    def get_employees_list(self, bus_code: Optional[str] = None, 
                          filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get list of employees with optional filters."""
        pass
    
    @abstractmethod
    def get_employee_charge_jobs(self) -> Tuple[bool, Dict[str, Any]]:
        """Get employee charge job data from external API."""
        pass
    
    @abstractmethod
    def get_shifts_list(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available shifts."""
        pass
    
    @abstractmethod
    def get_leave_data(self, start_date: str, end_date: str, 
                      bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get employee leave data."""
        pass
    
    @abstractmethod
    def enhance_employee_data_with_charge_jobs(self, employee_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance employee data with charge job information."""
        pass


class IDataProcessingService(ABC):
    """Abstract interface for data processing operations."""
    
    @abstractmethod
    def parse_charge_job_data(self, charge_job_string: str) -> Dict[str, str]:
        """Parse charge job data string."""
        pass
    
    @abstractmethod
    def validate_attendance_record(self, record: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate attendance record."""
        pass
    
    @abstractmethod
    def format_data_for_export(self, data: List[Dict[str, Any]], 
                              format_type: str) -> Any:
        """Format data for export."""
        pass
    
    @abstractmethod
    def calculate_working_hours(self, check_in: str, check_out: str) -> Dict[str, float]:
        """Calculate working hours from check-in/check-out times."""
        pass


class IExternalApiService(ABC):
    """Abstract interface for external API operations."""
    
    @abstractmethod
    def fetch_charge_job_data(self) -> Tuple[bool, Dict[str, Any]]:
        """Fetch charge job data from Google Apps Script."""
        pass
    
    @abstractmethod
    def sync_attendance_data(self, data: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """Sync attendance data to external system."""
        pass
    
    @abstractmethod
    def test_api_connectivity(self) -> Dict[str, Any]:
        """Test connectivity to external APIs."""
        pass


class ICacheService(ABC):
    """Abstract interface for caching operations."""
    
    @abstractmethod
    def get_cached_data(self, key: str) -> Any:
        """Get cached data."""
        pass
    
    @abstractmethod
    def cache_data(self, key: str, data: Any, timeout: Optional[int] = None) -> bool:
        """Cache data with optional timeout."""
        pass
    
    @abstractmethod
    def invalidate_cache(self, pattern: Optional[str] = None) -> bool:
        """Invalidate cache entries."""
        pass
    
    @abstractmethod
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        pass
