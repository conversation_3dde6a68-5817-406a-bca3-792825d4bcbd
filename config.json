{"database_config": {"connection_mode": "remote", "fallback_enabled": true, "connection_timeout": 30, "retry_attempts": 3, "local_database": {"server": "localhost", "database": "VenusHR14", "authentication": "sql_server", "username": "sa", "password": "windows0819", "port": 1433, "trusted_connection": false, "driver": "ODBC Driver 17 for SQL Server"}, "remote_database": {"server": "********", "database": "VenusHR14", "authentication": "sql_server", "username": "sa", "password": "supp0rt@", "port": 1888, "trusted_connection": false, "driver": "ODBC Driver 17 for SQL Server"}, "schema_validation": {"required_tables": ["HR_T_TAMachine_Summary", "HR_M_EmployeePI", "HR_T_Overtime"], "verify_schema_parity": true}, "ptrj_mill_database": {"server": "********", "database": "db_ptrj_mill", "authentication": "sql_server", "username": "sa", "password": "supp0rt@", "port": 1888, "trusted_connection": false, "driver": "ODBC Driver 17 for SQL Server", "enabled": true, "timeout": 15, "fallback_enabled": true}}, "google_apps_script": {"sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec", "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec", "charge_job_editor_url": "https://script.google.com/u/0/home/<USER>/19sqbcDCTpksXNL8W4QZkS-vSkBZZhfYHby7A71jpGub9XfjPP4b7FUAY/edit"}, "server_config": {"port": 5173, "host": "0.0.0.0", "debug": true}, "charge_job_parsing": {"primary_separator": " / ", "fallback_separator": "/", "formats": {"4_component": {"description": "Format: TaskCode / StationCode / MachineCode / ExpenseCode", "components": ["task_code", "station_code", "machine_code", "expense_code"], "separators": 3}, "3_component": {"description": "Format: TaskCode / StationCode / ExpenseCode (no Machine Code)", "components": ["task_code", "station_code", "expense_code"], "separators": 2}, "2_component": {"description": "Format: TaskCode / ExpenseCode (no Station Code, no Machine Code)", "components": ["task_code", "expense_code"], "separators": 1}}, "edge_cases": {"task_code_with_slash": {"description": "Handle task codes containing forward slash (e.g., 'C/Roll Wages')", "strategy": "Use space-slash-space as primary delimiter, fallback to slash-only if needed"}, "single_separator": {"description": "Handle cases with only one separator", "mapping": "Task Code / Expense Code (Station and Machine empty)"}}}, "data_sources": {"employee_data_sheet": "EmployeeData", "charge_job_column": "E", "task_code_column": "E"}, "sync_settings": {"timeout_seconds": 30, "retry_attempts": 3, "staging_environment": true, "default_sync_mode": "local_staging"}, "staging_config": {"database_table": "staging_attendance", "max_records": 10000, "auto_cleanup_days": 30, "default_mode": "Local Sync Staging"}, "api_endpoints": {"employee_charge_jobs": "/api/employee-charge-jobs", "sync_to_spreadsheet": "/api/sync-to-spreadsheet", "monthly_grid": "/api/monthly-grid", "staging_data": "/api/staging/data", "staging_upload": "/api/staging/upload"}}