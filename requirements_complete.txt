"""
Complete Requirements for Venus Attendance Report Web Application
Self-contained version with all necessary dependencies
"""

# Core Flask and Web Framework
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7

# Database connectivity
pyodbc==4.0.39

# Data processing and analysis
pandas==2.1.1
numpy==1.25.2

# Excel export functionality
openpyxl==3.1.2
xlsxwriter==3.1.9

# HTTP requests for external APIs
requests==2.31.0

# Date and time utilities
python-dateutil==2.8.2

# JSON handling (usually built-in, but explicit version)
jsonschema==4.19.1

# Logging and utilities
colorlog==6.7.0

# Type hints and annotations
typing-extensions==4.8.0

# Security and encoding
urllib3==2.0.7
certifi==2023.7.22

# Optional: Development and testing tools
# pytest==7.4.2
# pytest-flask==1.3.0

# Optional: Performance monitoring
# psutil==5.9.6

# Windows-specific ODBC drivers (if needed)
# Note: ODBC Driver 17 for SQL Server must be installed separately on the system

# Additional utilities for robust error handling
six==1.16.0
idna==3.4
charset-normalizer==3.3.0 