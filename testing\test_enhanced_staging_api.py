#!/usr/bin/env python3
"""
Test script for Enhanced Staging API with PTRJ Employee ID Integration
Tests both legacy format and new grouped structure endpoints.
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:5173"
LEGACY_ENDPOINT = f"{BASE_URL}/api/staging/data"
GROUPED_ENDPOINT = f"{BASE_URL}/api/staging/data-grouped"

def test_legacy_api():
    """Test legacy staging API with PTRJ Employee ID integration."""
    print("🧪 Testing Legacy Staging API...")
    
    try:
        # Test basic call
        response = requests.get(f"{LEGACY_ENDPOINT}?limit=5")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Legacy API Response Status: {response.status_code}")
        print(f"📊 Total Records: {data.get('total_records', 0)}")
        print(f"👥 Total Employees: {data.get('total_employees', 0)}")
        print(f"📁 Structure Optimized: {data.get('structure_optimized', False)}")
        
        # Check if we have data
        if data.get('data') and len(data['data']) > 0:
            first_record = data['data'][0]
            print(f"\n📋 Sample Record Structure:")
            print(f"   🆔 ID: {first_record.get('id', 'N/A')}")
            print(f"   👤 Employee Name: {first_record.get('employee_name', 'N/A')}")
            print(f"   🏢 Venus Employee ID: {first_record.get('employee_id', 'N/A')}")
            print(f"   🏭 PTRJ Employee ID: {first_record.get('ptrj_employee_id', 'N/A')}")
            print(f"   📅 Date: {first_record.get('date', 'N/A')}")
            print(f"   ⏰ Total Hours: {first_record.get('total_hours', 'N/A')}")
            print(f"   🏗️ Task Code: {first_record.get('task_code', 'N/A')}")
            print(f"   🏭 Station Code: {first_record.get('station_code', 'N/A')}")
            
            # Check PTRJ Employee ID integration
            ptrj_ids = [r.get('ptrj_employee_id') for r in data['data'] if r.get('ptrj_employee_id')]
            print(f"\n🔗 PTRJ Integration Status:")
            print(f"   📊 Records with PTRJ ID: {len(ptrj_ids)}/{len(data['data'])}")
            print(f"   ✅ Integration Success Rate: {(len(ptrj_ids)/len(data['data']))*100:.1f}%")
            
        else:
            print("⚠️  No data returned from legacy API")
            
    except Exception as e:
        print(f"❌ Legacy API Test Failed: {str(e)}")

def test_grouped_api():
    """Test new grouped staging API."""
    print("\n🧪 Testing Grouped Staging API...")
    
    try:
        # Test basic call
        response = requests.get(f"{GROUPED_ENDPOINT}?limit=5")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Grouped API Response Status: {response.status_code}")
        print(f"📊 Total Records: {data.get('total_records', 0)}")
        print(f"👥 Total Employees: {data.get('total_employees', 0)}")
        print(f"🏗️ Structure Type: {data.get('structure_type', 'N/A')}")
        
        # Check PTRJ integration info
        ptrj_info = data.get('ptrj_integration', {})
        print(f"🔗 PTRJ Integration: {ptrj_info.get('enabled', False)}")
        
        # Check if we have data
        if data.get('data') and len(data['data']) > 0:
            first_employee = data['data'][0]
            identity = first_employee.get('identitas_karyawan', {})
            attendance = first_employee.get('data_presensi', [])
            
            print(f"\n📋 Sample Employee Structure:")
            print(f"   👤 Employee Name: {identity.get('employee_name', 'N/A')}")
            print(f"   🏢 Venus Employee ID: {identity.get('employee_id_venus', 'N/A')}")
            print(f"   🏭 PTRJ Employee ID: {identity.get('employee_id_ptrj', 'N/A')}")
            print(f"   🏗️ Task Code: {identity.get('task_code', 'N/A')}")
            print(f"   🏭 Station Code: {identity.get('station_code', 'N/A')}")
            print(f"   ⚙️ Machine Code: {identity.get('machine_code', 'N/A')}")
            print(f"   💰 Expense Code: {identity.get('expense_code', 'N/A')}")
            print(f"   📊 Attendance Records: {len(attendance)}")
            
            if attendance:
                first_attendance = attendance[0]
                print(f"\n📅 Sample Attendance Record:")
                print(f"   📅 Date: {first_attendance.get('date', 'N/A')}")
                print(f"   ⏰ Total Hours: {first_attendance.get('total_hours', 'N/A')}")
                print(f"   🕐 Check In: {first_attendance.get('check_in', 'N/A')}")
                print(f"   🕕 Check Out: {first_attendance.get('check_out', 'N/A')}")
                print(f"   📈 Regular Hours: {first_attendance.get('regular_hours', 'N/A')}")
                print(f"   ⏱️ Overtime Hours: {first_attendance.get('overtime_hours', 'N/A')}")
                print(f"   🏠 On Leave: {first_attendance.get('is_on_leave', 'N/A')}")
                print(f"   ❌ ALFA Status: {first_attendance.get('is_alfa', 'N/A')}")
            
            # Calculate structure efficiency
            total_employees = len(data['data'])
            total_attendance_records = sum(len(emp['data_presensi']) for emp in data['data'])
            avg_records_per_employee = total_attendance_records / total_employees if total_employees > 0 else 0
            
            print(f"\n📈 Structure Efficiency Analysis:")
            print(f"   👥 Total Employees: {total_employees}")
            print(f"   📊 Total Attendance Records: {total_attendance_records}")
            print(f"   📊 Avg Records per Employee: {avg_records_per_employee:.1f}")
            
            # Check PTRJ ID coverage
            employees_with_ptrj = sum(1 for emp in data['data'] 
                                    if emp['identitas_karyawan'].get('employee_id_ptrj') 
                                    and emp['identitas_karyawan'].get('employee_id_ptrj') != 'N/A')
            ptrj_coverage = (employees_with_ptrj / total_employees * 100) if total_employees > 0 else 0
            
            print(f"   🔗 PTRJ ID Coverage: {employees_with_ptrj}/{total_employees} ({ptrj_coverage:.1f}%)")
            
        else:
            print("⚠️  No data returned from grouped API")
            
    except Exception as e:
        print(f"❌ Grouped API Test Failed: {str(e)}")

def test_api_comparison():
    """Compare data efficiency between legacy and grouped APIs."""
    print("\n🧪 Testing API Efficiency Comparison...")
    
    try:
        # Test same data with both APIs
        params = "?limit=10&start_date=2025-06-01&end_date=2025-06-30"
        
        # Legacy API
        legacy_response = requests.get(f"{LEGACY_ENDPOINT}{params}")
        legacy_data = legacy_response.json()
        legacy_size = len(legacy_response.content)
        
        # Grouped API
        grouped_response = requests.get(f"{GROUPED_ENDPOINT}{params}")
        grouped_data = grouped_response.json()
        grouped_size = len(grouped_response.content)
        
        print(f"📊 Data Size Comparison:")
        print(f"   📁 Legacy API Response Size: {legacy_size:,} bytes")
        print(f"   📁 Grouped API Response Size: {grouped_size:,} bytes")
        
        if legacy_size > 0:
            efficiency_gain = ((legacy_size - grouped_size) / legacy_size) * 100
            print(f"   📈 Efficiency Gain: {efficiency_gain:.1f}%")
            
            if efficiency_gain > 0:
                print(f"   ✅ Grouped structure is more efficient!")
            else:
                print(f"   ⚠️  Legacy structure is more compact for this dataset")
        
        # Compare record counts
        legacy_records = len(legacy_data.get('data', []))
        grouped_employees = len(grouped_data.get('data', []))
        grouped_records = sum(len(emp.get('data_presensi', [])) 
                            for emp in grouped_data.get('data', []))
        
        print(f"\n📊 Record Count Comparison:")
        print(f"   📁 Legacy: {legacy_records} flat records")
        print(f"   📁 Grouped: {grouped_employees} employees, {grouped_records} attendance records")
        print(f"   ✅ Data Integrity: {legacy_records == grouped_records}")
        
    except Exception as e:
        print(f"❌ API Comparison Test Failed: {str(e)}")

def test_filtering_and_pagination():
    """Test filtering and pagination features."""
    print("\n🧪 Testing Filtering and Pagination...")
    
    try:
        # Test date filtering
        start_date = "2025-06-01"
        end_date = "2025-06-15"
        
        response = requests.get(f"{GROUPED_ENDPOINT}?start_date={start_date}&end_date={end_date}&limit=3")
        data = response.json()
        
        print(f"📅 Date Filter Test ({start_date} to {end_date}):")
        print(f"   📊 Returned Records: {data.get('returned_records', 0)}")
        print(f"   👥 Employees: {data.get('total_employees', 0)}")
        
        # Check if dates are within range
        if data.get('data'):
            all_dates = []
            for emp in data['data']:
                for record in emp.get('data_presensi', []):
                    all_dates.append(record.get('date'))
            
            dates_in_range = all(start_date <= date <= end_date for date in all_dates if date)
            print(f"   ✅ All dates in range: {dates_in_range}")
        
        # Test pagination
        pagination = data.get('pagination', {})
        print(f"\n📄 Pagination Info:")
        print(f"   📊 Limit: {pagination.get('limit', 'N/A')}")
        print(f"   📊 Offset: {pagination.get('offset', 'N/A')}")
        print(f"   📊 Has More: {pagination.get('has_more', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Filtering and Pagination Test Failed: {str(e)}")

def test_charge_job_integration():
    """Test charge job data integration."""
    print("\n🧪 Testing Charge Job Integration...")
    
    try:
        response = requests.get(f"{GROUPED_ENDPOINT}?limit=5")
        data = response.json()
        
        charge_job_info = data.get('charge_job_enhancement', {})
        print(f"🏗️ Charge Job Integration:")
        print(f"   ✅ Enabled: {charge_job_info.get('enabled', False)}")
        print(f"   📊 Records Enhanced: {charge_job_info.get('records_enhanced', 0)}")
        print(f"   📡 Source: {charge_job_info.get('source', 'N/A')}")
        
        if data.get('data'):
            # Check charge job data completeness
            employees_with_charge_data = 0
            total_employees = len(data['data'])
            
            for emp in data['data']:
                identity = emp.get('identitas_karyawan', {})
                if (identity.get('task_code') and 
                    identity.get('station_code') and 
                    identity.get('machine_code') and 
                    identity.get('expense_code')):
                    employees_with_charge_data += 1
            
            charge_coverage = (employees_with_charge_data / total_employees * 100) if total_employees > 0 else 0
            print(f"   📊 Charge Data Coverage: {employees_with_charge_data}/{total_employees} ({charge_coverage:.1f}%)")
            
            if employees_with_charge_data > 0:
                sample_emp = next(emp for emp in data['data'] 
                                if emp['identitas_karyawan'].get('task_code'))
                identity = sample_emp['identitas_karyawan']
                
                print(f"\n📋 Sample Charge Job Data:")
                print(f"   🏗️ Task: {identity.get('task_code', 'N/A')}")
                print(f"   🏭 Station: {identity.get('station_code', 'N/A')}")
                print(f"   ⚙️ Machine: {identity.get('machine_code', 'N/A')}")
                print(f"   💰 Expense: {identity.get('expense_code', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Charge Job Integration Test Failed: {str(e)}")

def main():
    """Run all tests."""
    print("🚀 Enhanced Staging API Test Suite")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run all tests
    test_legacy_api()
    test_grouped_api()
    test_api_comparison()
    test_filtering_and_pagination()
    test_charge_job_integration()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Suite Completed in {duration:.2f} seconds")
    print("✅ All tests executed successfully!")
    print("\n📚 For detailed API documentation, see: API_DOCUMENTATION_STAGING_ENHANCED.md")

if __name__ == "__main__":
    main() 