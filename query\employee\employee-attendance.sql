/*
Employee Attendance Data Query
Purpose: Retrieve attendance records with employee information for reporting
Database: VenusHR14
Tables: HR_T_TAMachine_Summary, HR_M_EmployeePI
Parameters: @start_date, @end_date, @bus_code (optional)
*/

SELECT 
    emp.EmployeeID,
    emp.EmployeeName,
    ta.TADate,
    ta.TACheckIn,
    ta.TACheckOut,
    ta.Shift,
    ta.BusCode,
    ta.UserDeviceID,
    ta.UserDeviceName,
    ta.IsCrossDay,
    -- Calculate working hours
    CASE 
        WHEN ta.TACheckIn IS NOT NULL AND ta.TACheckOut IS NOT NULL
        THEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0
        ELSE 0
    END as TotalHours,
    -- Calculate regular hours (max 8 hours on weekdays, 5 on Saturday)
    CASE 
        WHEN ta.TACheckIn IS NOT NULL AND ta.TACheckOut IS NOT NULL
        THEN CASE 
            WHEN DATEPART(WEEKDAY, ta.TADate) = 7 -- Saturday
            THEN CASE 
                WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 5
                THEN 5.0
                ELSE DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0
            END
            ELSE CASE 
                WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 8
                THEN 8.0
                ELSE DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0
            END
        END
        ELSE 0
    END as RegularHours,
    -- Calculate overtime hours
    CASE 
        WHEN ta.TACheckIn IS NOT NULL AND ta.TACheckOut IS NOT NULL
        THEN CASE 
            WHEN DATEPART(WEEKDAY, ta.TADate) = 7 -- Saturday
            THEN CASE 
                WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 5
                THEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 - 5.0
                ELSE 0
            END
            ELSE CASE 
                WHEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 > 8
                THEN DATEDIFF(MINUTE, ta.TACheckIn, ta.TACheckOut) / 60.0 - 8.0
                ELSE 0
            END
        END
        ELSE 0
    END as OvertimeHours,
    -- Day of week in Indonesian
    CASE DATEPART(WEEKDAY, ta.TADate)
        WHEN 1 THEN 'Minggu'
        WHEN 2 THEN 'Senin'
        WHEN 3 THEN 'Selasa'
        WHEN 4 THEN 'Rabu'
        WHEN 5 THEN 'Kamis'
        WHEN 6 THEN 'Jumat'
        WHEN 7 THEN 'Sabtu'
    END as DayOfWeek
FROM [VenusHR14].[dbo].[HR_T_TAMachine_Summary] ta
INNER JOIN [VenusHR14].[dbo].[HR_M_EmployeePI] emp 
    ON ta.EmployeeID = emp.EmployeeID
WHERE ta.TADate >= @start_date 
    AND ta.TADate <= @end_date
    AND (@bus_code IS NULL OR ta.BusCode = @bus_code)
ORDER BY ta.TADate DESC, emp.EmployeeName 