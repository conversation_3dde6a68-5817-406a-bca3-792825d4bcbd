#!/usr/bin/env python3
"""
VenusHR14 Attendance Report Web Application - Main Entry Point

This is the main entry point for the refactored attendance report system.
The application has been modularized following SOLID principles.

Usage:
    python web_app.py                # Standard mode with database scanning
    python web_app.py --local        # Fast boot with local database
    python web_app.py --remote       # Fast boot with remote database
    python web_app.py -db local      # Alternative syntax for local database
    python web_app.py -db remote     # Alternative syntax for remote database
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_factory import create_app, parse_command_line_args

def main():
    """
    Main application entry point.
    """
    try:
        # Parse command-line arguments
        db_mode = parse_command_line_args()
        
        # Create the Flask application
        app = create_app()
        
        # Set database mode if specified
        if db_mode:
            print(f"\n🚀 Fast Boot Mode: {db_mode.upper()} Database")
            app.config['DB_MODE'] = db_mode
        else:
            print("\n🔍 Standard Mode: Database scanning enabled")
        
        # Run the application
        print("\n" + "="*50)
        print("🌟 VenusHR14 Attendance Report System")
        print("📊 Modular Architecture - SOLID Principles")
        print("🔗 http://localhost:5000")
        print("="*50 + "\n")
        
        # Start the Flask development server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # Disable reloader to prevent double initialization
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()