"""
Database interfaces for Attendance Report System.
Defines abstract base classes for database operations.
Implements Dependency Inversion Principle.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import sqlite3
import pyodbc


class IDatabaseConnection(ABC):
    """Abstract interface for database connections."""
    
    @abstractmethod
    def get_connection(self, force_mode: Optional[str] = None):
        """Get database connection."""
        pass
    
    @abstractmethod
    def test_connection(self, mode: str = "local") -> Tuple[bool, str]:
        """Test database connection."""
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        pass
    
    @abstractmethod
    def execute_non_query(self, query: str, params: tuple = ()) -> int:
        """Execute a non-query (INSERT, UPDATE, DELETE) and return affected rows."""
        pass
    
    @abstractmethod
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        pass


class IStagingRepository(ABC):
    """Abstract interface for staging database operations."""
    
    @abstractmethod
    def get_connection(self) -> sqlite3.Connection:
        """Get staging database connection."""
        pass
    
    @abstractmethod
    def initialize_database(self) -> bool:
        """Initialize staging database tables."""
        pass
    
    @abstractmethod
    def get_staging_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get staging data with filters."""
        pass
    
    @abstractmethod
    def add_staging_record(self, record: Dict[str, Any]) -> str:
        """Add a record to staging."""
        pass
    
    @abstractmethod
    def update_staging_record(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a staging record."""
        pass
    
    @abstractmethod
    def delete_staging_record(self, record_id: str) -> bool:
        """Delete a staging record."""
        pass
    
    @abstractmethod
    def get_staging_statistics(self) -> Dict[str, Any]:
        """Get staging database statistics."""
        pass
    
    @abstractmethod
    def cleanup_duplicates(self) -> Dict[str, Any]:
        """Clean up duplicate records."""
        pass
    
    @abstractmethod
    def check_health(self) -> bool:
        """Check staging database health."""
        pass


class IAttendanceRepository(ABC):
    """Abstract interface for attendance data operations."""
    
    @abstractmethod
    def get_attendance_data(self, start_date: str, end_date: str, 
                           bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get attendance data for date range."""
        pass
    
    @abstractmethod
    def get_employees_list(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of employees."""
        pass
    
    @abstractmethod
    def get_shifts_list(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of shifts."""
        pass
    
    @abstractmethod
    def get_monthly_attendance_grid(self, year: int, month: int, 
                                   bus_code: Optional[str] = None) -> Dict[str, Any]:
        """Get monthly attendance in grid format."""
        pass
    
    @abstractmethod
    def get_available_months(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available months with data."""
        pass
    
    @abstractmethod
    def get_leave_data(self, start_date: str, end_date: str, 
                      bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get employee leave data."""
        pass


class IConnectionManager(ABC):
    """Abstract interface for database connection management."""
    
    @abstractmethod
    def initialize_connections(self) -> bool:
        """Initialize database connections."""
        pass
    
    @abstractmethod
    def switch_connection_mode(self, new_mode: str) -> Tuple[bool, str]:
        """Switch database connection mode."""
        pass
    
    @abstractmethod
    def perform_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        pass
    
    @abstractmethod
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        pass
    
    @abstractmethod
    def update_database_config(self, new_config: Dict[str, Any]) -> Tuple[bool, str]:
        """Update database configuration."""
        pass


class IDataProcessor(ABC):
    """Abstract interface for data processing operations."""
    
    @abstractmethod
    def parse_charge_job_data(self, charge_job_string: str) -> Dict[str, str]:
        """Parse charge job data string."""
        pass
    
    @abstractmethod
    def enhance_attendance_data(self, attendance_data: List[Dict[str, Any]], 
                               charge_job_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhance attendance data with charge job information."""
        pass
    
    @abstractmethod
    def validate_attendance_record(self, record: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate attendance record data."""
        pass
    
    @abstractmethod
    def format_attendance_for_export(self, data: List[Dict[str, Any]], 
                                    format_type: str) -> Any:
        """Format attendance data for export."""
        pass


class IExternalApiClient(ABC):
    """Abstract interface for external API operations."""
    
    @abstractmethod
    def fetch_charge_job_data(self) -> Tuple[bool, Dict[str, Any]]:
        """Fetch charge job data from external API."""
        pass
    
    @abstractmethod
    def sync_attendance_data(self, data: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """Sync attendance data to external system."""
        pass
    
    @abstractmethod
    def test_api_connectivity(self) -> Tuple[bool, str]:
        """Test external API connectivity."""
        pass


class ICacheManager(ABC):
    """Abstract interface for caching operations."""
    
    @abstractmethod
    def get(self, key: str) -> Any:
        """Get cached value."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """Set cached value."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete cached value."""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all cached values."""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        pass
