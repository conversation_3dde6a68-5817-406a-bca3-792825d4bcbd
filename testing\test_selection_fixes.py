#!/usr/bin/env python3
"""
Test script to verify the attendance selection functionality and employee list synchronization fixes.
"""

import requests
import json
import sys
from datetime import datetime, timedelta

def test_attendance_selection_fixes():
    """Test both row selection functionality and employee list synchronization."""
    base_url = "http://localhost:5000"
    
    print("Testing Attendance Selection Functionality and Employee List Synchronization")
    print("=" * 80)
    
    # Test 1: Check staging employees API with exclusion filter
    print("1. Testing staging employees API with exclusion filter...")
    try:
        response = requests.get(f"{base_url}/api/staging/employees", params={'bus_code': 'PTRJ'})
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ Staging employees API working")
                print(f"   📊 Total employees before filtering: {data.get('total_employees_before_filtering', 'N/A')}")
                print(f"   📊 Total employees after filtering: {data['total_employees']}")
                print(f"   🔍 Filtering applied: {data.get('filtering_applied', 'Unknown')}")
                
                if data.get('total_employees_before_filtering', 0) > data['total_employees']:
                    filtered_count = data['total_employees_before_filtering'] - data['total_employees']
                    print(f"   ✅ Employee exclusion filter is working (filtered out {filtered_count} employees)")
                else:
                    print(f"   ⚠️ No employees filtered out (exclusion list may be empty or no inactive employees)")
                
                # Show sample employees
                if data['employees']:
                    print(f"   📝 Sample employees:")
                    for emp in data['employees'][:3]:
                        print(f"      - {emp['display_name']}")
                    if len(data['employees']) > 3:
                        print(f"      ... and {len(data['employees']) - 3} more")
            else:
                print(f"   ❌ API returned error: {data['error']}")
        else:
            print(f"   ❌ API request failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing staging employees API: {e}")
    
    # Test 2: Compare main attendance employees with staging employees
    print("\n2. Testing employee list synchronization...")
    try:
        # Get main attendance employees
        main_response = requests.get(f"{base_url}/api/employees", params={'bus_code': 'PTRJ'})
        staging_response = requests.get(f"{base_url}/api/staging/employees", params={'bus_code': 'PTRJ'})
        
        if main_response.status_code == 200 and staging_response.status_code == 200:
            main_data = main_response.json()
            staging_data = staging_response.json()
            
            if main_data['success'] and staging_data['success']:
                main_employees = {emp['EmployeeID']: emp['EmployeeName'] for emp in main_data['data']}
                staging_employees = {emp['employee_id']: emp['employee_name'] for emp in staging_data['employees']}
                
                print(f"   📊 Main attendance employees: {len(main_employees)}")
                print(f"   📊 Staging employees: {len(staging_employees)}")
                
                # Check for overlap
                common_employees = set(main_employees.keys()) & set(staging_employees.keys())
                main_only = set(main_employees.keys()) - set(staging_employees.keys())
                staging_only = set(staging_employees.keys()) - set(main_employees.keys())
                
                print(f"   🔗 Common employees: {len(common_employees)}")
                print(f"   📋 Main only: {len(main_only)}")
                print(f"   📋 Staging only: {len(staging_only)}")
                
                if len(common_employees) == len(main_employees) == len(staging_employees):
                    print(f"   ✅ Perfect synchronization: All employee lists match")
                elif len(common_employees) > 0:
                    similarity = len(common_employees) / max(len(main_employees), len(staging_employees)) * 100
                    print(f"   ✅ Good synchronization: {similarity:.1f}% overlap")
                else:
                    print(f"   ❌ Poor synchronization: No common employees found")
            else:
                print(f"   ❌ API errors - Main: {main_data.get('error', 'OK')}, Staging: {staging_data.get('error', 'OK')}")
        else:
            print(f"   ❌ API requests failed - Main: {main_response.status_code}, Staging: {staging_response.status_code}")
    except Exception as e:
        print(f"   ❌ Error testing synchronization: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 FIXES IMPLEMENTED:")
    print("1. ✅ Employee exclusion filter added to staging API")
    print("2. ✅ Bus code parameter synchronization implemented")
    print("3. ✅ JavaScript event delegation for row selection")
    print("4. ✅ DataTable conflict prevention")
    print("\n🚀 Both issues should now be resolved!")

if __name__ == "__main__":
    test_attendance_selection_fixes() 