# Monthly Attendance Report Feature - User Guide

## Overview

Fitur Monthly Attendance Report memungkinkan Anda untuk melihat semua data kehadiran yang dikelompokkan per bulan. Anda dapat mengklik bulan tertentu (seperti Mei) untuk melihat laporan lengkap bulan tersebut.

## 🎯 Fitur Utama

### 1. **Tampilan Bulanan Otomatis**
- Sistem secara otomatis menampilkan semua bulan yang memiliki data kehadiran
- Setiap bulan ditampilkan dalam bentuk kartu yang dapat diklik
- Menampilkan jumlah record dan karyawan per bulan

### 2. **Klik untuk Melihat Laporan**
- Klik pada kartu bulan (contoh: "Mei 2025") untuk melihat laporan bulan tersebut
- Laporan akan menampilkan semua data kehadiran untuk bulan yang dipilih
- Statistik ringkasan bulan akan ditampilkan di bagian atas

### 3. **Filter Business Code**
- Dapat memfilter berdasarkan kode bisnis (contoh: PTRJ)
- Filter akan mempengaruhi bulan-bulan yang ditampilkan

## 📋 Cara Menggunakan

### Langkah 1: Akses Tab Monthly Reports
1. Buka web application: `http://localhost:5000`
2. Klik tab **"Monthly Reports"** (tab pertama, sudah aktif secara default)

### Langkah 2: Load Available Months
1. **Opsional**: Masukkan Business Code (contoh: PTRJ) di field "Business Code"
2. Klik tombol **"Load Available Months"**
3. Sistem akan menampilkan semua bulan yang memiliki data kehadiran

### Langkah 3: Pilih Bulan
1. Lihat kartu-kartu bulan yang tersedia
2. **Klik pada bulan yang ingin Anda lihat** (contoh: klik "May 2025")
3. Kartu bulan akan ter-highlight menunjukkan bulan yang dipilih

### Langkah 4: Lihat Laporan Bulanan
1. Setelah mengklik bulan, sistem akan:
   - Menampilkan ringkasan statistik bulan tersebut
   - Memuat semua data kehadiran untuk bulan yang dipilih
   - Menampilkan data dalam tabel interaktif

### Langkah 5: Export Laporan (Opsional)
1. Setelah memilih bulan, klik tombol **"Export"** di bagian ringkasan
2. File Excel akan diunduh dengan data kehadiran bulan tersebut

## 🎨 Tampilan Interface

### Kartu Bulan
Setiap bulan ditampilkan dalam kartu yang berisi:
- **Nama Bulan dan Tahun** (contoh: "May 2025")
- **Jumlah Records** (badge berwarna)
- **Jumlah Karyawan**
- **Rentang Tanggal** (tanggal pertama - tanggal terakhir)

### Ringkasan Bulanan
Setelah memilih bulan, akan ditampilkan:
- **Employees**: Total karyawan unik
- **Records**: Total record kehadiran
- **Working Days**: Jumlah hari kerja
- **Regular Hours**: Total jam kerja reguler
- **Overtime Hours**: Total jam lembur
- **Export Button**: Tombol untuk mengunduh laporan

### Tabel Data
Tabel interaktif yang menampilkan:
- Employee ID dan Name
- Date dan Day of Week
- Shift
- Check In/Out times
- Regular Hours (dengan business rules)
- Overtime Hours
- Total Hours

## 🔧 API Endpoints yang Digunakan

### 1. `/api/months`
- **Fungsi**: Mengambil daftar bulan yang tersedia
- **Parameter**: `bus_code` (opsional)
- **Response**: List bulan dengan informasi record count dan employee count

### 2. `/api/monthly-report`
- **Fungsi**: Mengambil laporan detail untuk bulan tertentu
- **Parameter**: `year`, `month`, `bus_code` (opsional)
- **Response**: Summary statistik + data kehadiran detail

### 3. `/api/export`
- **Fungsi**: Export laporan ke Excel
- **Parameter**: `year`, `month`, `bus_code` (opsional)
- **Response**: File Excel untuk download

## 💡 Tips Penggunaan

### 1. **Filter Business Code**
- Masukkan kode bisnis (contoh: PTRJ) untuk melihat hanya data dari business unit tertentu
- Kosongkan field untuk melihat semua data

### 2. **Navigasi Cepat**
- Gunakan tab "Monthly Reports" untuk navigasi per bulan
- Gunakan tab "Custom Date Range" untuk filter tanggal spesifik

### 3. **Export Data**
- Export dari Monthly Reports akan mengunduh data bulan penuh
- Export dari Custom Date Range akan mengunduh data sesuai filter tanggal

### 4. **Visual Feedback**
- Kartu bulan yang dipilih akan ter-highlight dengan border biru
- Loading indicator akan muncul saat memuat data
- Notifikasi sukses/error akan muncul di bagian atas

## 🚀 Contoh Penggunaan

### Skenario: Melihat Laporan Kehadiran Mei 2025
1. Buka web application
2. Pastikan tab "Monthly Reports" aktif
3. Masukkan "PTRJ" di Business Code (jika diperlukan)
4. Klik "Load Available Months"
5. Cari dan klik kartu "May 2025"
6. Lihat ringkasan dan data detail yang muncul
7. Klik "Export" untuk mengunduh laporan Excel

### Skenario: Membandingkan Beberapa Bulan
1. Klik bulan pertama (contoh: "April 2025")
2. Catat statistik yang ditampilkan
3. Klik bulan kedua (contoh: "May 2025")
4. Bandingkan statistik antara kedua bulan

## 🔍 Troubleshooting

### Tidak Ada Bulan yang Muncul
- Pastikan ada data kehadiran di database
- Cek koneksi database
- Periksa Business Code yang dimasukkan

### Error Saat Load Monthly Report
- Pastikan bulan yang diklik valid
- Cek koneksi internet
- Refresh halaman dan coba lagi

### Export Tidak Berfungsi
- Pastikan sudah memilih bulan terlebih dahulu
- Cek browser settings untuk download
- Pastikan tidak ada popup blocker yang aktif

## 📊 Business Logic

Sistem menggunakan business logic yang sama seperti fitur lainnya:
- **Weekdays**: Maksimal 7 jam kerja reguler
- **Saturday**: Maksimal 5 jam kerja reguler
- **Overtime**: Dihitung terpisah dari tabel HR_T_Overtime
- **Employee Names**: Diambil dari tabel HR_M_EmployeePI

Fitur Monthly Reports memberikan cara yang mudah dan intuitif untuk menjelajahi data kehadiran berdasarkan bulan, sesuai dengan permintaan Anda untuk mengelompokkan data per bulan dan mengklik bulan tertentu untuk melihat laporannya.
