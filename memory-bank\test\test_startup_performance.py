#!/usr/bin/env python3
"""
Test script to measure application startup performance
and verify that database connections are deferred.
"""

import time
import subprocess
import sys
import requests
import threading
import signal
import os

def test_startup_performance():
    """Test application startup time and verify quick startup."""
    print("=" * 60)
    print("TESTING APPLICATION STARTUP PERFORMANCE")
    print("=" * 60)
    
    # Record start time
    start_time = time.time()
    
    print("🚀 Starting application...")
    
    # Start the application
    process = subprocess.Popen([
        sys.executable, 'web_app.py'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # Wait a bit for startup
    startup_timeout = 10  # seconds
    
    try:
        # Check if process starts quickly
        for i in range(startup_timeout):
            if process.poll() is not None:
                print(f"❌ Application exited during startup with code: {process.returncode}")
                stdout, stderr = process.communicate()
                print("STDOUT:", stdout)
                print("STDERR:", stderr)
                return False
            
            try:
                # Try to connect to the web interface
                response = requests.get('http://localhost:5173/', timeout=2)
                if response.status_code == 200:
                    startup_duration = time.time() - start_time
                    print(f"✅ Application started successfully in {startup_duration:.2f} seconds!")
                    
                    # Test database management API
                    print("🔧 Testing database management API...")
                    try:
                        db_response = requests.get('http://localhost:5173/api/database/config', timeout=5)
                        if db_response.status_code == 200:
                            print("✅ Database management API is accessible")
                            config_data = db_response.json()
                            print(f"📊 Current connection mode: {config_data.get('config', {}).get('connection_mode', 'unknown')}")
                        else:
                            print(f"⚠️ Database management API returned status: {db_response.status_code}")
                    except Exception as api_error:
                        print(f"⚠️ Database management API test failed: {api_error}")
                    
                    # Test the connection testing endpoint
                    print("🧪 Testing connection test API...")
                    try:
                        test_response = requests.post('http://localhost:5173/api/database/test-connection', 
                                                    json={'mode': 'local'}, timeout=10)
                        if test_response.status_code == 200:
                            print("✅ Connection testing API is functional")
                            test_data = test_response.json()
                            print(f"📊 Test result: {test_data.get('success', 'unknown')}")
                        else:
                            print(f"⚠️ Connection testing API returned status: {test_response.status_code}")
                    except Exception as test_error:
                        print(f"⚠️ Connection testing API failed: {test_error}")
                    
                    return True
                    
            except requests.exceptions.RequestException:
                # Application not ready yet
                pass
            
            time.sleep(1)
            print(f"⏳ Waiting for startup... ({i+1}/{startup_timeout})")
        
        print(f"❌ Application did not start within {startup_timeout} seconds")
        return False
        
    finally:
        # Clean up
        print("🧹 Stopping application...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing application...")
            process.kill()
            process.wait()

def test_deferred_database_testing():
    """Verify that database connections are tested on-demand."""
    print("\n" + "=" * 60)
    print("TESTING DEFERRED DATABASE CONNECTION FUNCTIONALITY")
    print("=" * 60)
    
    try:
        # Test the "test all connections" endpoint
        print("🧪 Testing 'test all connections' endpoint...")
        response = requests.post('http://localhost:5173/api/database/test-all-connections', timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test all connections endpoint is working!")
            
            if result.get('success'):
                print("📊 Connection test results:")
                for mode, details in result.get('results', {}).items():
                    status = "✅ Connected" if details.get('success') else "❌ Failed"
                    print(f"  - {mode.title()}: {status} ({details.get('message', 'No message')})")
                
                print(f"💡 Suggestion: {result.get('suggestion', 'No suggestion')}")
            else:
                print(f"⚠️ Connection testing failed: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ Test all connections endpoint returned: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing deferred functionality: {e}")

if __name__ == "__main__":
    print("🔍 Application Startup Performance Test")
    print(f"📅 Test started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_startup_performance()
    
    if success:
        print("\n✅ STARTUP PERFORMANCE TEST PASSED!")
        print("🎯 Application starts quickly without database connection delays")
        
        # Test deferred functionality
        test_deferred_database_testing()
        
        print("\n🎉 ALL TESTS COMPLETED!")
        print("📝 Summary:")
        print("  ✅ Fast startup without automatic database testing")
        print("  ✅ Database connections can be tested on-demand")
        print("  ✅ Web interface is responsive")
        print("  ✅ API endpoints are functional")
        
    else:
        print("\n❌ STARTUP PERFORMANCE TEST FAILED!")
        print("⚠️ Application did not start within expected time")
        
    print("\n" + "=" * 60) 