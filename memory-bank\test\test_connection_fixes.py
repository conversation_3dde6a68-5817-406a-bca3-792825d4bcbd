"""
Test script to verify database connection fixes
Tests the API endpoints and connection handling
"""

import requests
import time
import sys
import json

BASE_URL = "http://localhost:5173"

def test_api_endpoints():
    """Test all database-related API endpoints"""
    print("🧪 Testing Database Connection API Endpoints")
    print("=" * 60)
    
    tests = [
        {
            'name': 'Database Status',
            'method': 'GET',
            'url': f'{BASE_URL}/api/database/status',
            'expected_keys': ['success', 'status']
        },
        {
            'name': 'Initial Scan',
            'method': 'POST',
            'url': f'{BASE_URL}/api/database/initial-scan',
            'data': {},
            'expected_keys': ['success', 'status', 'summary']
        },
        {
            'name': 'Test Local Connection',
            'method': 'POST',
            'url': f'{BASE_URL}/api/database/test-connection',
            'data': {'mode': 'local'},
            'expected_keys': ['success', 'results', 'status']
        },
        {
            'name': 'Test Remote Connection',
            'method': 'POST',
            'url': f'{BASE_URL}/api/database/test-connection',
            'data': {'mode': 'remote'},
            'expected_keys': ['success', 'results', 'status']
        },
        {
            'name': 'Test All Connections',
            'method': 'POST',
            'url': f'{BASE_URL}/api/database/test-connection',
            'data': {'mode': 'all'},
            'expected_keys': ['success', 'results', 'status']
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"\n📋 Testing: {test['name']}")
        try:
            if test['method'] == 'GET':
                response = requests.get(test['url'], timeout=10)
            else:
                response = requests.post(
                    test['url'], 
                    json=test.get('data', {}),
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check expected keys
                missing_keys = []
                for key in test['expected_keys']:
                    if key not in data:
                        missing_keys.append(key)
                
                if missing_keys:
                    print(f"  ❌ Missing keys: {missing_keys}")
                    results.append(False)
                else:
                    print(f"  ✅ Success - Response contains all expected keys")
                    if 'status' in data:
                        status = data['status']
                        print(f"    Current mode: {status.get('current_mode', 'unknown')}")
                        print(f"    Local: {'✅' if status.get('local_status', {}).get('connected') else '❌'}")
                        print(f"    Remote: {'✅' if status.get('remote_status', {}).get('connected') else '❌'}")
                    results.append(True)
            else:
                print(f"  ❌ HTTP {response.status_code}: {response.text}")
                results.append(False)
                
        except requests.exceptions.Timeout:
            print(f"  ❌ Request timeout")
            results.append(False)
        except requests.exceptions.ConnectionError:
            print(f"  ❌ Connection error - is the server running?")
            results.append(False)
        except Exception as e:
            print(f"  ❌ Error: {str(e)}")
            results.append(False)
    
    return results

def test_startup_performance():
    """Test startup performance"""
    print("\n⏱️ Testing Startup Performance")
    print("=" * 60)
    
    try:
        start_time = time.time()
        response = requests.get(f'{BASE_URL}/', timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            startup_time = end_time - start_time
            print(f"✅ Application responding in {startup_time:.2f} seconds")
            
            if startup_time < 5:
                print("  🚀 Fast startup achieved!")
                return True
            else:
                print("  ⚠️ Startup is slower than expected")
                return False
        else:
            print(f"❌ Application not responding (HTTP {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Startup test failed: {str(e)}")
        return False

def test_no_redirect_behavior():
    """Test that no automatic redirects occur"""
    print("\n🚫 Testing No Redirect Behavior")
    print("=" * 60)
    
    try:
        # Test main page doesn't redirect
        response = requests.get(f'{BASE_URL}/', timeout=10, allow_redirects=False)
        
        if response.status_code == 200:
            print("✅ Main page loads without redirect")
            return True
        elif 300 <= response.status_code < 400:
            print(f"❌ Automatic redirect detected (HTTP {response.status_code})")
            print(f"  Redirect location: {response.headers.get('Location', 'unknown')}")
            return False
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Redirect test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 DATABASE CONNECTION FIXES TEST SUITE")
    print("=" * 60)
    print(f"Testing server at: {BASE_URL}")
    
    # Wait for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f'{BASE_URL}/', timeout=5)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            if i == 9:
                print("❌ Server is not responding. Please start the server first.")
                sys.exit(1)
            time.sleep(2)
    
    # Run tests
    all_results = []
    
    all_results.append(test_startup_performance())
    all_results.append(test_no_redirect_behavior())
    all_results.extend(test_api_endpoints())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(all_results)
    total = len(all_results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Database connection fixes are working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 