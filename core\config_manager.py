"""
Configuration Manager for Attendance Report System.
Handles loading, validation, and management of application configuration.
Implements Single Responsibility Principle.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime


class ConfigurationManager:
    """
    Manages application configuration with validation and fallback mechanisms.
    Follows Single Responsibility Principle - only handles configuration.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path (str, optional): Path to configuration file
        """
        self.config_path = config_path or os.path.join(os.path.dirname(__file__), '..', 'config.json')
        self._config = None
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file with fallback to defaults."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
            
            # Ensure required sections exist
            self._ensure_config_structure()
            
        except FileNotFoundError:
            logging.warning("config.json file not found. Using default configuration.")
            self._config = self._get_default_config()
            
        except json.JSONDecodeError as e:
            logging.error(f"Error parsing config.json: {e}. Using default configuration.")
            self._config = self._get_default_config()
    
    def _ensure_config_structure(self) -> None:
        """Ensure all required configuration sections exist."""
        default_config = self._get_default_config()
        
        # Merge with defaults to ensure all sections exist
        for section, default_values in default_config.items():
            if section not in self._config:
                self._config[section] = default_values
            elif isinstance(default_values, dict):
                for key, value in default_values.items():
                    if key not in self._config[section]:
                        self._config[section][key] = value
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration structure."""
        return {
            "google_apps_script": {
                "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
                "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"
            },
            "database_config": {
                "connection_mode": "local",
                "fallback_enabled": True,
                "connection_timeout": 30,
                "local_database": {
                    "server": "localhost",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "windows0819",
                    "port": 1433,
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "remote_database": {
                    "server": "********",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "supp0rt@",
                    "port": 1888,
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            },
            "staging_config": {
                "database_table": "staging_attendance",
                "max_records": 10000,
                "auto_cleanup_days": 30,
                "default_mode": "Local Sync Staging"
            },
            "sync_settings": {
                "timeout_seconds": 15,
                "retry_attempts": 3,
                "batch_size": 1000
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "file_path": "logs/attendance_app.log",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            },
            "performance": {
                "enable_caching": True,
                "cache_timeout": 300,  # 5 minutes
                "connection_pool_size": 5,
                "query_timeout": 30
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key (str): Configuration key (supports dot notation, e.g., 'database_config.connection_mode')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                value = value[k]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value by key.
        
        Args:
            key (str): Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return self._config.get('database_config', {})
    
    def get_google_apps_script_config(self) -> Dict[str, Any]:
        """Get Google Apps Script configuration."""
        return self._config.get('google_apps_script', {})
    
    def get_staging_config(self) -> Dict[str, Any]:
        """Get staging configuration."""
        return self._config.get('staging_config', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self._config.get('logging', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration."""
        return self._config.get('performance', {})
    
    def save_config(self) -> bool:
        """
        Save current configuration to file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            return True
            
        except Exception as e:
            logging.error(f"Failed to save configuration: {str(e)}")
            return False
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate configuration structure and values.
        
        Returns:
            dict: Validation result with 'valid' boolean and 'errors' list
        """
        result = {'valid': True, 'errors': [], 'warnings': []}
        
        try:
            # Validate database configuration
            db_config = self.get_database_config()
            
            # Check required database fields
            required_db_fields = ['connection_mode', 'local_database', 'remote_database']
            for field in required_db_fields:
                if field not in db_config:
                    result['valid'] = False
                    result['errors'].append(f'Missing required database config field: {field}')
            
            # Validate database connection details
            for db_type in ['local_database', 'remote_database']:
                if db_type in db_config:
                    db_details = db_config[db_type]
                    required_fields = ['server', 'database', 'username', 'password', 'port']
                    
                    for field in required_fields:
                        if field not in db_details or not db_details[field]:
                            if field == 'password' and db_type == 'remote_database':
                                result['warnings'].append(f'Remote database password not configured')
                            else:
                                result['valid'] = False
                                result['errors'].append(f'Missing {db_type} field: {field}')
                    
                    # Validate port number
                    if 'port' in db_details:
                        port = db_details['port']
                        if not isinstance(port, int) or port < 1 or port > 65535:
                            result['valid'] = False
                            result['errors'].append(f'Invalid port number for {db_type}: {port}')
            
            # Validate Google Apps Script URLs
            gas_config = self.get_google_apps_script_config()
            for url_key in ['sync_url', 'charge_job_data_url']:
                if url_key in gas_config:
                    url = gas_config[url_key]
                    if not url or not url.startswith('https://'):
                        result['warnings'].append(f'Invalid or missing Google Apps Script URL: {url_key}')
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f'Configuration validation error: {str(e)}')
        
        return result
    
    def get_safe_config(self) -> Dict[str, Any]:
        """
        Get configuration without sensitive information.
        
        Returns:
            dict: Configuration with sensitive data masked
        """
        safe_config = json.loads(json.dumps(self._config))  # Deep copy
        
        # Mask sensitive information
        if 'database_config' in safe_config:
            for db_type in ['local_database', 'remote_database']:
                if db_type in safe_config['database_config']:
                    if 'password' in safe_config['database_config'][db_type]:
                        safe_config['database_config'][db_type]['password'] = '***masked***'
        
        return safe_config
    
    def update_database_config(self, new_config: Dict[str, Any]) -> bool:
        """
        Update database configuration and save to file.
        
        Args:
            new_config (dict): New database configuration
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update internal configuration
            db_config = self._config.get('database_config', {})
            
            for key, value in new_config.items():
                if key in ['connection_mode', 'fallback_enabled', 'connection_timeout']:
                    db_config[key] = value
                elif key in ['local_database', 'remote_database'] and isinstance(value, dict):
                    if key not in db_config:
                        db_config[key] = {}
                    db_config[key].update(value)
            
            self._config['database_config'] = db_config
            
            # Save to file
            return self.save_config()
            
        except Exception as e:
            logging.error(f"Failed to update database configuration: {str(e)}")
            return False
