"""
Attendance Service for Attendance Report System.
Handles all attendance-related business logic and operations.
Implements Single Responsibility and Dependency Inversion Principles.
"""

import calendar
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from interfaces.service_interfaces import IAttendanceService
from modules.attendance_reporter import AttendanceReporter


class AttendanceService(IAttendanceService):
    """
    Service for managing attendance operations.
    Implements business logic for attendance data management and reporting.
    """
    
    def __init__(self, database_manager, config_manager, logging_manager):
        """
        Initialize attendance service.
        
        Args:
            database_manager: Database manager instance
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.database_manager = database_manager
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)
        
        # Initialize attendance reporter with enhanced error handling
        self.reporter = None
        self._initialize_reporter()
        
        # Performance configuration
        self.performance_config = config_manager.get_performance_config()
        self.enable_caching = self.performance_config.get('enable_caching', True)
        self.cache_timeout = self.performance_config.get('cache_timeout', 300)
    
    def _initialize_reporter(self) -> None:
        """Initialize the attendance reporter with error handling."""
        try:
            # Create a connection-aware reporter
            self.reporter = EnhancedAttendanceReporter(self.database_manager)
            self.logger.info("✅ Enhanced AttendanceReporter initialized")
        except Exception as e:
            self.logger.warning(f"⚠️ Enhanced AttendanceReporter initialization warning: {e}")
            self.reporter = None
    
    def get_attendance_data(self, start_date: str, end_date: str, 
                           bus_code: Optional[str] = None, 
                           employee_id: Optional[str] = None,
                           shift: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get attendance data with filters and performance optimization.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            bus_code: Optional bus code filter
            employee_id: Optional employee ID filter
            shift: Optional shift filter
            
        Returns:
            List of attendance records
        """
        try:
            if not self.reporter:
                raise Exception("Attendance reporter not initialized")
            
            # Get raw attendance data
            start_time = datetime.now()
            data = self.reporter.get_attendance_data(start_date, end_date, bus_code)
            
            # Apply additional filters
            if employee_id:
                data = [record for record in data if record.get('EmployeeID') == employee_id]
            
            if shift:
                data = [record for record in data if record.get('Shift') == shift]
            
            # Log performance
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.info(f"⚡ Retrieved {len(data)} attendance records in {duration:.3f}s")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to get attendance data: {str(e)}")
            return []
    
    def get_monthly_attendance_grid(self, year: int, month: int, 
                                   bus_code: Optional[str] = None) -> Dict[str, Any]:
        """
        Get monthly attendance in grid format (employee vs days).
        
        Args:
            year: Year
            month: Month (1-12)
            bus_code: Optional bus code filter
            
        Returns:
            Dictionary with grid data and metadata
        """
        try:
            if not self.reporter:
                raise Exception("Attendance reporter not initialized")
            
            start_time = datetime.now()
            
            # Get monthly grid data
            grid_data = self.reporter.get_monthly_attendance_grid(year, month, bus_code)
            
            # Enhance with additional metadata
            days_in_month = calendar.monthrange(year, month)[1]
            month_name = calendar.month_name[month]
            
            enhanced_data = {
                'grid_data': grid_data,
                'metadata': {
                    'year': year,
                    'month': month,
                    'month_name': month_name,
                    'days_in_month': days_in_month,
                    'bus_code': bus_code,
                    'generated_at': datetime.now().isoformat()
                }
            }
            
            # Log performance
            duration = (datetime.now() - start_time).total_seconds()
            employee_count = len(grid_data) if isinstance(grid_data, list) else 0
            self.logger.info(f"⚡ Generated monthly grid for {employee_count} employees in {duration:.3f}s")
            
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"Failed to get monthly attendance grid: {str(e)}")
            return {
                'grid_data': [],
                'metadata': {
                    'year': year,
                    'month': month,
                    'error': str(e)
                }
            }
    
    def get_attendance_summary(self, start_date: str, end_date: str, 
                              bus_code: Optional[str] = None) -> Dict[str, Any]:
        """
        Get attendance summary statistics.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            bus_code: Optional bus code filter
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            # Get attendance data
            data = self.get_attendance_data(start_date, end_date, bus_code)
            
            if not data:
                return {
                    'total_records': 0,
                    'unique_employees': 0,
                    'date_range': {'start': start_date, 'end': end_date},
                    'summary': {}
                }
            
            # Calculate summary statistics
            unique_employees = len(set(record.get('EmployeeID', '') for record in data))
            total_regular_hours = sum(float(record.get('RegularHours', 0)) for record in data)
            total_overtime_hours = sum(float(record.get('OvertimeHours', 0)) for record in data)
            total_hours = sum(float(record.get('TotalHours', 0)) for record in data)
            
            # Group by shift
            shift_summary = {}
            for record in data:
                shift = record.get('Shift', 'Unknown')
                if shift not in shift_summary:
                    shift_summary[shift] = {'count': 0, 'total_hours': 0}
                shift_summary[shift]['count'] += 1
                shift_summary[shift]['total_hours'] += float(record.get('TotalHours', 0))
            
            # Group by date
            date_summary = {}
            for record in data:
                date = record.get('TADate', 'Unknown')
                if date not in date_summary:
                    date_summary[date] = {'count': 0, 'total_hours': 0}
                date_summary[date]['count'] += 1
                date_summary[date]['total_hours'] += float(record.get('TotalHours', 0))
            
            return {
                'total_records': len(data),
                'unique_employees': unique_employees,
                'date_range': {'start': start_date, 'end': end_date},
                'hours_summary': {
                    'total_regular_hours': round(total_regular_hours, 2),
                    'total_overtime_hours': round(total_overtime_hours, 2),
                    'total_hours': round(total_hours, 2),
                    'average_hours_per_record': round(total_hours / len(data), 2) if data else 0
                },
                'shift_breakdown': shift_summary,
                'daily_breakdown': date_summary,
                'bus_code': bus_code,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get attendance summary: {str(e)}")
            return {
                'total_records': 0,
                'unique_employees': 0,
                'error': str(e)
            }
    
    def export_attendance_report(self, start_date: str, end_date: str, 
                                format_type: str, bus_code: Optional[str] = None) -> Any:
        """
        Export attendance report in specified format.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            format_type: Export format ('excel', 'json', 'csv')
            bus_code: Optional bus code filter
            
        Returns:
            Export data or file path
        """
        try:
            if not self.reporter:
                raise Exception("Attendance reporter not initialized")
            
            # Get attendance data
            data = self.get_attendance_data(start_date, end_date, bus_code)
            
            if not data:
                raise Exception("No data available for export")
            
            # Use the reporter's export functionality
            export_result = self.reporter.export_to_excel(data, format_type)
            
            self.logger.info(f"✅ Exported {len(data)} records to {format_type} format")
            
            return export_result
            
        except Exception as e:
            self.logger.error(f"Failed to export attendance report: {str(e)}")
            raise
    
    def get_available_months(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available months with data.
        
        Args:
            bus_code: Optional bus code filter
            
        Returns:
            List of available months with metadata
        """
        try:
            if not self.reporter:
                raise Exception("Attendance reporter not initialized")
            
            # Get available months from reporter
            months = self.reporter.get_available_months(bus_code)
            
            # Enhance with additional metadata
            enhanced_months = []
            for month_data in months:
                enhanced_month = {
                    **month_data,
                    'month_name': calendar.month_name[month_data.get('month', 1)],
                    'days_in_month': calendar.monthrange(
                        month_data.get('year', 2025), 
                        month_data.get('month', 1)
                    )[1]
                }
                enhanced_months.append(enhanced_month)
            
            return enhanced_months
            
        except Exception as e:
            self.logger.error(f"Failed to get available months: {str(e)}")
            return []


class EnhancedAttendanceReporter:
    """
    Enhanced wrapper for AttendanceReporter with database connection management.
    Provides robust error handling and automatic fallback functionality.
    """
    
    def __init__(self, database_manager):
        """
        Initialize enhanced attendance reporter.
        
        Args:
            database_manager: Database manager instance
        """
        self.database_manager = database_manager
        self.reporter = None
        self.last_connection_attempt = None
        self.connection_errors = []
        self.max_retry_attempts = 3
        
        self._initialize_reporter()
    
    def _initialize_reporter(self) -> None:
        """Initialize the attendance reporter with connection management."""
        try:
            # Get database connection
            connection = self.database_manager.get_connection()
            if connection:
                # Create reporter with connection
                self.reporter = AttendanceReporter(connection)
                self.last_connection_attempt = datetime.now()
            else:
                raise Exception("No database connection available")
                
        except Exception as e:
            self.connection_errors.append(str(e))
            self.reporter = None
    
    def _ensure_connection(self) -> bool:
        """Ensure we have a working database connection."""
        if self.reporter is None:
            self._initialize_reporter()
        return self.reporter is not None
    
    def get_attendance_data(self, start_date: str, end_date: str, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get attendance data with connection management."""
        if not self._ensure_connection():
            raise Exception("No database connection available")
        
        return self.reporter.get_attendance_data(start_date, end_date, bus_code)
    
    def get_monthly_attendance_grid(self, year: int, month: int, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get monthly attendance grid with connection management."""
        if not self._ensure_connection():
            raise Exception("No database connection available")
        
        return self.reporter.get_monthly_attendance_grid(year, month, bus_code)
    
    def get_available_months(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get available months with connection management."""
        if not self._ensure_connection():
            raise Exception("No database connection available")
        
        return self.reporter.get_available_months(bus_code)
    
    def export_to_excel(self, data: List[Dict[str, Any]], format_type: str) -> Any:
        """Export data with connection management."""
        if not self._ensure_connection():
            raise Exception("No database connection available")
        
        return self.reporter.export_to_excel(data, format_type)
