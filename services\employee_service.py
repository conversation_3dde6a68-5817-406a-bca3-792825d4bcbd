"""
Employee Service for Attendance Report System.
Handles all employee-related business logic and operations.
Implements Single Responsibility and Dependency Inversion Principles.
"""

import requests
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from interfaces.service_interfaces import IEmployeeService


class EmployeeService(IEmployeeService):
    """
    Service for managing employee operations.
    Implements business logic for employee data management and charge job integration.
    """
    
    def __init__(self, database_manager, config_manager, logging_manager):
        """
        Initialize employee service.
        
        Args:
            database_manager: Database manager instance
            config_manager: Configuration manager instance
            logging_manager: Logging manager instance
        """
        self.database_manager = database_manager
        self.config_manager = config_manager
        self.logger = logging_manager.get_logger(__name__)
        
        # Get Google Apps Script configuration
        self.gas_config = config_manager.get_google_apps_script_config()
        self.charge_job_data_url = self.gas_config.get('charge_job_data_url', '')
        self.sync_url = self.gas_config.get('sync_url', '')
        
        # Get sync settings
        self.sync_settings = config_manager.get('sync_settings', {})
        self.timeout_seconds = self.sync_settings.get('timeout_seconds', 15)
        self.retry_attempts = self.sync_settings.get('retry_attempts', 3)
        
        # Cache for charge job data
        self._charge_job_cache = None
        self._cache_timestamp = None
        self._cache_timeout = 300  # 5 minutes
    
    def get_employees_list(self, bus_code: Optional[str] = None, 
                          filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get list of employees with optional filters.
        
        Args:
            bus_code: Optional bus code filter
            filters: Optional additional filters
            
        Returns:
            List of employee records
        """
        try:
            # Build query to get employees from attendance data
            query = """
                SELECT DISTINCT EmployeeID, EmployeeName, BusCode
                FROM HR_T_TAMachine_Summary 
                WHERE 1=1
            """
            params = []
            
            if bus_code:
                query += " AND BusCode = ?"
                params.append(bus_code)
            
            # Apply additional filters if provided
            if filters:
                if filters.get('employee_name_pattern'):
                    query += " AND EmployeeName LIKE ?"
                    params.append(f"%{filters['employee_name_pattern']}%")
                
                if filters.get('exclude_inactive', True):
                    # Add logic to exclude inactive employees if needed
                    pass
            
            query += " ORDER BY EmployeeName"
            
            # Execute query
            employees = self.database_manager.execute_query(query, tuple(params))
            
            self.logger.info(f"Retrieved {len(employees)} employees")
            
            return employees
            
        except Exception as e:
            self.logger.error(f"Failed to get employees list: {str(e)}")
            return []
    
    def get_employee_charge_jobs(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Get employee charge job data from external API with caching.
        
        Returns:
            Tuple of (success, charge_job_data)
        """
        try:
            # Check cache first
            if self._is_cache_valid():
                self.logger.info("Using cached charge job data")
                return True, self._charge_job_cache
            
            # Fetch from external API
            self.logger.info("Fetching charge job data from Google Apps Script API")
            
            response = requests.get(self.charge_job_data_url, timeout=self.timeout_seconds)
            response.raise_for_status()
            
            charge_jobs_response = response.json()
            self.logger.info("Retrieved charge jobs response from Google Apps Script")
            
            # Process the response
            processed_data = self._process_charge_job_response(charge_jobs_response)
            
            # Update cache
            self._charge_job_cache = processed_data
            self._cache_timestamp = datetime.now()
            
            self.logger.info(f"Successfully processed {len(processed_data)} charge job records")
            
            return True, processed_data
            
        except requests.exceptions.Timeout:
            self.logger.warning(f"Charge job API request timed out after {self.timeout_seconds}s")
            return False, self._get_fallback_charge_job_data()
            
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"Failed to fetch charge job data from API: {str(e)}")
            return False, self._get_fallback_charge_job_data()
            
        except Exception as e:
            self.logger.error(f"Unexpected error fetching charge job data: {str(e)}")
            return False, self._get_fallback_charge_job_data()
    
    def get_shifts_list(self, bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available shifts.
        
        Args:
            bus_code: Optional bus code filter
            
        Returns:
            List of shift records
        """
        try:
            query = """
                SELECT DISTINCT Shift, COUNT(*) as usage_count
                FROM HR_T_TAMachine_Summary 
                WHERE Shift IS NOT NULL AND Shift != ''
            """
            params = []
            
            if bus_code:
                query += " AND BusCode = ?"
                params.append(bus_code)
            
            query += " GROUP BY Shift ORDER BY usage_count DESC, Shift"
            
            shifts = self.database_manager.execute_query(query, tuple(params))
            
            self.logger.info(f"Retrieved {len(shifts)} shifts")
            
            return shifts
            
        except Exception as e:
            self.logger.error(f"Failed to get shifts list: {str(e)}")
            return []
    
    def get_leave_data(self, start_date: str, end_date: str, 
                      bus_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get employee leave data.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            bus_code: Optional bus code filter
            
        Returns:
            List of leave records
        """
        try:
            query = """
                SELECT EmployeeID, EmployeeName, TADate, 
                       LeaveTypeCode, LeaveTypeDescription, LeaveRefNumber,
                       IsOnLeave, BusCode
                FROM HR_T_TAMachine_Summary 
                WHERE TADate BETWEEN ? AND ?
                  AND (IsOnLeave = 1 OR LeaveTypeCode IS NOT NULL)
            """
            params = [start_date, end_date]
            
            if bus_code:
                query += " AND BusCode = ?"
                params.append(bus_code)
            
            query += " ORDER BY TADate, EmployeeName"
            
            leave_data = self.database_manager.execute_query(query, tuple(params))
            
            self.logger.info(f"Retrieved {len(leave_data)} leave records")
            
            return leave_data
            
        except Exception as e:
            self.logger.error(f"Failed to get leave data: {str(e)}")
            return []
    
    def enhance_employee_data_with_charge_jobs(self, employee_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance employee data with charge job information.
        
        Args:
            employee_data: List of employee records
            
        Returns:
            Enhanced employee data
        """
        try:
            # Get charge job data
            success, charge_job_data = self.get_employee_charge_jobs()
            
            if not success or not charge_job_data:
                self.logger.warning("No charge job data available for enhancement")
                return employee_data
            
            enhanced_data = []
            
            for employee in employee_data:
                enhanced_employee = employee.copy()
                
                # Try to match employee with charge job data
                emp_id = employee.get('EmployeeID', '')
                emp_name = employee.get('EmployeeName', '')
                
                charge_data = self._find_charge_job_for_employee(emp_id, emp_name, charge_job_data)
                
                if charge_data:
                    enhanced_employee.update({
                        'TaskCode': charge_data.get('task_code', ''),
                        'StationCode': charge_data.get('station_code', ''),
                        'MachineCode': charge_data.get('machine_code', ''),
                        'ExpenseCode': charge_data.get('expense_code', ''),
                        'RawChargeJob': charge_data.get('raw_charge_job', ''),
                        'ChargeJobMatched': True
                    })
                else:
                    enhanced_employee['ChargeJobMatched'] = False
                
                enhanced_data.append(enhanced_employee)
            
            matched_count = sum(1 for emp in enhanced_data if emp.get('ChargeJobMatched', False))
            self.logger.info(f"Enhanced {len(enhanced_data)} employees, {matched_count} matched with charge jobs")
            
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"Failed to enhance employee data with charge jobs: {str(e)}")
            return employee_data
    
    def _is_cache_valid(self) -> bool:
        """Check if charge job cache is still valid."""
        if not self._charge_job_cache or not self._cache_timestamp:
            return False
        
        cache_age = (datetime.now() - self._cache_timestamp).total_seconds()
        return cache_age < self._cache_timeout
    
    def _process_charge_job_response(self, response_data: Any) -> Dict[str, Any]:
        """
        Process charge job response from external API.
        
        Args:
            response_data: Raw response data from API
            
        Returns:
            Processed charge job data
        """
        try:
            # Handle different response formats
            if isinstance(response_data, dict):
                if 'data' in response_data:
                    employee_list = response_data['data']
                elif 'employees' in response_data:
                    employee_list = response_data['employees']
                else:
                    employee_list = [response_data]
            else:
                employee_list = response_data
            
            processed_data = {}
            
            for emp in employee_list:
                emp_name = (emp.get('namaKaryawan') or 
                           emp.get('employeeName') or 
                           emp.get('EmployeeName') or 
                           emp.get('name') or '').strip()
                
                emp_id = (emp.get('employeeId') or 
                         emp.get('EmployeeID') or 
                         emp.get('employeeID') or 
                         emp.get('id') or '').strip()
                
                charge_job = (emp.get('chargeJob') or 
                             emp.get('charge_job') or 
                             emp.get('ChargeJob') or 
                             emp.get('task_code_data') or '').strip()
                
                # Use employee ID as primary key, fallback to name
                employee_key = emp_id if emp_id else emp_name
                
                if employee_key and charge_job:
                    parsed_data = self._parse_charge_job_data(charge_job)
                    processed_data[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'task_code': parsed_data['task_code'],
                        'station_code': parsed_data['station_code'], 
                        'machine_code': parsed_data['machine_code'],
                        'expense_code': parsed_data['expense_code'],
                        'raw_charge_job': charge_job
                    }
                    
                    # Also index by name for lookup flexibility
                    if emp_name and emp_name != employee_key:
                        processed_data[emp_name] = processed_data[employee_key]
            
            return processed_data
            
        except Exception as e:
            self.logger.error(f"Failed to process charge job response: {str(e)}")
            return {}
    
    def _parse_charge_job_data(self, charge_job_string: str) -> Dict[str, str]:
        """
        Parse charge job data string into components.
        
        Args:
            charge_job_string: Raw charge job string
            
        Returns:
            Dictionary with parsed components
        """
        try:
            # Import the parsing utility
            from utils.data_processor import parse_charge_job_data
            return parse_charge_job_data(charge_job_string)
            
        except ImportError:
            # Fallback parsing if utility not available
            return {
                'task_code': '',
                'station_code': '',
                'machine_code': '',
                'expense_code': charge_job_string  # Use full string as expense code
            }
    
    def _find_charge_job_for_employee(self, emp_id: str, emp_name: str, 
                                     charge_job_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Find charge job data for a specific employee.
        
        Args:
            emp_id: Employee ID
            emp_name: Employee name
            charge_job_data: Dictionary of charge job data
            
        Returns:
            Charge job data for employee or None
        """
        # Method 1: Exact match by employee ID
        if emp_id and emp_id in charge_job_data:
            return charge_job_data[emp_id]
        
        # Method 2: Exact match by employee name
        if emp_name and emp_name in charge_job_data:
            return charge_job_data[emp_name]
        
        # Method 3: Case-insensitive match by name
        if emp_name:
            for charge_key, charge_value in charge_job_data.items():
                if emp_name.upper() == charge_key.upper():
                    return charge_value
        
        # Method 4: Partial name match
        if emp_name:
            for charge_key, charge_value in charge_job_data.items():
                emp_name_clean = emp_name.upper().strip()
                charge_key_clean = charge_key.upper().strip()
                
                if (emp_name_clean in charge_key_clean or 
                    charge_key_clean in emp_name_clean or
                    len(set(emp_name_clean.split()) & set(charge_key_clean.split())) >= 1):
                    return charge_value
        
        return None
    
    def _get_fallback_charge_job_data(self) -> Dict[str, Any]:
        """Get fallback charge job data when API is unavailable."""
        if self._charge_job_cache:
            self.logger.info("Using cached charge job data as fallback")
            return self._charge_job_cache
        
        self.logger.warning("No charge job data available (no cache, API unavailable)")
        return {}
