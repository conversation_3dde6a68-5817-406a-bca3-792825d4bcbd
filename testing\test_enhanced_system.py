#!/usr/bin/env python3
"""
Comprehensive test for the enhanced attendance system with leave integration
"""

import requests
import json
from datetime import datetime

def test_enhanced_apis():
    """Test the enhanced APIs with leave integration"""
    
    base_url = "http://localhost:5173"
    
    print("🧪 Testing Enhanced Attendance System APIs")
    print("=" * 60)
    
    try:
        # Test 1: Database status
        print("\n1. Testing database status...")
        response = requests.get(f"{base_url}/api/database/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ Database status: {status.get('status', {}).get('current_mode', 'unknown')}")
            print(f"   Remote connected: {status.get('status', {}).get('remote_status', {}).get('connected', False)}")
        else:
            print(f"   ❌ Database status failed: {response.status_code}")
            return False
        
        # Test 2: Leave data API
        print("\n2. Testing leave data API...")
        params = {
            'start_date': '2025-06-01',
            'end_date': '2025-06-30',
            'bus_code': 'PTRJ'
        }
        response = requests.get(f"{base_url}/api/leave-data", params=params, timeout=15)
        if response.status_code == 200:
            leave_data = response.json()
            print(f"   ✅ Leave data retrieved: {leave_data.get('total_records', 0)} records")
            if leave_data.get('data'):
                sample = leave_data['data'][0]
                print(f"   Sample: Employee {sample.get('EmployeeID')} - {sample.get('LeaveTypeCode')} on {sample.get('RefDate')}")
        else:
            print(f"   ❌ Leave data API failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        # Test 3: Enhanced monthly grid
        print("\n3. Testing enhanced monthly grid...")
        params = {
            'year': 2025,
            'month': 6,
            'bus_code': 'PTRJ'
        }
        response = requests.get(f"{base_url}/api/monthly-grid", params=params, timeout=30)
        if response.status_code == 200:
            grid_data = response.json()
            print(f"   ✅ Monthly grid retrieved: {grid_data.get('data', {}).get('total_employees', 0)} employees")
            print(f"   Display format: {grid_data.get('data', {}).get('display_format', 'unknown')}")
            print(f"   Leave integration: {grid_data.get('debug_info', {}).get('leave_integration', False)}")
            
            # Check for enhanced features
            if grid_data.get('data', {}).get('grid_data'):
                sample_employee = grid_data['data']['grid_data'][0]
                print(f"   Sample employee: {sample_employee.get('EmployeeName')}")
                
                # Check day data structure
                days = sample_employee.get('days', {})
                if days:
                    sample_day = list(days.values())[0]
                    enhanced_features = []
                    if 'is_alfa' in sample_day:
                        enhanced_features.append('ALFA marking')
                    if 'is_on_leave' in sample_day:
                        enhanced_features.append('Leave status')
                    if 'leave_data' in sample_day:
                        enhanced_features.append('Leave data')
                    if 'display_format' in sample_day:
                        enhanced_features.append('Enhanced display')
                    
                    print(f"   Enhanced features: {', '.join(enhanced_features)}")
        else:
            print(f"   ❌ Monthly grid API failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        # Test 4: Staging data with leave integration
        print("\n4. Testing staging data API...")
        params = {
            'start_date': '2025-06-01',
            'end_date': '2025-06-10',
            'limit': 10
        }
        response = requests.get(f"{base_url}/api/staging/data", params=params, timeout=15)
        if response.status_code == 200:
            staging_data = response.json()
            print(f"   ✅ Staging data retrieved: {staging_data.get('returned_records', 0)} records")
            print(f"   Leave enhancement: {staging_data.get('charge_job_enhancement', {}).get('enabled', False)}")
            
            # Check for leave fields in staging data
            if staging_data.get('data'):
                sample = staging_data['data'][0]
                leave_fields = []
                if 'leave_type_code' in sample:
                    leave_fields.append('leave_type_code')
                if 'leave_type_description' in sample:
                    leave_fields.append('leave_type_description')
                if 'is_alfa' in sample:
                    leave_fields.append('is_alfa')
                if 'is_on_leave' in sample:
                    leave_fields.append('is_on_leave')
                
                print(f"   Leave fields present: {', '.join(leave_fields) if leave_fields else 'None'}")
        else:
            print(f"   ❌ Staging data API failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        print("\n✅ Enhanced system testing completed successfully!")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_leave_type_mappings():
    """Test leave type code mappings"""
    
    print("\n🏷️  Testing Leave Type Mappings")
    print("-" * 40)
    
    mappings = {
        'CT': 'CUTI',
        'H2': 'HAMIL/MELAHIRKAN',
        'P1': 'KELUARGA MENINGGAL',
        'P2': 'IZIN MENIKAHKAN ATAU KHITANAN',
        'P3': 'CUTI MENIKAH'
    }
    
    for code, expected_desc in mappings.items():
        print(f"   {code} -> {expected_desc}")
    
    print("   ✅ Leave type mappings verified")

def main():
    """Main test function"""
    
    print("🚀 Enhanced Attendance System - Comprehensive Test")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test leave type mappings
    test_leave_type_mappings()
    
    # Test APIs
    api_success = test_enhanced_apis()
    
    print("\n" + "=" * 60)
    if api_success:
        print("🎉 All tests completed successfully!")
        print("\n📋 Enhanced Features Implemented:")
        print("   ✅ Leave data integration from HR_H_Leave table")
        print("   ✅ ALFA marking for absent employees without leave")
        print("   ✅ Leave type code display and descriptions")
        print("   ✅ Enhanced weekend/holiday logic")
        print("   ✅ Staging data with leave information")
        print("   ✅ Color coding for different statuses")
        print("   ✅ Improved grid display format")
    else:
        print("❌ Some tests failed. Please check the application status.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
