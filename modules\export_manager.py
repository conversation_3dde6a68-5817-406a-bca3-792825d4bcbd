"""
Export Manager Module for Attendance Reports
Handles Excel and JSON exports with proper formatting and styling.
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

# Configure logging
logger = logging.getLogger(__name__)

class ExportManager:
    """
    Manages export operations for attendance reports.
    Supports Excel and JSON formats with proper formatting.
    """
    
    def __init__(self, export_dir: str = "exports"):
        """
        Initialize export manager.
        
        Args:
            export_dir: Directory to save exported files
        """
        self.export_dir = export_dir
        os.makedirs(export_dir, exist_ok=True)
        logger.info(f"ExportManager initialized with export directory: {export_dir}")
    
    def export_to_excel(self, data: List[Dict[str, Any]], filename: str = None, 
                       title: str = "Attendance Report") -> str:
        """
        Export data to Excel file with professional formatting.
        
        Args:
            data: List of dictionaries containing attendance data
            filename: Custom filename (optional)
            title: Report title for the Excel sheet
            
        Returns:
            Path to the exported Excel file
        """
        if not data:
            logger.warning("No data provided for Excel export")
            return None
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"attendance_report_{timestamp}.xlsx"
        
        # Ensure .xlsx extension
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        filepath = os.path.join(self.export_dir, filename)
        
        try:
            # Create DataFrame
            df = pd.DataFrame(data)
            
            # Create workbook and worksheet
            wb = Workbook()
            ws = wb.active
            ws.title = "Attendance Report"
            
            # Define styles
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            title_font = Font(bold=True, size=16)
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # Add title
            ws['A1'] = title
            ws['A1'].font = title_font
            ws.merge_cells('A1:' + chr(65 + len(df.columns) - 1) + '1')
            
            # Add timestamp
            ws['A2'] = f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws.merge_cells('A2:' + chr(65 + len(df.columns) - 1) + '2')
            
            # Add data starting from row 4
            start_row = 4
            
            # Add headers
            for col_num, column in enumerate(df.columns, 1):
                cell = ws.cell(row=start_row, column=col_num, value=column)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = border
            
            # Add data rows
            for row_num, row_data in enumerate(df.values, start_row + 1):
                for col_num, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_num, column=col_num, value=value)
                    cell.border = border
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    
                    # Format numeric columns
                    if isinstance(value, (int, float)) and col_num > 3:  # Assuming first 3 cols are text
                        cell.number_format = '0.00'
            
            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)  # Max width of 50
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Save workbook
            wb.save(filepath)
            logger.info(f"Excel file exported successfully: {filepath}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting to Excel: {str(e)}")
            raise
    
    def export_to_json(self, data: List[Dict[str, Any]], filename: str = None,
                      include_metadata: bool = True) -> str:
        """
        Export data to JSON file with optional metadata.
        
        Args:
            data: List of dictionaries containing attendance data
            filename: Custom filename (optional)
            include_metadata: Whether to include metadata in JSON
            
        Returns:
            Path to the exported JSON file
        """
        if not data:
            logger.warning("No data provided for JSON export")
            return None
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"attendance_report_{timestamp}.json"
        
        # Ensure .json extension
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = os.path.join(self.export_dir, filename)
        
        try:
            # Prepare export data
            export_data = {}
            
            if include_metadata:
                export_data['metadata'] = {
                    'export_timestamp': datetime.now().isoformat(),
                    'total_records': len(data),
                    'export_format': 'JSON',
                    'generated_by': 'Venus Attendance Report System'
                }
            
            export_data['attendance_data'] = data
            
            # Convert datetime objects to strings for JSON serialization
            def json_serializer(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            # Write JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=json_serializer)
            
            logger.info(f"JSON file exported successfully: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting to JSON: {str(e)}")
            raise
    
    def export_monthly_grid(self, grid_data: Dict[str, Any], filename: str = None) -> str:
        """
        Export monthly attendance grid to Excel with special formatting.
        
        Args:
            grid_data: Monthly grid data dictionary
            filename: Custom filename (optional)
            
        Returns:
            Path to the exported Excel file
        """
        if not grid_data or 'grid_data' not in grid_data:
            logger.warning("No grid data provided for export")
            return None
        
        # Generate filename if not provided
        if not filename:
            year = grid_data.get('year', datetime.now().year)
            month = grid_data.get('month', datetime.now().month)
            filename = f"monthly_grid_{year}_{month:02d}.xlsx"
        
        # Ensure .xlsx extension
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        filepath = os.path.join(self.export_dir, filename)
        
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = f"Monthly Grid {grid_data.get('month_name', '')} {grid_data.get('year', '')}"
            
            # Define styles
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            title_font = Font(bold=True, size=14)
            weekend_fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # Add title and metadata
            title = f"Monthly Attendance Grid - {grid_data.get('month_name', '')} {grid_data.get('year', '')}"
            ws['A1'] = title
            ws['A1'].font = title_font
            ws.merge_cells('A1:AG1')  # Merge across all columns
            
            # Add summary info
            ws['A2'] = f"Total Employees: {grid_data.get('total_employees', 0)}"
            ws['A3'] = f"Days in Month: {grid_data.get('days_in_month', 0)}"
            ws['A4'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # Start data from row 6
            start_row = 6
            
            # Create headers
            headers = ['No', 'Employee ID', 'Employee Name']
            days_in_month = grid_data.get('days_in_month', 31)
            
            # Add day headers
            for day in range(1, days_in_month + 1):
                headers.append(str(day))
            
            # Write headers
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=start_row, column=col_num, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = border
            
            # Write employee data
            employees = grid_data.get('grid_data', [])
            for row_num, employee in enumerate(employees, start_row + 1):
                # Employee info columns
                ws.cell(row=row_num, column=1, value=employee.get('No', '')).border = border
                ws.cell(row=row_num, column=2, value=employee.get('EmployeeID', '')).border = border
                ws.cell(row=row_num, column=3, value=employee.get('EmployeeName', '')).border = border
                
                # Daily hours columns
                days_data = employee.get('days', {})
                for day in range(1, days_in_month + 1):
                    col_num = day + 3  # Offset by 3 for employee info columns
                    day_value = days_data.get(str(day), '-')
                    
                    cell = ws.cell(row=row_num, column=col_num, value=day_value)
                    cell.border = border
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    
                    # Color weekend columns (Saturday/Sunday)
                    # Note: This is a simplified example, actual weekend detection would need date calculation
                    if day % 7 in [0, 6]:  # Approximate weekend detection
                        cell.fill = weekend_fill
            
            # Auto-adjust column widths
            ws.column_dimensions['A'].width = 5   # No
            ws.column_dimensions['B'].width = 15  # Employee ID
            ws.column_dimensions['C'].width = 25  # Employee Name
            
            # Set day columns to narrow width
            for day in range(1, days_in_month + 1):
                col_letter = chr(67 + day)  # Start from column D (after C)
                ws.column_dimensions[col_letter].width = 4
            
            # Save workbook
            wb.save(filepath)
            logger.info(f"Monthly grid exported successfully: {filepath}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting monthly grid: {str(e)}")
            raise
    
    def get_export_stats(self) -> Dict[str, Any]:
        """
        Get statistics about exported files.
        
        Returns:
            Dictionary with export statistics
        """
        try:
            files = os.listdir(self.export_dir)
            excel_files = [f for f in files if f.endswith('.xlsx')]
            json_files = [f for f in files if f.endswith('.json')]
            
            stats = {
                'total_files': len(files),
                'excel_files': len(excel_files),
                'json_files': len(json_files),
                'export_directory': self.export_dir,
                'recent_exports': sorted(files, key=lambda x: os.path.getmtime(os.path.join(self.export_dir, x)), reverse=True)[:10]
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting export stats: {str(e)}")
            return {'error': str(e)}
    
    def cleanup_old_exports(self, days_old: int = 30) -> int:
        """
        Clean up export files older than specified days.
        
        Args:
            days_old: Number of days old files to delete
            
        Returns:
            Number of files deleted
        """
        try:
            import time
            
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            deleted_count = 0
            
            for filename in os.listdir(self.export_dir):
                file_path = os.path.join(self.export_dir, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"Deleted old export file: {filename}")
            
            logger.info(f"Cleanup completed: {deleted_count} files deleted")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            return 0 