/**
 * Google Apps Script for Attendance Report Synchronization
 * 
 * This script receives attendance data from the VenusHR14 Attendance Report System
 * and synchronizes it with a Google Spreadsheet.
 * 
 * Setup Instructions:
 * 1. Create a new Google Apps Script project
 * 2. Replace the default code with this script
 * 3. Create a Google Spreadsheet with a sheet named 'AttendanceData'
 * 4. Set up the spreadsheet with headers in row 1:
 *    A: Employee ID | B: Employee Name | C: Date | D: Day of Week | E: Shift 
 *    F: Check In | G: Check Out | H: Regular Hours | I: Overtime Hours | J: Total Hours
 * 5. Deploy as web app with execute permissions for "Anyone"
 * 6. Copy the deployment URL to use in the attendance report system
 * 
 * <AUTHOR> AI Assistant
 * @version 2.0
 */

/**
 * Handle GET requests from the attendance report system
 * @param {Object} e - Event object containing request parameters
 * @returns {ContentService.TextOutput} JSON response
 */
function doGet(e) {
  try {
    console.log('=== Incoming Request ===');
    console.log('Parameters:', JSON.stringify(e.parameter));
    
    const action = e.parameter.action;
    const data = e.parameter.data;
    
    console.log('Action:', action);
    console.log('Data length:', data ? data.length : 'undefined');
    
    if (!action) {
      throw new Error('Action parameter is required');
    }
    
    let result;
    
    switch (action) {
      case 'sync_attendance':
        result = handleAttendanceSync(e.parameter);
        break;
      case 'sync_monthly_grid':
        result = handleMonthlyGridSync(e.parameter);
        break;
      case 'sync_daily_grid':
        result = handleDailyGridSync(e.parameter.data);
        break;
      case 'get_data':
        result = handleGetData(e.parameter);
        break;
      case 'clear_data':
        result = handleClearData(e.parameter);
        break;
      default:
        throw new Error(`Invalid action: ${action}. Supported actions: sync_attendance, sync_monthly_grid, sync_daily_grid, get_data, clear_data`);
    }
    
    console.log('=== Request Completed Successfully ===');
    return ContentService
      .createTextOutput(JSON.stringify(result))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('=== Error in doGet ===');
    console.error('Error:', error.toString());
    console.error('Stack:', error.stack);
    
    const errorResult = {
      status: 'error',
      message: error.toString(),
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
    
    return ContentService
      .createTextOutput(JSON.stringify(errorResult))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle attendance data synchronization
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleAttendanceSync(params) {
  try {
    // Get the data parameter
    const dataParam = params.data;
    if (!dataParam) {
      return { 
        status: 'error', 
        message: 'No data provided for sync' 
      };
    }

    // Parse the attendance data
    let attendanceData;
    try {
      attendanceData = JSON.parse(dataParam);
    } catch (parseError) {
      return { 
        status: 'error', 
        message: 'Invalid JSON data format' 
      };
    }

    if (!Array.isArray(attendanceData) || attendanceData.length === 0) {
      return { 
        status: 'error', 
        message: 'Data must be a non-empty array' 
      };
    }

    // Get the spreadsheet and worksheet
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName('AttendanceData');
    
    // Create sheet if it doesn't exist
    if (!sheet) {
      sheet = spreadsheet.insertSheet('AttendanceData');
      
      // Set up headers
      const headers = [
        'Employee ID', 'Employee Name', 'Date', 'Day of Week', 'Shift',
        'Check In', 'Check Out', 'Regular Hours', 'Overtime Hours', 'Total Hours',
        'Sync Date', 'Sync Time'
      ];
      
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format header row
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      headerRange.setWrap(true);
      
      // Set column widths
      sheet.setColumnWidth(1, 120); // Employee ID
      sheet.setColumnWidth(2, 200); // Employee Name
      sheet.setColumnWidth(3, 100); // Date
      sheet.setColumnWidth(4, 100); // Day of Week
      sheet.setColumnWidth(5, 80);  // Shift
      sheet.setColumnWidth(6, 100); // Check In
      sheet.setColumnWidth(7, 100); // Check Out
      sheet.setColumnWidth(8, 100); // Regular Hours
      sheet.setColumnWidth(9, 100); // Overtime Hours
      sheet.setColumnWidth(10, 100); // Total Hours
      sheet.setColumnWidth(11, 120); // Sync Date
      sheet.setColumnWidth(12, 120); // Sync Time
    }

    // Prepare data for insertion
    const currentDate = new Date();
    const syncDate = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    const dataToInsert = [];
    
    for (const record of attendanceData) {
      const row = [
        record.employeeId || '',
        record.employeeName || '',
        record.date || '',
        record.dayOfWeek || '',
        record.shift || '',
        record.checkIn || '',
        record.checkOut || '',
        parseFloat(record.regularHours) || 0,
        parseFloat(record.overtimeHours) || 0,
        parseFloat(record.totalHours) || 0,
        syncDate,
        syncTime
      ];
      dataToInsert.push(row);
    }

    // Insert data starting from the next available row
    const lastRow = sheet.getLastRow();
    const startRow = lastRow + 1;
    
    if (dataToInsert.length > 0) {
      const range = sheet.getRange(startRow, 1, dataToInsert.length, dataToInsert[0].length);
      range.setValues(dataToInsert);
      
      // Apply alternating colors for better readability
      for (let i = 0; i < dataToInsert.length; i++) {
        const rowNumber = startRow + i;
        const rowRange = sheet.getRange(rowNumber, 1, 1, dataToInsert[0].length);
        
        if (rowNumber % 2 === 0) {
          rowRange.setBackground('#f8f9fa');
        } else {
          rowRange.setBackground('#ffffff');
        }
      }
    }

    // Return success response
    return { 
      status: 'success', 
      message: `Successfully synced ${attendanceData.length} attendance records`,
      data: {
        records_synced: attendanceData.length,
        sync_date: syncDate,
        sync_time: syncTime,
        sheet_name: 'AttendanceData',
        total_rows: sheet.getLastRow()
      }
    };

  } catch (error) {
    console.error('Error in handleAttendanceSync:', error);
    return { 
      status: 'error', 
      message: 'Sync failed: ' + error.toString() 
    };
  }
}

/**
 * Handle monthly grid data synchronization
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleMonthlyGridSync(params) {
  try {
    // Get the data parameter
    const dataParam = params.data;
    if (!dataParam) {
      return { 
        status: 'error', 
        message: 'No data provided for monthly grid sync' 
      };
    }

    // Parse the monthly grid data
    let monthlyGridData;
    try {
      monthlyGridData = JSON.parse(dataParam);
    } catch (parseError) {
      return { 
        status: 'error', 
        message: 'Invalid JSON data format' 
      };
    }

    if (!Array.isArray(monthlyGridData) || monthlyGridData.length === 0) {
      return { 
        status: 'error', 
        message: 'Data must be a non-empty array' 
      };
    }

    // Get the spreadsheet and worksheet
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName('MonthlyGridData');
    
    // Create sheet if it doesn't exist
    if (!sheet) {
      sheet = spreadsheet.insertSheet('MonthlyGridData');
      
      // Set up headers
      const headers = [
        'No', 'Employee ID', 'Employee Name', 'Year', 'Month', 'Month Name',
        'Total Working Days', 'Total Regular Hours', 'Total Overtime Hours', 'Total Hours',
        'Sync Date', 'Sync Time'
      ];
      
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format header row
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#28a745');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      headerRange.setWrap(true);
      
      // Set column widths
      sheet.setColumnWidth(1, 60);  // No
      sheet.setColumnWidth(2, 120); // Employee ID
      sheet.setColumnWidth(3, 200); // Employee Name
      sheet.setColumnWidth(4, 80);  // Year
      sheet.setColumnWidth(5, 80);  // Month
      sheet.setColumnWidth(6, 120); // Month Name
      sheet.setColumnWidth(7, 140); // Total Working Days
      sheet.setColumnWidth(8, 140); // Total Regular Hours
      sheet.setColumnWidth(9, 140); // Total Overtime Hours
      sheet.setColumnWidth(10, 120); // Total Hours
      sheet.setColumnWidth(11, 120); // Sync Date
      sheet.setColumnWidth(12, 120); // Sync Time
    }

    // Prepare data for insertion
    const currentDate = new Date();
    const syncDate = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    const dataToInsert = [];
    
    for (const record of monthlyGridData) {
      const row = [
        record.no || '',
        record.employeeId || '',
        record.employeeName || '',
        parseInt(record.year) || 0,
        parseInt(record.month) || 0,
        record.monthName || '',
        parseInt(record.totalWorkingDays) || 0,
        parseFloat(record.totalRegularHours) || 0,
        parseFloat(record.totalOvertimeHours) || 0,
        parseFloat(record.totalHours) || 0,
        syncDate,
        syncTime
      ];
      dataToInsert.push(row);
    }

    // Insert data starting from the next available row
    const lastRow = sheet.getLastRow();
    const startRow = lastRow + 1;
    
    if (dataToInsert.length > 0) {
      const range = sheet.getRange(startRow, 1, dataToInsert.length, dataToInsert[0].length);
      range.setValues(dataToInsert);
      
      // Apply alternating colors for better readability
      for (let i = 0; i < dataToInsert.length; i++) {
        const rowNumber = startRow + i;
        const rowRange = sheet.getRange(rowNumber, 1, 1, dataToInsert[0].length);
        
        if (rowNumber % 2 === 0) {
          rowRange.setBackground('#f8f9fa');
        } else {
          rowRange.setBackground('#ffffff');
        }
      }
      
      // Format number columns
      const workingDaysRange = sheet.getRange(startRow, 7, dataToInsert.length, 1);
      workingDaysRange.setNumberFormat('0');
      
      const hoursRange = sheet.getRange(startRow, 8, dataToInsert.length, 3);
      hoursRange.setNumberFormat('0.0');
    }

    // Return success response
    return { 
      status: 'success', 
      message: `Successfully synced ${dataToInsert.length} monthly grid records`,
      records_synced: dataToInsert.length,
      sync_date: syncDate,
      sync_time: syncTime
    };

  } catch (error) {
    console.error('Error in handleMonthlyGridSync:', error);
    return { 
      status: 'error', 
      message: 'Monthly grid sync failed: ' + error.toString() 
    };
  }
}

/**
 * Handle get data requests
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleGetData(params) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName('AttendanceData');
    
    if (!sheet) {
      return { 
        status: 'error', 
        message: 'AttendanceData sheet not found' 
      };
    }

    // Get all data from the sheet
    const lastRow = sheet.getLastRow();
    if (lastRow <= 1) {
      return { 
        status: 'success', 
        data: [],
        message: 'No data found in the sheet'
      };
    }

    const dataRange = sheet.getRange(2, 1, lastRow - 1, 12);
    const values = dataRange.getValues();
    
    const formattedData = values.map(row => ({
      employeeId: row[0],
      employeeName: row[1],
      date: row[2],
      dayOfWeek: row[3],
      shift: row[4],
      checkIn: row[5],
      checkOut: row[6],
      regularHours: row[7],
      overtimeHours: row[8],
      totalHours: row[9],
      syncDate: row[10],
      syncTime: row[11]
    }));

    return { 
      status: 'success', 
      data: formattedData,
      total_records: formattedData.length
    };

  } catch (error) {
    console.error('Error in handleGetData:', error);
    return { 
      status: 'error', 
      message: 'Failed to get data: ' + error.toString() 
    };
  }
}

/**
 * Handle clear data requests
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleClearData(params) {
  try {
    const confirmClear = params.confirm;
    if (confirmClear !== 'yes') {
      return { 
        status: 'error', 
        message: 'Clear operation requires confirm=yes parameter' 
      };
    }

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName('AttendanceData');
    
    if (!sheet) {
      return { 
        status: 'error', 
        message: 'AttendanceData sheet not found' 
      };
    }

    // Clear all data except headers
    const lastRow = sheet.getLastRow();
    if (lastRow > 1) {
      sheet.deleteRows(2, lastRow - 1);
    }

    return { 
      status: 'success', 
      message: 'All attendance data cleared successfully' 
    };

  } catch (error) {
    console.error('Error in handleClearData:', error);
    return { 
      status: 'error', 
      message: 'Failed to clear data: ' + error.toString() 
    };
  }
}

/**
 * Handle POST requests (alternative method for large data)
 * @param {Object} e - Event object containing request data
 * @returns {ContentService.TextOutput} JSON response
 */
function doPost(e) {
  try {
    console.log('=== POST Request Received ===');

    // Handle form data (application/x-www-form-urlencoded)
    let action, data;

    if (e.parameter && e.parameter.action) {
      // Form data from Flask requests.post(data=payload)
      action = e.parameter.action;
      data = e.parameter.data;
      console.log('Form data - Action:', action);
      console.log('Form data - Data length:', data ? data.length : 'undefined');
    } else if (e.postData && e.postData.contents) {
      // JSON data from direct POST
      const requestData = JSON.parse(e.postData.contents);
      action = requestData.action;
      data = requestData.data;
      console.log('JSON data - Action:', action);
      console.log('JSON data - Data length:', data ? data.length : 'undefined');
    } else {
      return ContentService.createTextOutput(
        JSON.stringify({
          status: 'error',
          message: 'No POST data received'
        })
      ).setMimeType(ContentService.MimeType.JSON);
    }

    if (!action) {
      return ContentService.createTextOutput(
        JSON.stringify({
          status: 'error',
          message: 'Action parameter is required'
        })
      ).setMimeType(ContentService.MimeType.JSON);
    }

    let result;

    switch (action) {
      case 'sync_attendance':
        result = handleAttendanceSync({ data: data });
        break;
      case 'sync_monthly_grid':
        result = handleMonthlyGridSync({ data: data });
        break;
      case 'sync_daily_grid':
        result = handleDailyGridSync(data);
        break;
      case 'get_data':
        result = handleGetData({ data: data });
        break;
      case 'clear_data':
        result = handleClearData({ data: data });
        break;
      default:
        result = {
          status: 'error',
          message: `Invalid POST action: ${action}. Supported actions: sync_attendance, sync_monthly_grid, sync_daily_grid, get_data, clear_data`
        };
    }

    console.log('=== POST Request Completed Successfully ===');
    return ContentService
      .createTextOutput(JSON.stringify(result))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    console.error('=== Error in doPost ===');
    console.error('Error:', error.toString());
    console.error('Stack:', error.stack);

    const errorResult = {
      status: 'error',
      message: error.toString(),
      stack: error.stack,
      timestamp: new Date().toISOString()
    };

    return ContentService.createTextOutput(
      JSON.stringify(errorResult)
    ).setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Test function to validate the script setup
 * Can be run from the Apps Script editor to test functionality
 */
function testScript() {
  console.log('Testing Google Apps Script for Attendance Sync...');
  
  // Test data
  const testData = [
    {
      employeeId: 'TEST001',
      employeeName: 'Test Employee',
      date: '2025-01-15',
      dayOfWeek: 'Wednesday',
      shift: 'Morning',
      checkIn: '08:00:00',
      checkOut: '17:00:00',
      regularHours: 8,
      overtimeHours: 1,
      totalHours: 9
    }
  ];

  // Simulate sync request
  const mockParams = {
    action: 'sync_attendance',
    data: JSON.stringify(testData)
  };

  const result = handleAttendanceSync(mockParams);
  console.log('Test result:', result.getContent());
  
  return 'Test completed. Check logs for results.';
}

function handleDailyGridSync(dataString) {
  try {
    console.log('=== Handling Enhanced Daily Grid Sync ===');
    
    if (!dataString) {
      throw new Error('No data provided for daily grid sync');
    }
    
    const data = JSON.parse(dataString);
    console.log('Parsed data records:', data.length);
    console.log('Sample record:', JSON.stringify(data[0], null, 2));
    
    // Get month/year info from first record
    const firstRecord = data[0];
    const monthName = firstRecord.monthName || 'Unknown';
    const year = firstRecord.year || new Date().getFullYear();
    const month = firstRecord.month || new Date().getMonth() + 1;
    const daysInMonth = firstRecord.daysInMonth || 31;
    
    // Create sheet name with month/year
    const sheetName = `monthlyGridData_${monthName}_${year}`;
    
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      console.log('Creating new sheet:', sheetName);
      sheet = spreadsheet.insertSheet(sheetName);
      
      // Create headers - Enhanced to include all visible columns
      const headers = ['No', 'Employee ID', 'Employee Name'];
      
      // Add day headers
      for (let day = 1; day <= daysInMonth; day++) {
        // Calculate day name
        const date = new Date(year, month - 1, day);
        const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
        const dayName = dayNames[date.getDay()];
        headers.push(`${day}\n(${dayName})`);
      }
      
      // Add summary columns (matching UI grid totals)
      headers.push('Total\nDays');
      headers.push('Regular\nHours');
      headers.push('Overtime\nHours');
      
      // Add charge job columns (matching new integration)
      headers.push('Task\nCode');
      headers.push('Machine\nCode');
      headers.push('Expense\nCode');
      
      // Add sync metadata
      headers.push('Sync Date');
      headers.push('Sync Time');
      
      // Set headers
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.getRange(1, 1, 1, headers.length).setBackground('#4285f4');
      sheet.getRange(1, 1, 1, headers.length).setFontColor('white');
      sheet.getRange(1, 1, 1, headers.length).setHorizontalAlignment('center');
      sheet.getRange(1, 1, 1, headers.length).setWrapStrategy(SpreadsheetApp.WrapStrategy.WRAP);
      
      // Set column widths
      sheet.setColumnWidth(1, 60);   // No
      sheet.setColumnWidth(2, 150);  // Employee ID
      sheet.setColumnWidth(3, 200);  // Employee Name
      
      // Set day columns width
      for (let i = 4; i <= 3 + daysInMonth; i++) {
        sheet.setColumnWidth(i, 80);
      }
      
      // Set summary columns width
      const summaryStartCol = 4 + daysInMonth;
      sheet.setColumnWidth(summaryStartCol, 80);     // Total Days
      sheet.setColumnWidth(summaryStartCol + 1, 100); // Regular Hours
      sheet.setColumnWidth(summaryStartCol + 2, 100); // Overtime Hours
      
      // Set charge job columns width
      sheet.setColumnWidth(summaryStartCol + 3, 150); // Task Code
      sheet.setColumnWidth(summaryStartCol + 4, 150); // Machine Code
      sheet.setColumnWidth(summaryStartCol + 5, 150); // Expense Code
      
      // Set sync metadata columns width
      sheet.setColumnWidth(summaryStartCol + 6, 120); // Sync Date
      sheet.setColumnWidth(summaryStartCol + 7, 120); // Sync Time
      
      // Color Sunday headers (red) and Saturday headers (blue)
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        const col = 3 + day; // Column position
        if (date.getDay() === 0) { // Sunday
          sheet.getRange(1, col).setBackground('#dc3545');
        } else if (date.getDay() === 6) { // Saturday
          sheet.getRange(1, col).setBackground('#6f42c1');
        }
      }
      
      // Color summary headers (green)
      for (let i = 0; i < 3; i++) {
        sheet.getRange(1, summaryStartCol + i).setBackground('#198754');
      }
      
      // Color charge job headers (blue)
      for (let i = 3; i < 6; i++) {
        sheet.getRange(1, summaryStartCol + i).setBackground('#0d6efd');
      }
    }
    
    // Get current date/time for sync tracking
    const now = new Date();
    const syncDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(now, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    // Prepare data for insertion with enhanced data processing
    const rows = data.map(record => {
      const row = [
        record.no || '',
        record.employeeId || '',
        record.employeeName || ''
      ];
      
      // Add daily hours data and calculate totals
      let totalWorkingDays = 0;
      let totalRegularHours = 0;
      let totalOvertimeHours = 0;
      
      for (let day = 1; day <= daysInMonth; day++) {
        const hours = record.dailyHours[day.toString()] || '-';
        row.push(hours);
        
        // Calculate totals for summary columns
        if (hours !== '-' && hours !== 'OFF') {
          if (hours.includes('|')) {
            const parts = hours.split('|');
            if (parts.length === 2) {
              const regularPart = parts[0].trim().replace(/[()]/g, '');
              const overtimePart = parts[1].trim().replace(/[()]/g, '');
              
              const regularHours = parseFloat(regularPart) || 0;
              const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
              
              if (regularHours > 0 || overtimeHours > 0) {
                totalWorkingDays++;
                totalRegularHours += regularHours;
                totalOvertimeHours += overtimeHours;
              }
            }
          } else {
            const hoursNum = parseFloat(hours);
            if (hoursNum > 0) {
              totalWorkingDays++;
              totalRegularHours += hoursNum;
            }
          }
        }
      }
      
      // Add summary columns
      row.push(totalWorkingDays);
      row.push(totalRegularHours.toFixed(1));
      row.push(totalOvertimeHours.toFixed(1));
      
      // Add charge job data (if available)
      row.push(record.taskCode || '');
      row.push(record.machineCode || '');
      row.push(record.expenseCode || '');
      
      // Add sync metadata
      row.push(syncDate);
      row.push(syncTime);
      
      return row;
    });
    
    // Insert data
    const startRow = sheet.getLastRow() + 1;
    console.log('Inserting enhanced daily data starting at row:', startRow);
    
    sheet.getRange(startRow, 1, rows.length, rows[0].length).setValues(rows);
    
    // Apply enhanced formatting with 7-hour conditional formatting
    for (let i = 0; i < rows.length; i++) {
      const currentRow = startRow + i;
      const record = data[i]; // Get the actual record
      
      // Format daily hours columns with enhanced conditional formatting
      for (let day = 1; day <= daysInMonth; day++) {
        const col = 3 + day; // Column position (after No, Employee ID, Employee Name)
        const hours = record.dailyHours[day.toString()] || '-';
        const cell = sheet.getRange(currentRow, col);
        
        // Apply enhanced color coding based on hours
        if (hours === 'OFF') {
          cell.setBackground('#d1ecf1'); // Light blue for OFF
          cell.setFontColor('#055160');
        } else if (hours === '-') {
          cell.setBackground('#f8f9fa'); // Light gray for absent
          cell.setFontColor('#6c757d');
        } else if (hours.includes('|')) {
          // Parse regular and overtime hours
          const parts = hours.split('|');
          if (parts.length === 2) {
            const regularPart = parts[0].trim().replace(/[()]/g, '');
            const overtimePart = parts[1].trim().replace(/[()]/g, '');
            
            const regularHours = parseFloat(regularPart) || 0;
            const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
            
            // Check if Saturday (different threshold)
            const date = new Date(year, month - 1, day);
            const isSaturday = date.getDay() === 6;
            const threshold = isSaturday ? 5 : 7;
            
            // Get status information for enhanced formatting
            const dayStatus = record.dailyHours[`${day}_status`];
            const checkInOnly = dayStatus?.check_in_only || false;
            const checkOutOnly = dayStatus?.check_out_only || false;
            const completeRecord = dayStatus?.complete_record || true; // Default to true for backward compatibility
            
            // ENHANCED FORMATTING: Handle incomplete records first
            if (checkInOnly) {
              // Blue background for check-in only records
              cell.setBackground('#bbdefb'); // Blue
              cell.setFontColor('#1976d2');
              cell.setFontWeight('bold');
              cell.setBorder(true, true, true, true, false, false, '#2196f3', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
            } else if (checkOutOnly) {
              // Purple background for check-out only records  
              cell.setBackground('#e1bee7'); // Purple
              cell.setFontColor('#7b1fa2');
              cell.setFontWeight('bold');
              cell.setBorder(true, true, true, true, false, false, '#9c27b0', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
            } else if (dayStatus?.status === 'overtime_only' || (regularHours === 0 && overtimeHours > 0)) {
              // NEW: Yellow background for overtime-only records (no normal work hours)
              cell.setBackground('#fff3cd'); // Yellow
              cell.setFontColor('#664d03');
              cell.setFontWeight('bold');
              cell.setBorder(true, true, true, true, false, false, '#ffc107', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
            } else {
              // Complete records - apply new color scheme
              
              // 1. Dark Green: Normal hours > 7 AND both check-in/check-out present
              if (regularHours > 7 && completeRecord) {
                cell.setBackground('#2e7d32'); // Dark green
                cell.setFontColor('#ffffff');
                cell.setFontWeight('bold');
                cell.setBorder(true, true, true, true, false, false, '#1b5e20', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
              }
              // 2. Light Green: Normal hours present (any amount) AND no overtime
              else if (regularHours > 0 && overtimeHours === 0) {
                cell.setBackground('#c8e6c9'); // Light green
                cell.setFontColor('#2e7d32');
                cell.setFontWeight('bold');
              }
              // ENHANCED: Specific formatting for exactly 7 hours normal work (original requirement)
              else if (regularHours === 7 && !isSaturday) {
                // BRIGHT GREEN for exactly 7 hours normal work (Monday-Friday)
                cell.setBackground('#22c55e'); // Bright green
                cell.setFontColor('#ffffff');
                cell.setFontWeight('bold');
                // Add border to make it stand out
                cell.setBorder(true, true, true, true, false, false, '#16a34a', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
              } else if (regularHours === 5 && isSaturday) {
                // BRIGHT GREEN for exactly 5 hours normal work (Saturday)
                cell.setBackground('#22c55e'); // Bright green
                cell.setFontColor('#ffffff');
                cell.setFontWeight('bold');
                cell.setBorder(true, true, true, true, false, false, '#16a34a', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
              } else if (regularHours >= threshold && overtimeHours === 0) {
                cell.setBackground('#d1e7dd'); // Standard green for full regular hours
                cell.setFontColor('#0f5132');
                cell.setFontWeight('bold');
              } else if (regularHours > 0) {
                if (overtimeHours > 0) {
                  cell.setBackground('#fff3cd'); // Yellow for overtime
                  cell.setFontColor('#664d03');
                  cell.setFontWeight('bold');
                } else {
                  cell.setBackground('#f8d7da'); // Red for partial hours
                  cell.setFontColor('#842029');
                  cell.setFontWeight('bold');
                }
              }
            }
          }
        }
        
        // Center align and set font
        cell.setHorizontalAlignment('center');
        cell.setFontFamily('Courier New');
        cell.setFontSize(9);
      }
      
      // Format summary columns
      const summaryStartCol = 4 + daysInMonth;
      
      // Total Days
      const totalDaysCell = sheet.getRange(currentRow, summaryStartCol);
      totalDaysCell.setBackground('#e8f5e8');
      totalDaysCell.setFontWeight('bold');
      totalDaysCell.setHorizontalAlignment('center');
      
      // Regular Hours - apply 7-hour highlighting here too
      const regularHoursCell = sheet.getRange(currentRow, summaryStartCol + 1);
      const totalRegularHours = parseFloat(rows[i][summaryStartCol]) || 0;
      
      // Calculate expected hours for the month
      let expectedRegularHours = 0;
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        const dayOfWeek = date.getDay();
        if (dayOfWeek >= 1 && dayOfWeek <= 5) { // Monday to Friday
          expectedRegularHours += 7;
        } else if (dayOfWeek === 6) { // Saturday
          expectedRegularHours += 5;
        }
        // Sunday is 0 hours
      }
      
      if (Math.abs(totalRegularHours - expectedRegularHours) < 0.1) {
        // Perfect attendance - bright green
        regularHoursCell.setBackground('#22c55e');
        regularHoursCell.setFontColor('#ffffff');
        regularHoursCell.setFontWeight('bold');
        regularHoursCell.setBorder(true, true, true, true, false, false, '#16a34a', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
      } else {
        regularHoursCell.setBackground('#e8f5e8');
        regularHoursCell.setFontWeight('bold');
      }
      regularHoursCell.setHorizontalAlignment('center');
      
      // Overtime Hours
      const overtimeHoursCell = sheet.getRange(currentRow, summaryStartCol + 2);
      overtimeHoursCell.setBackground('#e8f5e8');
      overtimeHoursCell.setFontWeight('bold');
      overtimeHoursCell.setHorizontalAlignment('center');
      
      // Format charge job columns
      for (let j = 3; j < 6; j++) {
        const chargeJobCell = sheet.getRange(currentRow, summaryStartCol + j);
        chargeJobCell.setBackground('#e8f5e8');
        chargeJobCell.setFontWeight('bold');
        chargeJobCell.setHorizontalAlignment('center');
        chargeJobCell.setFontSize(8);
      }
      
      // Add alternating row colors for employee info columns
      if (currentRow % 2 === 0) {
        sheet.getRange(currentRow, 1, 1, 3).setBackground('#f8f9fa');
      }
    }
    
    // Freeze header row and first 3 columns
    sheet.setFrozenRows(1);
    sheet.setFrozenColumns(3);
    
    // Auto-resize charge job columns for better readability
    const summaryStartCol = 4 + daysInMonth;
    for (let i = 3; i < 6; i++) {
      sheet.autoResizeColumn(summaryStartCol + i);
    }
    
    console.log(`Successfully synced ${data.length} enhanced daily grid records to sheet ${sheetName}`);
    console.log(`Data inserted at rows ${startRow} to ${startRow + rows.length - 1}`);
    console.log('Enhanced features applied: 7-hour conditional formatting, charge job data, summary totals');
    
    return {
      status: 'success',
      message: `Successfully synced ${data.length} enhanced daily grid records with all visible columns`,
      records_synced: data.length,
      sync_date: syncDate,
      sync_time: syncTime,
      sheet_name: sheetName,
      start_row: startRow,
      end_row: startRow + rows.length - 1,
      month_name: monthName,
      year: year,
      days_in_month: daysInMonth,
      enhanced_features: [
        '7-hour conditional formatting',
        'Total hours summary columns',
        'Charge job data integration',
        'Perfect attendance highlighting'
      ]
    };
    
  } catch (error) {
    console.error('Error in enhanced handleDailyGridSync:', error);
    throw error;
  }
} 