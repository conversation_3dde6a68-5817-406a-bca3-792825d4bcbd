"""
GUI module for the Attendance Report generator.
Provides a graphical interface for generating attendance reports.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkcalendar import DateEntry
from datetime import datetime
import subprocess

from attendance_reporter import AttendanceReporter

class AttendanceReportGUI:
    """
    GUI for generating attendance reports.
    """
    
    def __init__(self, root):
        """
        Initialize the GUI.
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("Venus Attendance Report Generator")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        self.reporter = AttendanceReporter()
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the user interface components."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Venus Attendance Report Generator", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Notebook for different report types
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create tabs
        self.daily_tab = ttk.Frame(self.notebook)
        self.monthly_tab = ttk.Frame(self.notebook)
        self.range_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.daily_tab, text="Daily Report")
        self.notebook.add(self.monthly_tab, text="Monthly Report")
        self.notebook.add(self.range_tab, text="Date Range Report")
        
        # Set up each tab
        self._setup_daily_tab()
        self._setup_monthly_tab()
        self._setup_range_tab()
        
        # Business code frame (common to all tabs)
        bus_frame = ttk.LabelFrame(main_frame, text="Business Code (Optional)", padding=10)
        bus_frame.pack(fill=tk.X, pady=10)
        
        self.bus_code_var = tk.StringVar()
        ttk.Label(bus_frame, text="Business Code:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(bus_frame, textvariable=self.bus_code_var, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(bus_frame, text="(e.g., PTRJ)").pack(side=tk.LEFT, padx=5)
        
        # Bottom buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Generate Report", command=self.generate_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Open Reports Folder", command=self.open_reports_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Exit", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
        
    def _setup_daily_tab(self):
        """Set up the daily report tab."""
        frame = ttk.Frame(self.daily_tab, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="Select Date:").pack(anchor=tk.W, pady=5)
        
        # Date selector
        self.daily_date = DateEntry(frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2,
                                   date_pattern='yyyy-mm-dd')
        self.daily_date.pack(anchor=tk.W, pady=5)
        
    def _setup_monthly_tab(self):
        """Set up the monthly report tab."""
        frame = ttk.Frame(self.monthly_tab, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Year selector
        year_frame = ttk.Frame(frame)
        year_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(year_frame, text="Year:").pack(side=tk.LEFT, padx=5)
        
        current_year = datetime.now().year
        years = list(range(current_year - 5, current_year + 2))
        self.year_var = tk.StringVar(value=str(current_year))
        year_combo = ttk.Combobox(year_frame, textvariable=self.year_var, 
                                 values=years, width=6)
        year_combo.pack(side=tk.LEFT, padx=5)
        
        # Month selector
        month_frame = ttk.Frame(frame)
        month_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(month_frame, text="Month:").pack(side=tk.LEFT, padx=5)
        
        months = [(str(i), datetime(2000, i, 1).strftime('%B')) for i in range(1, 13)]
        self.month_var = tk.StringVar(value=str(datetime.now().month))
        month_combo = ttk.Combobox(month_frame, textvariable=self.month_var,
                                  values=[f"{m[0]} - {m[1]}" for m in months], width=15)
        month_combo.pack(side=tk.LEFT, padx=5)
        
    def _setup_range_tab(self):
        """Set up the date range report tab."""
        frame = ttk.Frame(self.range_tab, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Start date
        start_frame = ttk.Frame(frame)
        start_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(start_frame, text="Start Date:").pack(side=tk.LEFT, padx=5)
        
        self.start_date = DateEntry(start_frame, width=12, background='darkblue',
                                   foreground='white', borderwidth=2,
                                   date_pattern='yyyy-mm-dd')
        self.start_date.pack(side=tk.LEFT, padx=5)
        
        # End date
        end_frame = ttk.Frame(frame)
        end_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(end_frame, text="End Date:").pack(side=tk.LEFT, padx=5)
        
        self.end_date = DateEntry(end_frame, width=12, background='darkblue',
                                 foreground='white', borderwidth=2,
                                 date_pattern='yyyy-mm-dd')
        self.end_date.pack(side=tk.LEFT, padx=5)
        
    def generate_report(self):
        """Generate a report based on the selected tab and parameters."""
        try:
            bus_code = self.bus_code_var.get() or None
            
            current_tab = self.notebook.index(self.notebook.select())
            
            if current_tab == 0:  # Daily report
                date_str = self.daily_date.get_date().strftime("%Y-%m-%d")
                report_path = self.reporter.generate_daily_report(date_str, bus_code)
                report_type = "daily"
                
            elif current_tab == 1:  # Monthly report
                year = int(self.year_var.get())
                month = int(self.month_var.get().split(' - ')[0])
                report_path = self.reporter.generate_monthly_report(year, month, bus_code)
                report_type = "monthly"
                
            elif current_tab == 2:  # Range report
                start_date = self.start_date.get_date().strftime("%Y-%m-%d")
                end_date = self.end_date.get_date().strftime("%Y-%m-%d")
                
                if start_date > end_date:
                    messagebox.showerror("Error", "Start date cannot be after end date")
                    return
                    
                report_path = self.reporter.generate_date_range_report(start_date, end_date, bus_code)
                report_type = "date range"
            
            if report_path:
                messagebox.showinfo("Success", f"The {report_type} report was generated successfully.\n\nFile: {os.path.basename(report_path)}")
                
                # Ask if user wants to open the file
                if messagebox.askyesno("Open File", "Would you like to open the report?"):
                    os.startfile(os.path.abspath(report_path))
            else:
                messagebox.showwarning("No Data", "No attendance data found for the specified criteria.")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")
    
    def open_reports_folder(self):
        """Open the reports folder in Windows Explorer."""
        try:
            export_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports'))
            os.makedirs(export_dir, exist_ok=True)
            os.startfile(export_dir)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open reports folder: {str(e)}")


def main():
    """Main entry point for the GUI application."""
    try:
        # Initialize tkinter
        root = tk.Tk()
        
        # Create the application
        app = AttendanceReportGUI(root)
        
        # Run the application
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main()) 