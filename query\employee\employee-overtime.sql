/*
Employee Overtime Data Query
Purpose: Retrieve overtime records with employee information
Database: VenusHR14
Tables: HR_T_Overtime, HR_M_EmployeePI
Parameters: @start_date, @end_date, @bus_code (optional), @employee_id (optional)
*/

SELECT 
    emp.EmployeeID,
    emp.EmployeeName,
    ot.OTDate,
    ot.OTNumber,
    ot.OTPeriode,
    ot.OTTypeCode,
    ot.OTStart,
    ot.O<PERSON>inish,
    ot.OTBreak,
    ot.OTTimeDuration,
    ot.OTHourDuration,
    ot.ActualOTHourDuration,
    ot.OTIndeks,
    ot.OTMeals,
    ot.OTTransport,
    ot.OTOther,
    ot.Description,
    ot.AppStatus,
    ot.ApprovedBy,
    ot.AppDate,
    ot.BusCode,
    -- Day of week in Indonesian
    CASE DATEPART(WEEKDAY, ot.OTDate)
        WHEN 1 THEN 'Minggu'
        WHEN 2 THEN 'Senin'
        WHEN 3 THEN 'Se<PERSON>a'
        WHEN 4 THEN 'Rabu'
        WHEN 5 THEN 'Ka<PERSON>'
        WHEN 6 THEN 'Jumat'
        WHEN 7 THEN 'Sabtu'
    END as DayOfWeek
FROM [VenusHR14].[dbo].[HR_T_Overtime] ot
INNER JOIN [VenusHR14].[dbo].[HR_M_EmployeePI] emp 
    ON ot.EmployeeID = emp.EmployeeID
WHERE ot.OTDate >= @start_date 
    AND ot.OTDate <= @end_date
    AND (@bus_code IS NULL OR ot.BusCode = @bus_code)
    AND (@employee_id IS NULL OR ot.EmployeeID = @employee_id)
ORDER BY ot.OTDate DESC, emp.EmployeeName 