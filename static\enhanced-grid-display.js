/**
 * Enhanced Grid Display with Leave Data Integration
 * Handles ALFA marking, leave type display, and improved color coding
 */

class EnhancedGridDisplay {
    constructor() {
        this.leaveTypeColors = {
            'CT': '#e3f2fd',      // Light blue for CUTI
            'H2': '#fce4ec',      // Light pink for HAMIL/MELAHIRKAN
            'P1': '#f3e5f5',      // Light purple for KELUARGA MENINGGAL
            'P2': '#e8f5e8',      // Light green for IZIN MENIKAHKAN
            'P3': '#fff3e0'       // Light orange for CUTI MENIKAH
        };
        
        this.statusColors = {
            'alfa': '#ffebee',     // Light red for ALFA
            'on_leave': '#e8f5e8', // Light green for on leave
            'off': '#f5f5f5',      // Light gray for OFF
            'overtime_only': '#fff3cd', // Light yellow for overtime only
            'needs_verification': '#fce4ec', // Light pink for verification needed
            'complete': '#e8f5e8'  // Light green for complete attendance
        };
    }

    /**
     * Format cell content based on status and leave data
     */
    formatCellContent(dayData) {
        const { status, normal_hours, overtime_hours, leave_data, is_sunday, is_national_holiday } = dayData;
        
        // Handle leave status
        if (status === 'on_leave' && leave_data) {
            return leave_data.leave_type_code || 'LEAVE';
        }
        
        // Handle ALFA status
        if (status === 'alfa') {
            return 'ALFA';
        }
        
        // Handle OFF status (Sunday/Holiday without overtime)
        if (status === 'off' || (is_sunday || is_national_holiday) && overtime_hours === 0) {
            return 'OFF';
        }
        
        // Handle overtime only (Sunday/Holiday with overtime)
        if (status === 'overtime_only' || (is_sunday || is_national_holiday) && overtime_hours > 0) {
            return `(0 | ${overtime_hours})`;
        }
        
        // Handle normal attendance with enhanced format
        if (normal_hours > 0 || overtime_hours > 0) {
            // Remove (-) symbols except for Sundays and holidays
            const normalDisplay = normal_hours > 0 ? normal_hours.toString() : 
                                 (is_sunday || is_national_holiday) ? '-' : '0';
            const overtimeDisplay = overtime_hours > 0 ? overtime_hours.toString() : 
                                   (is_sunday || is_national_holiday) ? '-' : '0';
            
            return `(${normalDisplay} | ${overtimeDisplay})`;
        }
        
        // Default absent display
        return is_sunday || is_national_holiday ? 'OFF' : '-';
    }

    /**
     * Get cell background color based on status
     */
    getCellBackgroundColor(dayData) {
        const { status, leave_data, is_alfa, is_on_leave } = dayData;
        
        // ALFA marking - RED background
        if (status === 'alfa' || is_alfa) {
            return '#ffcdd2'; // Red background for ALFA
        }
        
        // Leave status - specific colors by leave type
        if (status === 'on_leave' || is_on_leave) {
            if (leave_data && leave_data.leave_type_code) {
                return this.leaveTypeColors[leave_data.leave_type_code] || '#e8f5e8';
            }
            return '#e8f5e8'; // Default green for leave
        }
        
        // Other statuses
        return this.statusColors[status] || '#ffffff';
    }

    /**
     * Get cell text color based on status
     */
    getCellTextColor(dayData) {
        const { status, is_alfa, is_on_leave } = dayData;
        
        // ALFA marking - RED text
        if (status === 'alfa' || is_alfa) {
            return '#d32f2f'; // Red text for ALFA
        }
        
        // Leave status - dark green text
        if (status === 'on_leave' || is_on_leave) {
            return '#2e7d32'; // Dark green for leave
        }
        
        // Default text colors
        switch (status) {
            case 'off':
                return '#757575'; // Gray for OFF
            case 'overtime_only':
                return '#f57c00'; // Orange for overtime only
            case 'needs_verification':
                return '#c2185b'; // Pink for verification needed
            case 'complete':
                return '#388e3c'; // Green for complete
            default:
                return '#000000'; // Black default
        }
    }

    /**
     * Apply enhanced styling to a grid cell
     */
    styleGridCell(cellElement, dayData) {
        if (!cellElement || !dayData) return;
        
        // Set content
        cellElement.textContent = this.formatCellContent(dayData);
        
        // Set colors
        cellElement.style.backgroundColor = this.getCellBackgroundColor(dayData);
        cellElement.style.color = this.getCellTextColor(dayData);
        
        // Add special styling for ALFA
        if (dayData.status === 'alfa' || dayData.is_alfa) {
            cellElement.style.fontWeight = 'bold';
            cellElement.style.border = '2px solid #d32f2f';
        }
        
        // Add tooltip for leave data
        if (dayData.leave_data) {
            const tooltip = `Leave Type: ${dayData.leave_data.leave_type_description || dayData.leave_data.leave_type_code}`;
            if (dayData.leave_data.ref_number) {
                tooltip += `\nRef Number: ${dayData.leave_data.ref_number}`;
            }
            cellElement.title = tooltip;
        }
        
        // Center align text
        cellElement.style.textAlign = 'center';
        cellElement.style.verticalAlign = 'middle';
        cellElement.style.fontSize = '12px';
        cellElement.style.padding = '4px';
    }

    /**
     * Create legend for the enhanced grid
     */
    createLegend() {
        const legend = document.createElement('div');
        legend.className = 'grid-legend';
        legend.style.cssText = `
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        `;
        
        legend.innerHTML = `
            <h5>Grid Legend:</h5>
            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #ffcdd2; border: 1px solid #d32f2f; margin-right: 5px;"></div>
                    <span>ALFA (Absent without leave)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #e3f2fd; border: 1px solid #1976d2; margin-right: 5px;"></div>
                    <span>CT (CUTI)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #fce4ec; border: 1px solid #c2185b; margin-right: 5px;"></div>
                    <span>H2 (HAMIL/MELAHIRKAN)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #f3e5f5; border: 1px solid #7b1fa2; margin-right: 5px;"></div>
                    <span>P1 (KELUARGA MENINGGAL)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #e8f5e8; border: 1px solid #388e3c; margin-right: 5px;"></div>
                    <span>P2 (IZIN MENIKAHKAN)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #fff3e0; border: 1px solid #f57c00; margin-right: 5px;"></div>
                    <span>P3 (CUTI MENIKAH)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #f5f5f5; border: 1px solid #757575; margin-right: 5px;"></div>
                    <span>OFF (Sunday/Holiday)</span>
                </div>
            </div>
            <div style="margin-top: 10px;">
                <strong>Format:</strong> (Regular Hours | Overtime Hours) - ALFA shows in red, Leave types show as codes
            </div>
        `;
        
        return legend;
    }

    /**
     * Process grid data and apply enhanced styling
     */
    processGridData(gridData) {
        if (!gridData || !gridData.grid_data) return gridData;
        
        // Process each employee's data
        gridData.grid_data.forEach(employee => {
            if (employee.days) {
                Object.keys(employee.days).forEach(day => {
                    const dayData = employee.days[day];
                    
                    // Ensure all required properties exist
                    dayData.display_format = 'enhanced_with_leave';
                    dayData.formatted_content = this.formatCellContent(dayData);
                    dayData.background_color = this.getCellBackgroundColor(dayData);
                    dayData.text_color = this.getCellTextColor(dayData);
                });
            }
        });
        
        return gridData;
    }
}

// Export for use in other modules
window.EnhancedGridDisplay = EnhancedGridDisplay;
