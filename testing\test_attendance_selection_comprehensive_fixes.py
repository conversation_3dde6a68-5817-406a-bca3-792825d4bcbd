#!/usr/bin/env python3
"""
Comprehensive Test Script for Attendance Selection Functionality Fixes

Tests all three issues:
1. Checkboxes not appearing in main attendance table
2. Employee list synchronization between main report and staging tab
3. Missing PTRJ Employee ID in staging transfer and API

This script validates that all fixes are working properly.
"""

import requests
import json
import sys
from datetime import datetime, timedelta
import time

# Test configuration
BASE_URL = "http://127.0.0.1:5173"  # Adjust if your server runs on different port
BUS_CODE = "PTRJ"

def log_test_result(test_name, passed, details=""):
    """Log test result with consistent formatting."""
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")
    print()

def test_checkbox_functionality():
    """Test Issue 1: Checkboxes appearing in main attendance table."""
    print("=" * 60)
    print("TESTING ISSUE 1: CHECKBOX FUNCTIONALITY")
    print("=" * 60)
    
    # Note: This test verifies the frontend JavaScript fixes
    # The actual checkbox visibility is controlled by displayAttendanceData() function
    print("ℹ️  Frontend checkbox functionality testing:")
    print("   1. Load main attendance report by clicking 'Buat Laporan'")
    print("   2. Click 'Selection Mode' button in the attendance table")
    print("   3. Verify checkboxes appear next to row numbers")
    print("   4. Verify 'Pilih Semua' checkbox appears in table header")
    print("   5. Test individual row selection and 'Select All' functionality")
    print()
    
    # Test if the backend endpoints are working correctly
    try:
        # Test main attendance data endpoint
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        response = requests.get(f"{BASE_URL}/api/attendance", params={
            'start_date': start_date,
            'end_date': end_date,
            'bus_code': BUS_CODE
        })
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                record_count = len(data.get('data', []))
                log_test_result(
                    "Backend attendance data retrieval",
                    True,
                    f"Successfully retrieved {record_count} attendance records"
                )
                
                # Check if the data structure includes necessary fields for checkboxes
                if record_count > 0:
                    sample_record = data['data'][0]
                    required_fields = ['EmployeeID', 'EmployeeName', 'Date']
                    missing_fields = [field for field in required_fields if field not in sample_record]
                    
                    if not missing_fields:
                        log_test_result(
                            "Attendance record structure",
                            True,
                            f"All required fields present: {required_fields}"
                        )
                    else:
                        log_test_result(
                            "Attendance record structure",
                            False,
                            f"Missing fields: {missing_fields}"
                        )
                        
                    return True, record_count
                else:
                    log_test_result(
                        "Backend attendance data retrieval",
                        False,
                        "No attendance records found for testing"
                    )
                    return False, 0
            else:
                log_test_result(
                    "Backend attendance data retrieval",
                    False,
                    f"API error: {data.get('error', 'Unknown error')}"
                )
                return False, 0
        else:
            log_test_result(
                "Backend attendance data retrieval",
                False,
                f"HTTP error: {response.status_code}"
            )
            return False, 0
            
    except Exception as e:
        log_test_result(
            "Backend attendance data retrieval",
            False,
            f"Connection error: {str(e)}"
        )
        return False, 0

def test_employee_synchronization():
    """Test Issue 2: Employee list synchronization between main report and staging tab."""
    print("=" * 60)
    print("TESTING ISSUE 2: EMPLOYEE LIST SYNCHRONIZATION")
    print("=" * 60)
    
    try:
        # Test main employees endpoint
        main_response = requests.get(f"{BASE_URL}/api/employees", params={'bus_code': BUS_CODE})
        
        if main_response.status_code != 200:
            log_test_result(
                "Main employees endpoint",
                False,
                f"HTTP error: {main_response.status_code}"
            )
            return False
            
        main_data = main_response.json()
        if not main_data.get('success'):
            log_test_result(
                "Main employees endpoint",
                False,
                f"API error: {main_data.get('error', 'Unknown error')}"
            )
            return False
            
        main_employees = main_data.get('data', [])
        log_test_result(
            "Main employees endpoint",
            True,
            f"Retrieved {len(main_employees)} employees from main endpoint"
        )
        
        # Test staging employees endpoint
        staging_response = requests.get(f"{BASE_URL}/api/staging/employees", params={'bus_code': BUS_CODE})
        
        if staging_response.status_code != 200:
            log_test_result(
                "Staging employees endpoint",
                False,
                f"HTTP error: {staging_response.status_code}"
            )
            return False
            
        staging_data = staging_response.json()
        if not staging_data.get('success'):
            log_test_result(
                "Staging employees endpoint",
                False,
                f"API error: {staging_data.get('error', 'Unknown error')}"
            )
            return False
            
        staging_employees = staging_data.get('employees', [])
        log_test_result(
            "Staging employees endpoint",
            True,
            f"Retrieved {len(staging_employees)} employees from staging endpoint"
        )
        
        # Compare employee lists
        main_employee_ids = set(emp.get('EmployeeID') for emp in main_employees if emp.get('EmployeeID'))
        staging_employee_ids = set(emp.get('value') for emp in staging_employees if emp.get('value'))
        
        # Check if filtering was applied
        total_before_filtering = staging_data.get('total_employees_before_filtering', len(staging_employees))
        total_after_filtering = len(staging_employees)
        filtering_applied = staging_data.get('filtering_applied', False)
        
        log_test_result(
            "Employee filtering applied",
            filtering_applied,
            f"Before: {total_before_filtering}, After: {total_after_filtering}, Filtered: {total_before_filtering - total_after_filtering}"
        )
        
        # Check for synchronization (allowing for some differences due to filtering)
        common_employees = main_employee_ids.intersection(staging_employee_ids)
        sync_ratio = len(common_employees) / max(len(main_employee_ids), 1) if main_employee_ids else 0
        
        # Consider 80% or higher as good synchronization
        sync_threshold = 0.8
        sync_passed = sync_ratio >= sync_threshold
        
        log_test_result(
            "Employee list synchronization",
            sync_passed,
            f"Synchronization ratio: {sync_ratio:.2%} ({len(common_employees)}/{len(main_employee_ids)} employees)"
        )
        
        if not sync_passed:
            # Show some examples of differences
            main_only = main_employee_ids - staging_employee_ids
            staging_only = staging_employee_ids - main_employee_ids
            
            if main_only:
                print(f"    Employees in main but not staging: {list(main_only)[:5]}")
            if staging_only:
                print(f"    Employees in staging but not main: {list(staging_only)[:5]}")
        
        return sync_passed
        
    except Exception as e:
        log_test_result(
            "Employee synchronization test",
            False,
            f"Connection error: {str(e)}"
        )
        return False

def test_ptrj_employee_id_integration():
    """Test Issue 3: PTRJ Employee ID integration in staging."""
    print("=" * 60)
    print("TESTING ISSUE 3: PTRJ EMPLOYEE ID INTEGRATION")
    print("=" * 60)
    
    try:
        # Test selective copy functionality with PTRJ Employee ID
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        # Get employees first to select a few for testing
        staging_response = requests.get(f"{BASE_URL}/api/staging/employees", params={'bus_code': BUS_CODE})
        
        if staging_response.status_code != 200:
            log_test_result(
                "Employee retrieval for PTRJ test",
                False,
                f"HTTP error: {staging_response.status_code}"
            )
            return False
            
        staging_data = staging_response.json()
        if not staging_data.get('success'):
            log_test_result(
                "Employee retrieval for PTRJ test",
                False,
                f"API error: {staging_data.get('error', 'Unknown error')}"
            )
            return False
            
        employees = staging_data.get('employees', [])
        if len(employees) == 0:
            log_test_result(
                "Employee retrieval for PTRJ test",
                False,
                "No employees available for testing"
            )
            return False
            
        # Select first 2 employees for testing
        test_employee_ids = [emp['value'] for emp in employees[:2]]
        
        log_test_result(
            "Employee retrieval for PTRJ test",
            True,
            f"Selected {len(test_employee_ids)} employees for testing: {test_employee_ids}"
        )
        
        # Test selective copy with PTRJ Employee ID integration
        copy_data = {
            'employee_ids': test_employee_ids,
            'start_date': start_date,
            'end_date': end_date,
            'bus_code': BUS_CODE
        }
        
        copy_response = requests.post(
            f"{BASE_URL}/api/staging/selective-copy",
            json=copy_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if copy_response.status_code != 200:
            log_test_result(
                "Selective copy request",
                False,
                f"HTTP error: {copy_response.status_code}"
            )
            return False
            
        copy_result = copy_response.json()
        if not copy_result.get('success'):
            log_test_result(
                "Selective copy request",
                False,
                f"API error: {copy_result.get('error', 'Unknown error')}"
            )
            return False
            
        # Check copy results
        new_records = copy_result.get('new_records', 0)
        updated_records = copy_result.get('updated_records', 0)
        total_processed = copy_result.get('total_processed', 0)
        
        log_test_result(
            "Selective copy execution",
            total_processed > 0,
            f"Processed {total_processed} records ({new_records} new, {updated_records} updated)"
        )
        
        # Check if staging data now includes PTRJ Employee IDs
        staging_data_response = requests.get(f"{BASE_URL}/api/staging/data", params={
            'limit': 100,
            'status': 'staged'
        })
        
        if staging_data_response.status_code != 200:
            log_test_result(
                "Staging data verification",
                False,
                f"HTTP error: {staging_data_response.status_code}"
            )
            return False
            
        staging_data_result = staging_data_response.json()
        if not staging_data_result.get('success'):
            log_test_result(
                "Staging data verification",
                False,
                f"API error: {staging_data_result.get('error', 'Unknown error')}"
            )
            return False
            
        staging_records = staging_data_result.get('data', [])
        
        # Check PTRJ Employee ID presence
        ptrj_id_present = 0
        ptrj_id_with_values = 0
        
        for record in staging_records:
            if 'ptrj_employee_id' in record:
                ptrj_id_present += 1
                if record['ptrj_employee_id'] and record['ptrj_employee_id'] != "N/A":
                    ptrj_id_with_values += 1
        
        if len(staging_records) > 0:
            ptrj_coverage = ptrj_id_present / len(staging_records)
            ptrj_value_ratio = ptrj_id_with_values / len(staging_records) if len(staging_records) > 0 else 0
            
            log_test_result(
                "PTRJ Employee ID field presence",
                ptrj_coverage >= 0.9,  # 90% or more records should have the field
                f"Field present in {ptrj_coverage:.1%} of records ({ptrj_id_present}/{len(staging_records)})"
            )
            
            log_test_result(
                "PTRJ Employee ID value population",
                ptrj_value_ratio > 0,  # At least some records should have actual values
                f"Actual values in {ptrj_value_ratio:.1%} of records ({ptrj_id_with_values}/{len(staging_records)})"
            )
            
            # Show sample PTRJ Employee ID values
            sample_records = [r for r in staging_records[:5] if 'ptrj_employee_id' in r]
            if sample_records:
                print("    Sample PTRJ Employee ID mappings:")
                for record in sample_records:
                    emp_name = record.get('employee_name', 'Unknown')
                    ptrj_id = record.get('ptrj_employee_id', 'Missing')
                    print(f"      {emp_name} → {ptrj_id}")
                print()
            
            return ptrj_coverage >= 0.9 and ptrj_value_ratio > 0
        else:
            log_test_result(
                "PTRJ Employee ID integration",
                False,
                "No staging records found for verification"
            )
            return False
            
    except Exception as e:
        log_test_result(
            "PTRJ Employee ID integration test",
            False,
            f"Connection error: {str(e)}"
        )
        return False

def test_api_endpoints_health():
    """Test general API health and connectivity."""
    print("=" * 60)
    print("TESTING API ENDPOINTS HEALTH")
    print("=" * 60)
    
    endpoints_to_test = [
        ("/api/attendance", "Main attendance data"),
        ("/api/employees", "Employee list"),
        ("/api/staging/employees", "Staging employee list"),
        ("/api/staging/data", "Staging data"),
        ("/api/staging/stats", "Staging statistics"),
        ("/api/database/status", "Database status")
    ]
    
    passed_endpoints = 0
    total_endpoints = len(endpoints_to_test)
    
    for endpoint, description in endpoints_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            if response.status_code == 200:
                try:
                    data = response.json()
                    # Most endpoints should return success: true
                    if data.get('success', True):  # Default to True for endpoints that don't use success field
                        log_test_result(
                            f"{description} endpoint",
                            True,
                            f"HTTP 200, Valid JSON response"
                        )
                        passed_endpoints += 1
                    else:
                        log_test_result(
                            f"{description} endpoint",
                            False,
                            f"HTTP 200 but API error: {data.get('error', 'Unknown error')}"
                        )
                except json.JSONDecodeError:
                    log_test_result(
                        f"{description} endpoint",
                        False,
                        "HTTP 200 but invalid JSON response"
                    )
            else:
                log_test_result(
                    f"{description} endpoint",
                    False,
                    f"HTTP {response.status_code}"
                )
        except requests.exceptions.RequestException as e:
            log_test_result(
                f"{description} endpoint",
                False,
                f"Connection error: {str(e)}"
            )
    
    overall_health = passed_endpoints / total_endpoints
    log_test_result(
        "Overall API health",
        overall_health >= 0.8,  # 80% of endpoints should be working
        f"{passed_endpoints}/{total_endpoints} endpoints working ({overall_health:.1%})"
    )
    
    return overall_health >= 0.8

def main():
    """Run comprehensive tests for all attendance selection fixes."""
    print("🧪 COMPREHENSIVE ATTENDANCE SELECTION FUNCTIONALITY TEST")
    print("=" * 80)
    print(f"Testing against: {BASE_URL}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Track overall results
    test_results = {}
    
    # Test 1: API Health Check
    test_results['api_health'] = test_api_endpoints_health()
    
    # Test 2: Checkbox Functionality (Issue 1)
    checkbox_passed, record_count = test_checkbox_functionality()
    test_results['checkbox_functionality'] = checkbox_passed
    
    # Test 3: Employee Synchronization (Issue 2)
    test_results['employee_synchronization'] = test_employee_synchronization()
    
    # Test 4: PTRJ Employee ID Integration (Issue 3)
    test_results['ptrj_integration'] = test_ptrj_employee_id_integration()
    
    # Summary
    print("=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    print()
    overall_success = passed_tests / total_tests
    print(f"Overall Result: {passed_tests}/{total_tests} tests passed ({overall_success:.1%})")
    
    if overall_success >= 0.75:  # 75% success rate
        print("🎉 Most functionality is working correctly!")
        exit_code = 0
    else:
        print("⚠️  Some issues need attention.")
        exit_code = 1
    
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Frontend testing instructions
    print("\n" + "=" * 80)
    print("MANUAL FRONTEND TESTING REQUIRED")
    print("=" * 80)
    print("To complete the testing, please verify the following in your browser:")
    print()
    print("1. CHECKBOX VISIBILITY (Issue 1):")
    print("   - Open the web application")
    print("   - Load attendance data using 'Buat Laporan'")
    print("   - Click 'Selection Mode' button")
    print("   - Verify checkboxes appear next to row numbers")
    print("   - Test 'Pilih Semua' checkbox functionality")
    print()
    print("2. EMPLOYEE LIST SYNCHRONIZATION (Issue 2):")
    print("   - After loading main report, go to Staging tab")
    print("   - Click 'Load Employees' in Selective Copy section")
    print("   - Verify employee list matches main report")
    print("   - Look for auto-sync notification")
    print()
    print("3. PTRJ EMPLOYEE ID (Issue 3):")
    print("   - In Staging tab, select employees and copy to staging")
    print("   - Check staging data table for PTRJ Employee ID column")
    print("   - Verify actual PTRJ IDs appear (not 'N/A')")
    print()
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main() 