"""
Main module for the Attendance Report generator.
Provides a simple command-line interface for generating attendance reports.
"""

import os
import sys
import argparse
from datetime import datetime, date
from attendance_reporter import AttendanceReporter

def parse_date(date_str):
    """Parse a date string in YYYY-MM-DD format."""
    try:
        return datetime.strptime(date_str, "%Y-%m-%d").date()
    except ValueError:
        raise argparse.ArgumentTypeError("Invalid date format. Use YYYY-MM-DD")

def main():
    """Main entry point for the attendance report generator."""
    parser = argparse.ArgumentParser(description="Generate attendance reports from Venus database")
    
    # Define report types as mutually exclusive group
    report_type = parser.add_mutually_exclusive_group(required=True)
    report_type.add_argument("--daily", type=parse_date, help="Generate daily report for specified date (YYYY-MM-DD)")
    report_type.add_argument("--monthly", action="store_true", help="Generate monthly report")
    report_type.add_argument("--range", action="store_true", help="Generate report for a date range")
    
    # Optional arguments
    parser.add_argument("--year", type=int, help="Year for monthly report")
    parser.add_argument("--month", type=int, choices=range(1, 13), help="Month for monthly report (1-12)")
    parser.add_argument("--start-date", type=parse_date, help="Start date for range report (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=parse_date, help="End date for range report (YYYY-MM-DD)")
    parser.add_argument("--bus-code", help="Business code filter")
    
    args = parser.parse_args()
    
    reporter = AttendanceReporter()
    
    try:
        if args.daily:
            date_str = args.daily.strftime("%Y-%m-%d")
            print(f"Generating daily report for {date_str}")
            report_path = reporter.generate_daily_report(date_str, args.bus_code)
        elif args.monthly:
            # Default to current month if not specified
            year = args.year or datetime.now().year
            month = args.month or datetime.now().month
            
            print(f"Generating monthly report for {year}-{month:02d}")
            report_path = reporter.generate_monthly_report(year, month, args.bus_code)
        elif args.range:
            if not args.start_date or not args.end_date:
                parser.error("Both --start-date and --end-date are required for range reports")
                
            start_str = args.start_date.strftime("%Y-%m-%d")
            end_str = args.end_date.strftime("%Y-%m-%d")
            
            print(f"Generating report for date range {start_str} to {end_str}")
            report_path = reporter.generate_date_range_report(start_str, end_str, args.bus_code)
            
        if report_path:
            print(f"Report generated successfully: {os.path.abspath(report_path)}")
        else:
            print("No data found for the specified criteria. Report was not generated.")
            
    except Exception as e:
        print(f"Error generating report: {str(e)}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main()) 