"""
Staging Controller for Attendance Report System.
Handles all staging-related API endpoints.
Implements Interface Segregation Principle.
"""

from flask import Blueprint, request
from typing import Dict, Any

from api.api_utils import (
    create_success_response, create_error_response, create_paginated_response,
    validate_json_request, log_api_request, parse_pagination_params,
    get_user_context, validate_filters, handle_database_error
)


class StagingController:
    """
    Controller for staging-related API endpoints.
    Implements Interface Segregation - only handles staging operations.
    """
    
    def __init__(self, staging_service, logging_manager):
        """
        Initialize staging controller.
        
        Args:
            staging_service: Staging service instance
            logging_manager: Logging manager instance
        """
        self.staging_service = staging_service
        self.logger = logging_manager.get_logger(__name__)
        
        # Create Flask blueprint
        self.blueprint = Blueprint('staging', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """Register all staging-related routes."""
        
        @self.blueprint.route('/data', methods=['GET'])
        @log_api_request(self.logger)
        def get_staging_data():
            """Get staging data with filters and pagination."""
            try:
                # Parse pagination parameters
                page, per_page = parse_pagination_params(default_per_page=100, max_per_page=1000)
                offset = (page - 1) * per_page
                
                # Parse filters
                allowed_filters = ['start_date', 'end_date', 'employee_id', 'status', 'optimize', 'enhance_with_charge_jobs']
                filters = validate_filters(allowed_filters)
                
                # Add pagination to filters
                filters.update({
                    'limit': per_page,
                    'offset': offset
                })
                
                # Get staging data
                result = self.staging_service.get_staging_data(filters)
                
                if result['success']:
                    return create_paginated_response(
                        data=result['data'],
                        total_count=result['pagination']['total_count'],
                        page=page,
                        per_page=per_page,
                        filters_applied=result['filters_applied']
                    )
                else:
                    return create_error_response(result.get('error', 'Failed to get staging data'), 500)
                
            except Exception as e:
                self.logger.error(f"Error getting staging data: {str(e)}")
                return handle_database_error(e, "getting staging data")
        
        @self.blueprint.route('/data-grouped', methods=['GET'])
        @log_api_request(self.logger)
        def get_staging_data_grouped():
            """Get staging data with optimized structure by default."""
            try:
                # Parse filters
                allowed_filters = ['start_date', 'end_date', 'employee_id', 'status']
                filters = validate_filters(allowed_filters)
                
                # Force optimization
                filters['optimize'] = True
                filters['enhance_with_charge_jobs'] = True
                
                # Get staging data
                result = self.staging_service.get_staging_data(filters)
                
                if result['success']:
                    return create_success_response(
                        data=result['data'],
                        message="Staging data retrieved successfully (grouped format)",
                        filters_applied=result['filters_applied']
                    )
                else:
                    return create_error_response(result.get('error', 'Failed to get grouped staging data'), 500)
                
            except Exception as e:
                self.logger.error(f"Error getting grouped staging data: {str(e)}")
                return handle_database_error(e, "getting grouped staging data")
        
        @self.blueprint.route('/data', methods=['POST'])
        @log_api_request(self.logger)
        @validate_json_request(['records'])
        def add_staging_data():
            """Add new staging data."""
            try:
                data = request.get_json()
                records = data.get('records', [])
                
                if not records:
                    return create_error_response("No records provided", 400)
                
                # Add records to staging
                result = self.staging_service.add_staging_records(records)
                
                if result['success']:
                    return create_success_response(
                        data={
                            'added_count': result['added_count'],
                            'failed_count': result.get('failed_count', 0),
                            'total_errors': result.get('total_errors', 0)
                        },
                        message=f"Successfully added {result['added_count']} records to staging",
                        status_code=201
                    )
                else:
                    return create_error_response(
                        result.get('error', 'Failed to add staging records'),
                        400,
                        error_details=result.get('errors', [])
                    )
                
            except Exception as e:
                self.logger.error(f"Error adding staging data: {str(e)}")
                return handle_database_error(e, "adding staging data")
        
        @self.blueprint.route('/data/<staging_id>', methods=['PUT'])
        @log_api_request(self.logger)
        @validate_json_request()
        def update_staging_record(staging_id):
            """Update an existing staging record."""
            try:
                data = request.get_json()
                
                # Update staging record
                success = self.staging_service.update_staging_record(staging_id, data)
                
                if success:
                    return create_success_response(
                        message=f"Staging record {staging_id} updated successfully"
                    )
                else:
                    return create_error_response(f"Failed to update staging record {staging_id}", 404)
                
            except Exception as e:
                self.logger.error(f"Error updating staging record {staging_id}: {str(e)}")
                return handle_database_error(e, f"updating staging record {staging_id}")
        
        @self.blueprint.route('/data/<staging_id>', methods=['DELETE'])
        @log_api_request(self.logger)
        def delete_staging_record(staging_id):
            """Delete a staging record."""
            try:
                success = self.staging_service.delete_staging_record(staging_id)
                
                if success:
                    return create_success_response(
                        message=f"Staging record {staging_id} deleted successfully"
                    )
                else:
                    return create_error_response(f"Failed to delete staging record {staging_id}", 404)
                
            except Exception as e:
                self.logger.error(f"Error deleting staging record {staging_id}: {str(e)}")
                return handle_database_error(e, f"deleting staging record {staging_id}")
        
        @self.blueprint.route('/move-to-staging', methods=['POST'])
        @log_api_request(self.logger)
        @validate_json_request(['attendance_data'])
        def move_to_staging():
            """Move attendance records to staging."""
            try:
                data = request.get_json()
                attendance_data = data.get('attendance_data', [])
                
                if not attendance_data:
                    return create_error_response("No attendance data provided", 400)
                
                # Move to staging
                result = self.staging_service.move_to_staging(attendance_data)
                
                if result['success']:
                    return create_success_response(
                        data={
                            'moved_count': result['moved_count'],
                            'failed_count': result.get('failed_count', 0)
                        },
                        message=f"Successfully moved {result['moved_count']} records to staging",
                        status_code=201
                    )
                else:
                    return create_error_response(
                        result.get('error', 'Failed to move records to staging'),
                        400
                    )
                
            except Exception as e:
                self.logger.error(f"Error moving records to staging: {str(e)}")
                return handle_database_error(e, "moving records to staging")
        
        @self.blueprint.route('/stats', methods=['GET'])
        @log_api_request(self.logger)
        def get_staging_stats():
            """Get staging statistics."""
            try:
                stats = self.staging_service.get_staging_statistics()
                
                return create_success_response(
                    data=stats,
                    message="Staging statistics retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting staging statistics: {str(e)}")
                return create_error_response(f"Failed to get staging statistics: {str(e)}", 500)
        
        @self.blueprint.route('/health', methods=['GET'])
        @log_api_request(self.logger)
        def staging_health():
            """Get staging database health status."""
            try:
                health_status = self.staging_service.get_staging_health_status()
                
                return create_success_response(
                    data=health_status,
                    message="Staging health status retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting staging health status: {str(e)}")
                return create_error_response(f"Failed to get staging health status: {str(e)}", 500)
        
        @self.blueprint.route('/cleanup-duplicates', methods=['POST'])
        @log_api_request(self.logger)
        def cleanup_staging_duplicates():
            """Clean up duplicate records in staging database."""
            try:
                result = self.staging_service.cleanup_staging_duplicates()
                
                if result.get('cleanup_successful', False):
                    return create_success_response(
                        data={
                            'duplicates_found': result['duplicates_found'],
                            'records_removed': result['records_removed']
                        },
                        message=f"Cleanup completed: removed {result['records_removed']} duplicate records"
                    )
                else:
                    return create_error_response(
                        result.get('error', 'Cleanup failed'),
                        500
                    )
                
            except Exception as e:
                self.logger.error(f"Error during staging cleanup: {str(e)}")
                return handle_database_error(e, "staging cleanup")
        
        @self.blueprint.route('/delete-all', methods=['DELETE'])
        @log_api_request(self.logger)
        def delete_all_staging_data():
            """Delete all staging data (complete purge)."""
            try:
                # Get user context for logging
                user_context = get_user_context()
                
                # This is a dangerous operation, so we log it extensively
                self.logger.warning(f"STAGING_PURGE requested by {user_context['ip_address']}")
                
                # For now, return not implemented - this should be carefully implemented
                return create_error_response(
                    "Complete staging purge not implemented for safety reasons",
                    501,
                    "NOT_IMPLEMENTED"
                )
                
            except Exception as e:
                self.logger.error(f"Error during staging purge: {str(e)}")
                return create_error_response(f"Failed to purge staging data: {str(e)}", 500)
        
        @self.blueprint.route('/summary', methods=['GET'])
        @log_api_request(self.logger)
        def staging_summary():
            """Get comprehensive staging area summary."""
            try:
                stats = self.staging_service.get_staging_statistics()
                health = self.staging_service.get_staging_health_status()
                
                summary = {
                    'statistics': stats,
                    'health': health,
                    'operational_status': 'healthy' if health.get('healthy', False) else 'degraded'
                }
                
                return create_success_response(
                    data=summary,
                    message="Staging summary retrieved successfully"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting staging summary: {str(e)}")
                return create_error_response(f"Failed to get staging summary: {str(e)}", 500)
