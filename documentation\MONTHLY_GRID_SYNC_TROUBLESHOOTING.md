# Panduan Troubleshooting: Mode Sinkronisasi Grid Bulanan

## Masalah: Checkbox tidak muncul di Mode Sinkronisasi

### Penyebab Umum & Solusi

#### 1. **Belum mengaktifkan "Enable grid sync functionality"**
**Gejala:** Tombol "Sync Mode" diklik tapi tidak ada perubahan
**Solusi:** 
- Pastikan checkbox "Enable grid sync functionality" sudah dicentang
- Checkbox ini ada di bagian "Monthly Grid Sync Configuration"

#### 2. **Belum memuat grid bulanan terlebih dahulu**
**Gejala:** Pesan error "Harap muat grid bulanan terlebih dahulu"
**Solusi:**
- Pergi ke tab "Monthly Reports"
- Pilih bulan dan tahun
- Klik "Load Monthly Grid" untuk memuat data
- Baru kemudian aktifkan Sync Mode

#### 3. **State variabel tidak terinisialisasi dengan benar**
**Gejala:** Checkbox tidak muncul meski sudah mengikuti langkah yang benar
**Solusi:**
- Gunakan tombol "🔍 Debug" untuk memeriksa status
- Lakukan refresh halaman jika diperlukan

### Langkah-langkah yang Benar:

1. **Muat Grid Bulanan**
   - Buka tab "Monthly Reports"
   - Pilih bulan/tahun yang diinginkan
   - Klik tombol untuk memuat grid

2. **Aktifkan Sync Functionality**
   - Centang checkbox "Enable grid sync functionality"
   - Panel sync akan muncul

3. **Aktifkan Sync Mode**
   - Klik tombol "Sync Mode"
   - Tombol akan berubah menjadi "Sync Mode ON"
   - Checkbox akan muncul di setiap baris karyawan

4. **Pilih Data**
   - Klik checkbox di header untuk select all
   - Atau pilih checkbox individual per karyawan
   - Info seleksi akan muncul

5. **Transfer ke Staging**
   - Pilih tipe staging (Local/Google Apps Script)
   - Klik "Transfer Selected to Staging"

### Debug Tools:

#### Tombol "🔍 Debug"
Tombol ini memberikan informasi real-time tentang:
- Status Sync Mode (ON/OFF)
- Ketersediaan data grid
- Status checkbox "Enable Sync"
- Jumlah checkbox yang ditemukan
- Jumlah baris tabel

#### Console Browser (F12)
Buka Developer Tools dan lihat console untuk log detail:
```javascript
=== SYNC MODE DEBUG ===
monthlyGridSyncModeActive: true
=== HEADER CHECKBOX CREATION ===
✅ Creating header checkbox column - sync mode is ACTIVE
=== ROW 0 CHECKBOX CREATION ===
✅ Creating checkbox for row 0
```

### Perbaikan yang Dilakukan:

1. **Validasi Prerequisite**
   - Sync mode hanya bisa diaktifkan jika "Enable sync" sudah dicentang
   - Grid data harus sudah dimuat terlebih dahulu

2. **Enhanced Debugging**
   - Logging yang lebih detail untuk setiap langkah
   - User-friendly debug messages
   - Type checking untuk variabel state

3. **Improved Error Messages**
   - Pesan error dalam bahasa Indonesia
   - Instruksi yang jelas untuk langkah selanjutnya

### Troubleshooting Lanjutan:

#### Jika masih tidak berfungsi:
1. **Refresh halaman** dan ulangi langkah-langkah
2. **Clear browser cache** jika diperlukan
3. **Periksa console browser** untuk error JavaScript
4. **Pastikan tidak ada conflict** dengan extensions browser

#### Verifikasi Berhasil:
- Checkbox muncul di kolom pertama setiap baris
- Header checkbox "All" muncul
- Tombol berubah menjadi "Sync Mode ON"
- Info seleksi muncul saat memilih baris

### Catatan Teknis:

- Variable `monthlyGridSyncModeActive` harus bernilai `true`
- Data grid harus tersimpan di `currentMonthlyGridData`
- Checkbox menggunakan class `.monthly-grid-row-checkbox`
- Header checkbox menggunakan ID `#selectAllMonthlyGrid`

### Contact Support:

Jika issue masih berlanjut setelah mengikuti panduan ini, berikan informasi:
1. Screenshot tampilan current state
2. Output dari tombol "🔍 Debug"
3. Console log browser (F12)
4. Langkah-langkah yang sudah dicoba 