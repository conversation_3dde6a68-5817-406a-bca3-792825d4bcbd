#!/usr/bin/env python3
"""
Test script to verify the new staging configuration functionality.
Tests both Local Staging and Google Apps Script Staging options.
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
FLASK_APP_URL = "http://localhost:5173"

def test_flask_app_running():
    """Test if the Flask app is running and accessible."""
    print("=" * 60)
    print("TESTING FLASK APPLICATION STARTUP")
    print("=" * 60)
    
    try:
        response = requests.get(FLASK_APP_URL, timeout=5)
        print(f"✅ Flask app is running on {FLASK_APP_URL}")
        print(f"Status Code: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print(f"❌ Flask app is not running on {FLASK_APP_URL}")
        print("Please start the Flask app with: python web_app.py")
        return False
    except Exception as e:
        print(f"❌ Error testing Flask app: {e}")
        return False

def test_staging_api_availability():
    """Test if staging API endpoints are available."""
    print("\n" + "=" * 60)
    print("TESTING STAGING API ENDPOINTS")
    print("=" * 60)
    
    endpoints = [
        '/api/staging/data',
        '/api/staging/stats',
        '/api/sync-to-spreadsheet'
    ]
    
    all_available = True
    
    for endpoint in endpoints:
        try:
            url = f"{FLASK_APP_URL}{endpoint}"
            response = requests.get(url, timeout=5)
            print(f"✅ {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
            all_available = False
    
    return all_available

def test_local_staging_functionality():
    """Test the local staging functionality."""
    print("\n" + "=" * 60)
    print("TESTING LOCAL STAGING FUNCTIONALITY")
    print("=" * 60)
    
    # Sample data for local staging test
    test_records = [
        {
            "employee_id": "PTRJ.TEST001",
            "employee_name": "Test Employee 1",
            "date": "2025-01-15",
            "day_of_week": "Wednesday",
            "shift": "Regular",
            "check_in": "08:00",
            "check_out": "17:00",
            "regular_hours": 8.0,
            "overtime_hours": 1.0,
            "task_code": "TSK001",
            "station_code": "STN001",
            "machine_code": "MCH001",
            "expense_code": "EXP001",
            "notes": "Test record for staging configuration"
        },
        {
            "employee_id": "PTRJ.TEST002",
            "employee_name": "Test Employee 2",
            "date": "2025-01-15",
            "day_of_week": "Wednesday",
            "shift": "Regular",
            "check_in": "08:00",
            "check_out": "16:30",
            "regular_hours": 7.5,
            "overtime_hours": 0.0,
            "task_code": "TSK002",
            "station_code": "STN002",
            "machine_code": "MCH002",
            "expense_code": "EXP002",
            "notes": "Test record for staging configuration"
        }
    ]
    
    try:
        # Test adding records to local staging
        response = requests.post(
            f"{FLASK_APP_URL}/api/staging/data",
            json={"records": test_records},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Local Staging POST Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Local staging successful!")
            print(f"   Added records: {result.get('added_records', 0)}")
            print(f"   Errors: {len(result.get('errors', []))}")
            
            # Test retrieving records from local staging
            get_response = requests.get(
                f"{FLASK_APP_URL}/api/staging/data",
                timeout=10
            )
            
            if get_response.status_code == 200:
                get_result = get_response.json()
                print(f"✅ Local staging retrieval successful!")
                print(f"   Total records in staging: {get_result.get('total_records', 0)}")
                return True
            else:
                print(f"❌ Failed to retrieve from local staging: {get_response.status_code}")
                return False
        else:
            print(f"❌ Local staging failed: {response.status_code}")
            try:
                error_result = response.json()
                print(f"   Error: {error_result.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Local staging test error: {e}")
        return False

def test_google_apps_script_staging():
    """Test Google Apps Script staging functionality."""
    print("\n" + "=" * 60)
    print("TESTING GOOGLE APPS SCRIPT STAGING FUNCTIONALITY")
    print("=" * 60)
    
    # Sample data for Google Apps Script staging test
    test_data = [
        {
            "no": 1,
            "employeeId": "PTRJ.TEST001",
            "employeeName": "Test Employee 1",
            "year": 2025,
            "month": 1,
            "monthName": "January",
            "daysInMonth": 31,
            "dailyHours": {
                "1": "(8) | (1)",
                "2": "(8) | (-)",
                "3": "(7.5) | (-)"
            },
            "taskCode": "TSK001",
            "machineCode": "MCH001",
            "expenseCode": "EXP001"
        }
    ]
    
    sync_payload = {
        "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
        "action": "sync_daily_grid_staging",
        "data": test_data
    }
    
    try:
        response = requests.post(
            f"{FLASK_APP_URL}/api/sync-to-spreadsheet",
            json=sync_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Google Apps Script Staging Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Google Apps Script staging successful!")
            print(f"   Success: {result.get('success')}")
            print(f"   Message: {result.get('message', 'No message')}")
            return True
        else:
            print(f"❌ Google Apps Script staging failed: {response.status_code}")
            try:
                error_result = response.json()
                print(f"   Error: {error_result.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Google Apps Script staging test error: {e}")
        return False

def test_staging_stats():
    """Test staging statistics functionality."""
    print("\n" + "=" * 60)
    print("TESTING STAGING STATISTICS")
    print("=" * 60)
    
    try:
        response = requests.get(f"{FLASK_APP_URL}/api/staging/stats", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Staging statistics retrieved successfully!")
            stats = result.get('stats', {})
            print(f"   Total records: {stats.get('total_records', 0)}")
            print(f"   Status counts: {stats.get('status_counts', {})}")
            print(f"   Date range: {stats.get('date_range', {})}")
            return True
        else:
            print(f"❌ Failed to get staging statistics: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Staging statistics test error: {e}")
        return False

def run_staging_configuration_tests():
    """Run all staging configuration tests."""
    print("Starting Staging Configuration Tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    tests = [
        ("Flask App Running", test_flask_app_running),
        ("Staging API Availability", test_staging_api_availability),
        ("Local Staging Functionality", test_local_staging_functionality),
        ("Google Apps Script Staging", test_google_apps_script_staging),
        ("Staging Statistics", test_staging_stats)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("STAGING CONFIGURATION TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{test_name}: {status}")
        if passed_test:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL STAGING CONFIGURATION TESTS PASSED!")
        print("The new staging configuration functionality is working correctly.")
        return True
    else:
        print(f"\n❌ {total - passed} TEST(S) FAILED")
        print("Please check the error messages above for troubleshooting.")
        return False

if __name__ == "__main__":
    success = run_staging_configuration_tests()
    sys.exit(0 if success else 1) 