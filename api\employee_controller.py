"""
Employee Controller for Attendance Report System.
Handles all employee-related API endpoints.
Implements Interface Segregation Principle.
"""

from flask import Blueprint, request
from typing import Dict, Any

from api.api_utils import (
    create_success_response, create_error_response,
    log_api_request, validate_filters, parse_date_range,
    handle_database_error
)


class EmployeeController:
    """
    Controller for employee-related API endpoints.
    Implements Interface Segregation - only handles employee operations.
    """
    
    def __init__(self, employee_service, logging_manager):
        """
        Initialize employee controller.
        
        Args:
            employee_service: Employee service instance
            logging_manager: Logging manager instance
        """
        self.employee_service = employee_service
        self.logger = logging_manager.get_logger(__name__)
        
        # Create Flask blueprint
        self.blueprint = Blueprint('employee', __name__)
        self._register_routes()
    
    def _register_routes(self):
        """Register all employee-related routes."""
        
        @self.blueprint.route('/employees', methods=['GET'])
        @log_api_request(self.logger)
        def get_employees_list():
            """Get list of employees with optional filters."""
            try:
                bus_code = request.args.get('bus_code')
                
                # Parse additional filters
                filters = {}
                if request.args.get('name_pattern'):
                    filters['employee_name_pattern'] = request.args.get('name_pattern')
                if request.args.get('exclude_inactive'):
                    filters['exclude_inactive'] = request.args.get('exclude_inactive').lower() == 'true'
                
                # Get employees list
                employees = self.employee_service.get_employees_list(bus_code, filters)
                
                return create_success_response(
                    data=employees,
                    message=f"Retrieved {len(employees)} employees",
                    bus_code=bus_code,
                    filters_applied=filters
                )
                
            except Exception as e:
                self.logger.error(f"Error getting employees list: {str(e)}")
                return handle_database_error(e, "getting employees list")
        
        @self.blueprint.route('/charge-jobs', methods=['GET'])
        @log_api_request(self.logger)
        def get_employee_charge_jobs():
            """Get employee charge job data from external API."""
            try:
                # Fetch charge job data
                success, charge_job_data = self.employee_service.get_employee_charge_jobs()
                
                if success:
                    return create_success_response(
                        data=charge_job_data,
                        message=f"Retrieved charge job data for {len(charge_job_data)} employees",
                        data_source="external_api"
                    )
                else:
                    return create_success_response(
                        data=charge_job_data,
                        message="Charge job data retrieved from fallback source",
                        data_source="fallback_cache",
                        warning="External API unavailable"
                    )
                
            except Exception as e:
                self.logger.error(f"Error getting charge job data: {str(e)}")
                return create_error_response(f"Failed to get charge job data: {str(e)}", 500)
        
        @self.blueprint.route('/shifts', methods=['GET'])
        @log_api_request(self.logger)
        def get_shifts_list():
            """Get list of available shifts."""
            try:
                bus_code = request.args.get('bus_code')
                
                # Get shifts list
                shifts = self.employee_service.get_shifts_list(bus_code)
                
                return create_success_response(
                    data=shifts,
                    message=f"Retrieved {len(shifts)} shifts",
                    bus_code=bus_code
                )
                
            except Exception as e:
                self.logger.error(f"Error getting shifts list: {str(e)}")
                return handle_database_error(e, "getting shifts list")
        
        @self.blueprint.route('/leave-data', methods=['GET'])
        @log_api_request(self.logger)
        def get_leave_data():
            """Get employee leave data for date range."""
            try:
                # Parse and validate date range
                start_date, end_date, error = parse_date_range(
                    request.args.get('start_date'),
                    request.args.get('end_date')
                )
                
                if error:
                    return create_error_response(error, 400)
                
                bus_code = request.args.get('bus_code')
                
                # Get leave data
                leave_data = self.employee_service.get_leave_data(start_date, end_date, bus_code)
                
                return create_success_response(
                    data=leave_data,
                    message=f"Retrieved {len(leave_data)} leave records",
                    date_range={'start': start_date, 'end': end_date},
                    bus_code=bus_code
                )
                
            except Exception as e:
                self.logger.error(f"Error getting leave data: {str(e)}")
                return handle_database_error(e, "getting leave data")
        
        @self.blueprint.route('/employees-enhanced', methods=['GET'])
        @log_api_request(self.logger)
        def get_enhanced_employees_list():
            """Get employees list enhanced with charge job information."""
            try:
                bus_code = request.args.get('bus_code')
                
                # Get base employees list
                employees = self.employee_service.get_employees_list(bus_code)
                
                if not employees:
                    return create_success_response(
                        data=[],
                        message="No employees found",
                        bus_code=bus_code
                    )
                
                # Enhance with charge job data
                enhanced_employees = self.employee_service.enhance_employee_data_with_charge_jobs(employees)
                
                # Count matched employees
                matched_count = sum(1 for emp in enhanced_employees if emp.get('ChargeJobMatched', False))
                
                return create_success_response(
                    data=enhanced_employees,
                    message=f"Retrieved {len(enhanced_employees)} employees, {matched_count} enhanced with charge jobs",
                    enhancement_stats={
                        'total_employees': len(enhanced_employees),
                        'charge_job_matched': matched_count,
                        'match_rate': round((matched_count / len(enhanced_employees)) * 100, 1) if enhanced_employees else 0
                    },
                    bus_code=bus_code
                )
                
            except Exception as e:
                self.logger.error(f"Error getting enhanced employees list: {str(e)}")
                return handle_database_error(e, "getting enhanced employees list")
        
        @self.blueprint.route('/charge-jobs/refresh', methods=['POST'])
        @log_api_request(self.logger)
        def refresh_charge_jobs():
            """Force refresh of charge job data from external API."""
            try:
                # Clear cache by setting it to None
                self.employee_service._charge_job_cache = None
                self.employee_service._cache_timestamp = None
                
                # Fetch fresh data
                success, charge_job_data = self.employee_service.get_employee_charge_jobs()
                
                if success:
                    return create_success_response(
                        data={
                            'refresh_successful': True,
                            'records_count': len(charge_job_data),
                            'data_source': 'external_api'
                        },
                        message="Charge job data refreshed successfully from external API"
                    )
                else:
                    return create_success_response(
                        data={
                            'refresh_successful': False,
                            'records_count': len(charge_job_data),
                            'data_source': 'fallback_cache'
                        },
                        message="Failed to refresh from external API, using cached data",
                        warning="External API unavailable"
                    )
                
            except Exception as e:
                self.logger.error(f"Error refreshing charge job data: {str(e)}")
                return create_error_response(f"Failed to refresh charge job data: {str(e)}", 500)
        
        @self.blueprint.route('/bus-codes', methods=['GET'])
        @log_api_request(self.logger)
        def get_bus_codes():
            """Get list of available bus codes."""
            try:
                # Query for distinct bus codes
                query = """
                    SELECT DISTINCT BusCode, COUNT(*) as employee_count
                    FROM HR_T_TAMachine_Summary 
                    WHERE BusCode IS NOT NULL AND BusCode != ''
                    GROUP BY BusCode 
                    ORDER BY employee_count DESC, BusCode
                """
                
                # Execute query through database manager
                bus_codes = self.database_manager.execute_query(query)
                
                return create_success_response(
                    data=bus_codes,
                    message=f"Retrieved {len(bus_codes)} bus codes"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting bus codes: {str(e)}")
                return handle_database_error(e, "getting bus codes")
        
        @self.blueprint.route('/employee/<employee_id>/details', methods=['GET'])
        @log_api_request(self.logger)
        def get_employee_details(employee_id):
            """Get detailed information for a specific employee."""
            try:
                # Get employee basic info
                query = """
                    SELECT DISTINCT EmployeeID, EmployeeName, BusCode
                    FROM HR_T_TAMachine_Summary 
                    WHERE EmployeeID = ?
                """
                
                employee_info = self.database_manager.execute_query(query, (employee_id,))
                
                if not employee_info:
                    return create_error_response(f"Employee {employee_id} not found", 404)
                
                employee = employee_info[0]
                
                # Enhance with charge job data
                enhanced_employees = self.employee_service.enhance_employee_data_with_charge_jobs([employee])
                enhanced_employee = enhanced_employees[0] if enhanced_employees else employee
                
                return create_success_response(
                    data=enhanced_employee,
                    message=f"Employee details retrieved for {employee_id}"
                )
                
            except Exception as e:
                self.logger.error(f"Error getting employee details for {employee_id}: {str(e)}")
                return handle_database_error(e, f"getting employee details for {employee_id}")
