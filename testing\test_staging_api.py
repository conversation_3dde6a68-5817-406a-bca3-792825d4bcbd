#!/usr/bin/env python3
"""
Test script to verify staging API functionality.
"""

import requests
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5173"

def test_staging_post():
    """Test posting data to staging API."""
    
    # Sample test data
    test_data = {
        "records": [
            {
                "employee_id": "TEST.001",
                "employee_name": "Test Employee 1",
                "date": "2025-05-01",
                "day_of_week": "Thu",
                "shift": "Regular",
                "check_in": "08:00",
                "check_out": "17:00",
                "regular_hours": 8,
                "overtime_hours": 0,
                "task_code": "TASK001",
                "station_code": "STATION001",
                "machine_code": "MACHINE001",
                "expense_code": "EXP001",
                "notes": "Test record 1"
            },
            {
                "employee_id": "TEST.002",
                "employee_name": "Test Employee 2",
                "date": "2025-05-01",
                "day_of_week": "Thu",
                "shift": "Regular",
                "check_in": "08:00",
                "check_out": "19:00",
                "regular_hours": 8,
                "overtime_hours": 3,
                "task_code": "TASK002",
                "station_code": "STATION002",
                "machine_code": "MACHINE002",
                "expense_code": "EXP002",
                "notes": "Test record 2 with overtime"
            }
        ]
    }
    
    try:
        logger.info("Testing POST to /api/staging/data")
        logger.info(f"Sending {len(test_data['records'])} test records")
        
        response = requests.post(
            f"{BASE_URL}/api/staging/data",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ POST request successful!")
            logger.info(f"Response: {json.dumps(result, indent=2)}")
            return True
        else:
            logger.error(f"❌ POST request failed with status {response.status_code}")
            logger.error(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during POST request: {e}")
        return False

def test_staging_get():
    """Test getting data from staging API."""
    
    try:
        logger.info("Testing GET from /api/staging/data")
        
        response = requests.get(
            f"{BASE_URL}/api/staging/data",
            timeout=30
        )
        
        logger.info(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ GET request successful!")
            logger.info(f"Total records: {result.get('total_records', 0)}")
            logger.info(f"Returned records: {result.get('returned_records', 0)}")
            logger.info(f"Successfully transferred: {result.get('successfully_transferred', 0)}")
            
            if result.get('data'):
                logger.info("Sample record:")
                logger.info(f"  {json.dumps(result['data'][0], indent=2)}")
            
            return True
        else:
            logger.error(f"❌ GET request failed with status {response.status_code}")
            logger.error(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during GET request: {e}")
        return False

def test_server_health():
    """Test if the server is running."""
    
    try:
        logger.info("Testing server health")
        
        response = requests.get(
            f"{BASE_URL}/api/debug",
            timeout=10
        )
        
        if response.status_code == 200:
            logger.info("✅ Server is running and responsive")
            return True
        else:
            logger.error(f"❌ Server health check failed with status {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Server health check failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting staging API tests...")
    
    # Test server health first
    if not test_server_health():
        logger.error("Server is not responding. Please start the web application first.")
        exit(1)
    
    # Test POST functionality
    logger.info("\n" + "="*50)
    post_success = test_staging_post()
    
    # Test GET functionality
    logger.info("\n" + "="*50)
    get_success = test_staging_get()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY:")
    logger.info(f"  POST test: {'✅ PASSED' if post_success else '❌ FAILED'}")
    logger.info(f"  GET test:  {'✅ PASSED' if get_success else '❌ FAILED'}")
    
    if post_success and get_success:
        logger.info("🎉 All staging API tests passed!")
        exit(0)
    else:
        logger.error("❌ Some tests failed. Check the logs above for details.")
        exit(1)
