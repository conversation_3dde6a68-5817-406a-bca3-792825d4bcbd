{"metadata": {"name": "Employee Exclusion List", "description": "List of employee names that should be excluded from automated HR absence data processing", "version": "1.0", "created_date": "2025-01-09", "last_updated": "2025-01-09", "source": "HR Management Directive", "author": "Venus AutoFill System"}, "exclusion_settings": {"enabled": true, "case_sensitive": false, "partial_match": true, "trim_whitespace": true, "allow_user_override": true, "require_confirmation": true}, "excluded_employees": ["Abu Dzar AL Ghiffari", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zulhary L <PERSON>bing", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Prima Widisono", "Rega Saputra", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "normalization_rules": {"remove_extra_spaces": true, "handle_abbreviations": [{"pattern": "Muh\\.", "replacement": "<PERSON>"}, {"pattern": "Drs\\.", "replacement": "Drs"}, {"pattern": "Dr\\.", "replacement": "Dr"}], "common_name_variations": [{"base": "<PERSON>", "variations": ["<PERSON><PERSON>.", "<PERSON><PERSON>", "<PERSON>."]}, {"base": "<PERSON>", "variations": ["<PERSON>.", "<PERSON>", "<PERSON><PERSON>"]}, {"base": "<PERSON>", "variations": ["<PERSON><PERSON>d", "A."]}]}, "validation": {"minimum_match_threshold": 0.85, "fuzzy_matching": false, "log_exclusions": true, "notification_required": true}}